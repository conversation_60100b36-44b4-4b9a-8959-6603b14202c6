"""
报告生成器

生成详细的回测报告，包括性能分析、风险分析等。
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any
import json
import logging
from pathlib import Path

from .performance_analyzer import PerformanceAnalyzer
from .risk_analyzer import RiskAnalyzer


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        self.performance_analyzer = PerformanceAnalyzer()
        self.risk_analyzer = RiskAnalyzer()
        self.logger = logging.getLogger(__name__)
    
    def generate_comprehensive_report(self, 
                                    portfolio_history: pd.DataFrame,
                                    trade_history: pd.DataFrame,
                                    strategy_stats: Dict[str, Any],
                                    risk_metrics: Dict[str, Any],
                                    benchmark_returns: Optional[pd.Series] = None) -> Dict[str, Any]:
        """生成综合回测报告"""
        
        report = {
            'report_metadata': {
                'generated_at': datetime.now(),
                'report_type': 'comprehensive_backtest_report',
                'version': '1.0'
            }
        }
        
        # 基本信息
        if not portfolio_history.empty:
            report['basic_info'] = {
                'start_date': portfolio_history.index[0],
                'end_date': portfolio_history.index[-1],
                'total_days': (portfolio_history.index[-1] - portfolio_history.index[0]).days,
                'initial_capital': portfolio_history['portfolio_value'].iloc[0],
                'final_value': portfolio_history['portfolio_value'].iloc[-1]
            }
        
        # 性能分析
        if not portfolio_history.empty:
            report['performance_analysis'] = self.performance_analyzer.analyze_portfolio_performance(portfolio_history)
            
            # 月度收益表
            monthly_returns = self.performance_analyzer.generate_monthly_returns(portfolio_history)
            if not monthly_returns.empty:
                report['monthly_returns'] = monthly_returns.to_dict()
            
            # 风险指标
            daily_returns = portfolio_history['portfolio_value'].pct_change().dropna()
            report['risk_metrics'] = self.performance_analyzer.calculate_risk_metrics(daily_returns)
        
        # 交易分析
        if not trade_history.empty:
            report['trade_analysis'] = self.performance_analyzer.analyze_trade_performance(trade_history)
        
        # 基准比较
        if benchmark_returns is not None and not portfolio_history.empty:
            portfolio_returns = portfolio_history['portfolio_value'].pct_change().dropna()
            report['benchmark_comparison'] = self.performance_analyzer.calculate_benchmark_comparison(
                portfolio_returns, benchmark_returns
            )
        
        # 风险分析
        report['risk_analysis'] = self.risk_analyzer.generate_risk_report(
            portfolio_history, trade_history
        )
        
        # 策略统计
        report['strategy_statistics'] = strategy_stats
        
        # 风险管理指标
        report['risk_management'] = risk_metrics
        
        return report
    
    def generate_summary_report(self, comprehensive_report: Dict[str, Any]) -> Dict[str, Any]:
        """生成摘要报告"""
        
        summary = {
            'report_type': 'summary_report',
            'generated_at': datetime.now()
        }
        
        # 提取关键指标
        if 'performance_analysis' in comprehensive_report:
            perf = comprehensive_report['performance_analysis']
            summary['key_metrics'] = {
                'total_return': perf.get('total_return', 0),
                'annualized_return': perf.get('annualized_return', 0),
                'sharpe_ratio': perf.get('sharpe_ratio', 0),
                'max_drawdown': perf.get('max_drawdown', 0),
                'win_rate': perf.get('win_rate', 0),
                'volatility': perf.get('volatility', 0)
            }
        
        # 交易摘要
        if 'trade_analysis' in comprehensive_report:
            trade = comprehensive_report['trade_analysis']
            summary['trading_summary'] = {
                'total_trades': trade.get('total_trades', 0),
                'total_commission': trade.get('total_commission', 0),
                'avg_trade_size': trade.get('avg_trade_size', 0)
            }
        
        # 风险摘要
        if 'risk_analysis' in comprehensive_report:
            risk = comprehensive_report['risk_analysis']
            if 'drawdown_analysis' in risk:
                dd_analysis = risk['drawdown_analysis']
                summary['risk_summary'] = {
                    'total_drawdown_periods': dd_analysis.get('total_drawdown_periods', 0),
                    'avg_drawdown': dd_analysis.get('avg_drawdown', 0),
                    'longest_drawdown_days': dd_analysis.get('longest_drawdown_period', {}).get('duration_days', 0)
                }
        
        # 评级
        summary['performance_rating'] = self._calculate_performance_rating(summary.get('key_metrics', {}))
        
        return summary
    
    def _calculate_performance_rating(self, metrics: Dict[str, float]) -> str:
        """计算性能评级"""
        score = 0
        
        # 收益率评分 (30%)
        annual_return = metrics.get('annualized_return', 0)
        if annual_return > 0.2:
            score += 30
        elif annual_return > 0.1:
            score += 20
        elif annual_return > 0.05:
            score += 10
        elif annual_return > 0:
            score += 5
        
        # 夏普比率评分 (25%)
        sharpe = metrics.get('sharpe_ratio', 0)
        if sharpe > 2:
            score += 25
        elif sharpe > 1.5:
            score += 20
        elif sharpe > 1:
            score += 15
        elif sharpe > 0.5:
            score += 10
        elif sharpe > 0:
            score += 5
        
        # 最大回撤评分 (25%)
        max_dd = metrics.get('max_drawdown', 1)
        if max_dd < 0.05:
            score += 25
        elif max_dd < 0.1:
            score += 20
        elif max_dd < 0.15:
            score += 15
        elif max_dd < 0.2:
            score += 10
        elif max_dd < 0.3:
            score += 5
        
        # 胜率评分 (20%)
        win_rate = metrics.get('win_rate', 0)
        if win_rate > 0.6:
            score += 20
        elif win_rate > 0.55:
            score += 15
        elif win_rate > 0.5:
            score += 10
        elif win_rate > 0.45:
            score += 5
        
        # 评级
        if score >= 80:
            return 'A+'
        elif score >= 70:
            return 'A'
        elif score >= 60:
            return 'B+'
        elif score >= 50:
            return 'B'
        elif score >= 40:
            return 'C+'
        elif score >= 30:
            return 'C'
        else:
            return 'D'
    
    def export_to_json(self, report: Dict[str, Any], filepath: str) -> bool:
        """导出报告为JSON格式"""
        try:
            # 处理不可序列化的对象
            serializable_report = self._make_serializable(report)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_report, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"报告已导出到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出报告失败: {e}")
            return False
    
    def _make_serializable(self, obj):
        """使对象可序列化"""
        if isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (pd.Timestamp, datetime)):
            return obj.isoformat()
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj
    
    def export_to_html(self, report: Dict[str, Any], filepath: str) -> bool:
        """导出报告为HTML格式"""
        try:
            html_content = self._generate_html_report(report)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML报告已导出到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出HTML报告失败: {e}")
            return False
    
    def _generate_html_report(self, report: Dict[str, Any]) -> str:
        """生成HTML报告"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>回测报告</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
                .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .metric { display: inline-block; margin: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 3px; }
                .positive { color: green; }
                .negative { color: red; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
        """
        
        # 标题
        html += f"""
        <div class="header">
            <h1>加密货币回测报告</h1>
            <p>生成时间: {report.get('report_metadata', {}).get('generated_at', 'N/A')}</p>
        </div>
        """
        
        # 基本信息
        if 'basic_info' in report:
            basic = report['basic_info']
            html += f"""
            <div class="section">
                <h2>基本信息</h2>
                <div class="metric">开始日期: {basic.get('start_date', 'N/A')}</div>
                <div class="metric">结束日期: {basic.get('end_date', 'N/A')}</div>
                <div class="metric">总天数: {basic.get('total_days', 'N/A')}</div>
                <div class="metric">初始资金: ${basic.get('initial_capital', 0):,.2f}</div>
                <div class="metric">最终价值: ${basic.get('final_value', 0):,.2f}</div>
            </div>
            """
        
        # 性能指标
        if 'performance_analysis' in report:
            perf = report['performance_analysis']
            total_return = perf.get('total_return', 0)
            return_class = 'positive' if total_return > 0 else 'negative'
            
            html += f"""
            <div class="section">
                <h2>性能指标</h2>
                <div class="metric">总收益率: <span class="{return_class}">{total_return:.2%}</span></div>
                <div class="metric">年化收益率: {perf.get('annualized_return', 0):.2%}</div>
                <div class="metric">夏普比率: {perf.get('sharpe_ratio', 0):.2f}</div>
                <div class="metric">最大回撤: <span class="negative">{perf.get('max_drawdown', 0):.2%}</span></div>
                <div class="metric">胜率: {perf.get('win_rate', 0):.2%}</div>
                <div class="metric">波动率: {perf.get('volatility', 0):.2%}</div>
            </div>
            """
        
        # 交易统计
        if 'trade_analysis' in report:
            trade = report['trade_analysis']
            html += f"""
            <div class="section">
                <h2>交易统计</h2>
                <div class="metric">总交易数: {trade.get('total_trades', 0)}</div>
                <div class="metric">买入交易: {trade.get('buy_trades', 0)}</div>
                <div class="metric">卖出交易: {trade.get('sell_trades', 0)}</div>
                <div class="metric">总手续费: ${trade.get('total_commission', 0):,.2f}</div>
                <div class="metric">平均交易规模: ${trade.get('avg_trade_size', 0):,.2f}</div>
            </div>
            """
        
        html += """
        </body>
        </html>
        """
        
        return html
    
    def generate_comparison_report(self, reports: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """生成策略比较报告"""
        comparison = {
            'report_type': 'strategy_comparison',
            'generated_at': datetime.now(),
            'strategies': list(reports.keys())
        }
        
        # 提取关键指标进行比较
        metrics_comparison = {}
        key_metrics = ['total_return', 'annualized_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']
        
        for metric in key_metrics:
            metrics_comparison[metric] = {}
            for strategy_name, report in reports.items():
                perf = report.get('performance_analysis', {})
                metrics_comparison[metric][strategy_name] = perf.get(metric, 0)
        
        comparison['metrics_comparison'] = metrics_comparison
        
        # 排名
        rankings = {}
        for metric in key_metrics:
            values = metrics_comparison[metric]
            if metric == 'max_drawdown':  # 回撤越小越好
                sorted_strategies = sorted(values.items(), key=lambda x: x[1])
            else:  # 其他指标越大越好
                sorted_strategies = sorted(values.items(), key=lambda x: x[1], reverse=True)
            
            rankings[metric] = [strategy for strategy, _ in sorted_strategies]
        
        comparison['rankings'] = rankings
        
        # 综合评分
        overall_scores = {}
        for strategy_name in reports.keys():
            score = 0
            for metric in key_metrics:
                rank = rankings[metric].index(strategy_name) + 1
                # 转换为分数（排名越高分数越高）
                score += (len(reports) - rank + 1) / len(reports)
            
            overall_scores[strategy_name] = score / len(key_metrics)
        
        comparison['overall_scores'] = overall_scores
        comparison['best_strategy'] = max(overall_scores.items(), key=lambda x: x[1])[0]
        
        return comparison
