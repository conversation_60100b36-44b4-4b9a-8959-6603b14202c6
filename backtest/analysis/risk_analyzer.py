"""
风险分析器

专门用于风险分析和评估的模块。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
from scipy import stats


class RiskAnalyzer:
    """风险分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_drawdown_periods(self, portfolio_values: pd.Series) -> Dict[str, Any]:
        """分析回撤期间"""
        if portfolio_values.empty:
            return {}
        
        # 计算累计最高点
        peak = portfolio_values.expanding().max()
        
        # 计算回撤
        drawdown = (portfolio_values - peak) / peak
        
        # 找出回撤期间
        drawdown_periods = []
        in_drawdown = False
        start_date = None
        peak_value = 0
        trough_value = 0
        
        for date, dd in drawdown.items():
            if dd < 0 and not in_drawdown:
                # 开始回撤
                in_drawdown = True
                start_date = date
                peak_value = peak[date]
                trough_value = portfolio_values[date]
            elif dd < 0 and in_drawdown:
                # 继续回撤
                if portfolio_values[date] < trough_value:
                    trough_value = portfolio_values[date]
            elif dd >= 0 and in_drawdown:
                # 回撤结束
                in_drawdown = False
                end_date = date
                duration = (end_date - start_date).days
                max_dd = (peak_value - trough_value) / peak_value
                
                drawdown_periods.append({
                    'start_date': start_date,
                    'end_date': end_date,
                    'duration_days': duration,
                    'peak_value': peak_value,
                    'trough_value': trough_value,
                    'max_drawdown': max_dd,
                    'recovery_date': end_date
                })
        
        # 如果最后还在回撤中
        if in_drawdown:
            duration = (portfolio_values.index[-1] - start_date).days
            max_dd = (peak_value - trough_value) / peak_value
            
            drawdown_periods.append({
                'start_date': start_date,
                'end_date': portfolio_values.index[-1],
                'duration_days': duration,
                'peak_value': peak_value,
                'trough_value': trough_value,
                'max_drawdown': max_dd,
                'recovery_date': None  # 尚未恢复
            })
        
        # 统计信息
        if drawdown_periods:
            max_drawdown_period = max(drawdown_periods, key=lambda x: x['max_drawdown'])
            longest_drawdown_period = max(drawdown_periods, key=lambda x: x['duration_days'])
            avg_drawdown = np.mean([dd['max_drawdown'] for dd in drawdown_periods])
            avg_duration = np.mean([dd['duration_days'] for dd in drawdown_periods])
        else:
            max_drawdown_period = None
            longest_drawdown_period = None
            avg_drawdown = 0
            avg_duration = 0
        
        return {
            'drawdown_periods': drawdown_periods,
            'total_drawdown_periods': len(drawdown_periods),
            'max_drawdown_period': max_drawdown_period,
            'longest_drawdown_period': longest_drawdown_period,
            'avg_drawdown': avg_drawdown,
            'avg_duration_days': avg_duration
        }
    
    def analyze_volatility_clustering(self, returns: pd.Series, window: int = 20) -> Dict[str, Any]:
        """分析波动率聚集"""
        if len(returns) < window * 2:
            return {}
        
        # 计算滚动波动率
        rolling_vol = returns.rolling(window=window).std()
        
        # 波动率的波动率
        vol_of_vol = rolling_vol.std()
        
        # 高波动率期间（超过均值+1个标准差）
        vol_threshold = rolling_vol.mean() + rolling_vol.std()
        high_vol_periods = rolling_vol > vol_threshold
        
        # 聚集度分析
        clustering_score = self._calculate_clustering_score(high_vol_periods)
        
        return {
            'avg_volatility': rolling_vol.mean(),
            'volatility_of_volatility': vol_of_vol,
            'high_volatility_threshold': vol_threshold,
            'high_volatility_periods': high_vol_periods.sum(),
            'clustering_score': clustering_score,
            'max_volatility': rolling_vol.max(),
            'min_volatility': rolling_vol.min()
        }
    
    def _calculate_clustering_score(self, binary_series: pd.Series) -> float:
        """计算聚集度分数"""
        if len(binary_series) == 0:
            return 0
        
        # 计算连续True的长度
        runs = []
        current_run = 0
        
        for value in binary_series:
            if value:
                current_run += 1
            else:
                if current_run > 0:
                    runs.append(current_run)
                    current_run = 0
        
        if current_run > 0:
            runs.append(current_run)
        
        if not runs:
            return 0
        
        # 聚集度 = 平均连续长度 / 总True比例
        avg_run_length = np.mean(runs)
        true_ratio = binary_series.sum() / len(binary_series)
        
        if true_ratio > 0:
            clustering_score = avg_run_length / true_ratio
        else:
            clustering_score = 0
        
        return clustering_score
    
    def analyze_tail_risk(self, returns: pd.Series) -> Dict[str, Any]:
        """分析尾部风险"""
        if returns.empty:
            return {}
        
        # 极值理论分析
        # 选择阈值（95%分位数）
        threshold = np.percentile(returns, 5)  # 左尾5%
        
        # 超过阈值的损失
        exceedances = returns[returns < threshold] - threshold
        
        if len(exceedances) == 0:
            return {'tail_risk_analysis': 'insufficient_data'}
        
        # 拟合广义帕累托分布
        try:
            # 使用scipy进行GPD拟合
            shape, loc, scale = stats.genpareto.fit(-exceedances, floc=0)
            
            # 计算极端VaR
            extreme_var_99 = threshold + scale / shape * ((len(returns) * 0.01) ** (-shape) - 1)
            extreme_var_999 = threshold + scale / shape * ((len(returns) * 0.001) ** (-shape) - 1)
            
        except Exception as e:
            self.logger.warning(f"GPD拟合失败: {e}")
            extreme_var_99 = np.percentile(returns, 1)
            extreme_var_999 = np.percentile(returns, 0.1)
            shape, scale = None, None
        
        # 尾部比率
        left_tail_ratio = len(returns[returns < np.percentile(returns, 5)]) / len(returns)
        right_tail_ratio = len(returns[returns > np.percentile(returns, 95)]) / len(returns)
        
        return {
            'threshold': threshold,
            'exceedances_count': len(exceedances),
            'gpd_shape': shape,
            'gpd_scale': scale,
            'extreme_var_99': abs(extreme_var_99),
            'extreme_var_999': abs(extreme_var_999),
            'left_tail_ratio': left_tail_ratio,
            'right_tail_ratio': right_tail_ratio,
            'tail_asymmetry': right_tail_ratio - left_tail_ratio
        }
    
    def analyze_correlation_risk(self, returns_dict: Dict[str, pd.Series]) -> Dict[str, Any]:
        """分析相关性风险"""
        if len(returns_dict) < 2:
            return {}
        
        # 创建收益率矩阵
        returns_df = pd.DataFrame(returns_dict)
        returns_df = returns_df.dropna()
        
        if returns_df.empty:
            return {}
        
        # 相关性矩阵
        correlation_matrix = returns_df.corr()
        
        # 平均相关性
        avg_correlation = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].mean()
        
        # 最大相关性
        max_correlation = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].max()
        
        # 最小相关性
        min_correlation = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].min()
        
        # 相关性稳定性（滚动相关性的标准差）
        rolling_corr_std = {}
        window = min(60, len(returns_df) // 3)
        
        if window > 10:
            for i, col1 in enumerate(returns_df.columns):
                for j, col2 in enumerate(returns_df.columns):
                    if i < j:
                        rolling_corr = returns_df[col1].rolling(window).corr(returns_df[col2])
                        rolling_corr_std[f"{col1}_{col2}"] = rolling_corr.std()
        
        # 主成分分析
        try:
            from sklearn.decomposition import PCA
            pca = PCA()
            pca.fit(returns_df)
            explained_variance_ratio = pca.explained_variance_ratio_
            first_pc_weight = explained_variance_ratio[0]
        except ImportError:
            explained_variance_ratio = None
            first_pc_weight = None
        
        return {
            'correlation_matrix': correlation_matrix.to_dict(),
            'avg_correlation': avg_correlation,
            'max_correlation': max_correlation,
            'min_correlation': min_correlation,
            'correlation_stability': rolling_corr_std,
            'first_pc_weight': first_pc_weight,
            'explained_variance_ratio': explained_variance_ratio.tolist() if explained_variance_ratio is not None else None
        }
    
    def analyze_liquidity_risk(self, trade_history: pd.DataFrame) -> Dict[str, Any]:
        """分析流动性风险"""
        if trade_history.empty:
            return {}
        
        # 交易量分析
        daily_volume = trade_history.groupby(trade_history['timestamp'].dt.date)['quantity'].sum()
        
        # 交易频率
        daily_trades = trade_history.groupby(trade_history['timestamp'].dt.date).size()
        
        # 平均交易规模
        avg_trade_size = trade_history['quantity'].mean()
        
        # 大额交易比例（超过平均值2倍）
        large_trades = trade_history[trade_history['quantity'] > avg_trade_size * 2]
        large_trade_ratio = len(large_trades) / len(trade_history)
        
        # 交易时间间隔
        trade_intervals = trade_history['timestamp'].diff().dt.total_seconds() / 3600  # 小时
        avg_interval = trade_intervals.mean()
        
        return {
            'avg_daily_volume': daily_volume.mean(),
            'volume_volatility': daily_volume.std(),
            'avg_daily_trades': daily_trades.mean(),
            'avg_trade_size': avg_trade_size,
            'large_trade_ratio': large_trade_ratio,
            'avg_trade_interval_hours': avg_interval,
            'max_trade_interval_hours': trade_intervals.max(),
            'liquidity_score': self._calculate_liquidity_score(daily_volume, daily_trades)
        }
    
    def _calculate_liquidity_score(self, daily_volume: pd.Series, daily_trades: pd.Series) -> float:
        """计算流动性分数"""
        if daily_volume.empty or daily_trades.empty:
            return 0
        
        # 标准化指标
        volume_score = 1 / (1 + daily_volume.std() / daily_volume.mean()) if daily_volume.mean() > 0 else 0
        frequency_score = daily_trades.mean() / 100  # 假设100笔/天为满分
        
        # 综合流动性分数
        liquidity_score = (volume_score + min(frequency_score, 1)) / 2
        
        return liquidity_score
    
    def generate_risk_report(self, portfolio_history: pd.DataFrame, 
                           trade_history: pd.DataFrame,
                           returns_dict: Optional[Dict[str, pd.Series]] = None) -> Dict[str, Any]:
        """生成综合风险报告"""
        report = {
            'timestamp': datetime.now(),
            'analysis_period': {
                'start': portfolio_history.index[0] if not portfolio_history.empty else None,
                'end': portfolio_history.index[-1] if not portfolio_history.empty else None
            }
        }
        
        # 回撤分析
        if not portfolio_history.empty:
            report['drawdown_analysis'] = self.analyze_drawdown_periods(portfolio_history['portfolio_value'])
            
            # 波动率分析
            daily_returns = portfolio_history['portfolio_value'].pct_change().dropna()
            report['volatility_analysis'] = self.analyze_volatility_clustering(daily_returns)
            
            # 尾部风险
            report['tail_risk_analysis'] = self.analyze_tail_risk(daily_returns)
        
        # 流动性风险
        if not trade_history.empty:
            report['liquidity_analysis'] = self.analyze_liquidity_risk(trade_history)
        
        # 相关性风险
        if returns_dict and len(returns_dict) > 1:
            report['correlation_analysis'] = self.analyze_correlation_risk(returns_dict)
        
        return report
