"""
图表生成器

生成各种回测相关的图表和可视化。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import seaborn as sns
from pathlib import Path
import logging

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")


class ChartGenerator:
    """图表生成器"""
    
    def __init__(self, output_dir: str = "charts"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # 图表样式配置
        self.colors = {
            'primary': '#1f77b4',
            'secondary': '#ff7f0e', 
            'success': '#2ca02c',
            'danger': '#d62728',
            'warning': '#ff7f0e',
            'info': '#17a2b8',
            'light': '#f8f9fa',
            'dark': '#343a40'
        }
    
    def generate_portfolio_chart(self, portfolio_history: pd.DataFrame, 
                               title: str = "投资组合价值变化") -> str:
        """生成投资组合价值图表"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 投资组合价值曲线
        ax1.plot(portfolio_history.index, portfolio_history['portfolio_value'], 
                color=self.colors['primary'], linewidth=2, label='投资组合价值')
        
        # 添加基准线（初始价值）
        initial_value = portfolio_history['portfolio_value'].iloc[0]
        ax1.axhline(y=initial_value, color=self.colors['secondary'], 
                   linestyle='--', alpha=0.7, label=f'初始价值: ${initial_value:,.0f}')
        
        ax1.set_title(title, fontsize=16, fontweight='bold')
        ax1.set_ylabel('投资组合价值 ($)', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 格式化x轴日期
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.MonthLocator())
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        
        # 日收益率
        daily_returns = portfolio_history['portfolio_value'].pct_change().dropna()
        ax2.plot(daily_returns.index, daily_returns * 100, 
                color=self.colors['info'], alpha=0.7, linewidth=1)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.set_title('日收益率 (%)', fontsize=14)
        ax2.set_ylabel('收益率 (%)', fontsize=12)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 格式化x轴日期
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_locator(mdates.MonthLocator())
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        filename = f"portfolio_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"投资组合图表已保存: {filepath}")
        return str(filepath)
    
    def generate_drawdown_chart(self, portfolio_history: pd.DataFrame,
                              title: str = "回撤分析") -> str:
        """生成回撤分析图表"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 计算回撤
        portfolio_values = portfolio_history['portfolio_value']
        peak = portfolio_values.expanding().max()
        drawdown = (portfolio_values - peak) / peak
        
        # 投资组合价值和峰值
        ax1.plot(portfolio_values.index, portfolio_values, 
                color=self.colors['primary'], linewidth=2, label='投资组合价值')
        ax1.plot(peak.index, peak, 
                color=self.colors['success'], linewidth=1, alpha=0.7, label='历史最高点')
        
        ax1.set_title(title, fontsize=16, fontweight='bold')
        ax1.set_ylabel('投资组合价值 ($)', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 回撤图
        ax2.fill_between(drawdown.index, drawdown * 100, 0, 
                        color=self.colors['danger'], alpha=0.3, label='回撤')
        ax2.plot(drawdown.index, drawdown * 100, 
                color=self.colors['danger'], linewidth=1)
        
        # 标记最大回撤点
        max_dd_idx = drawdown.idxmin()
        max_dd_value = drawdown.min() * 100
        ax2.scatter([max_dd_idx], [max_dd_value], 
                   color=self.colors['danger'], s=100, zorder=5)
        ax2.annotate(f'最大回撤: {max_dd_value:.2f}%', 
                    xy=(max_dd_idx, max_dd_value),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        ax2.set_title('回撤分析', fontsize=14)
        ax2.set_ylabel('回撤 (%)', fontsize=12)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 格式化x轴日期
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        filename = f"drawdown_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"回撤分析图表已保存: {filepath}")
        return str(filepath)
    
    def generate_performance_metrics_chart(self, metrics: Dict[str, float],
                                         title: str = "性能指标") -> str:
        """生成性能指标图表"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 收益率指标
        returns_metrics = {
            '总收益率': metrics.get('total_return', 0) * 100,
            '年化收益率': metrics.get('annualized_return', 0) * 100
        }
        
        bars1 = ax1.bar(returns_metrics.keys(), returns_metrics.values(), 
                       color=[self.colors['success'] if v > 0 else self.colors['danger'] 
                             for v in returns_metrics.values()])
        ax1.set_title('收益率指标', fontsize=14, fontweight='bold')
        ax1.set_ylabel('收益率 (%)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars1, returns_metrics.values()):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{value:.2f}%', ha='center', va='bottom', fontweight='bold')
        
        # 2. 风险指标
        risk_metrics = {
            '波动率': metrics.get('volatility', 0) * 100,
            '最大回撤': metrics.get('max_drawdown', 0) * 100
        }
        
        bars2 = ax2.bar(risk_metrics.keys(), risk_metrics.values(), 
                       color=self.colors['warning'])
        ax2.set_title('风险指标', fontsize=14, fontweight='bold')
        ax2.set_ylabel('百分比 (%)', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars2, risk_metrics.values()):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{value:.2f}%', ha='center', va='bottom', fontweight='bold')
        
        # 3. 风险调整收益指标
        ratio_metrics = {
            '夏普比率': metrics.get('sharpe_ratio', 0),
            'Sortino比率': metrics.get('sortino_ratio', 0),
            'Calmar比率': metrics.get('calmar_ratio', 0)
        }
        
        bars3 = ax3.bar(ratio_metrics.keys(), ratio_metrics.values(), 
                       color=self.colors['info'])
        ax3.set_title('风险调整收益指标', fontsize=14, fontweight='bold')
        ax3.set_ylabel('比率', fontsize=12)
        ax3.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars3, ratio_metrics.values()):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 4. 交易指标
        trade_metrics = {
            '胜率': metrics.get('win_rate', 0) * 100,
            '盈亏比': metrics.get('profit_loss_ratio', 0)
        }
        
        # 胜率用饼图
        ax4.pie([trade_metrics['胜率'], 100 - trade_metrics['胜率']], 
               labels=[f"胜率 {trade_metrics['胜率']:.1f}%", 
                      f"败率 {100 - trade_metrics['胜率']:.1f}%"],
               colors=[self.colors['success'], self.colors['danger']],
               autopct='%1.1f%%', startangle=90)
        ax4.set_title('胜率分析', fontsize=14, fontweight='bold')
        
        plt.suptitle(title, fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        
        # 保存图表
        filename = f"performance_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"性能指标图表已保存: {filepath}")
        return str(filepath)
    
    def generate_trade_analysis_chart(self, trade_history: pd.DataFrame,
                                    title: str = "交易分析") -> str:
        """生成交易分析图表"""
        if trade_history.empty:
            self.logger.warning("交易历史为空，无法生成交易分析图表")
            return ""
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 交易数量时间分布
        trade_history['date'] = pd.to_datetime(trade_history['timestamp']).dt.date
        daily_trades = trade_history.groupby('date').size()
        
        ax1.plot(daily_trades.index, daily_trades.values, 
                color=self.colors['primary'], marker='o', markersize=4)
        ax1.set_title('每日交易数量', fontsize=14, fontweight='bold')
        ax1.set_ylabel('交易数量', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # 2. 买卖交易分布
        direction_counts = trade_history['direction'].value_counts()
        ax2.pie(direction_counts.values, labels=direction_counts.index, 
               colors=[self.colors['success'], self.colors['danger']],
               autopct='%1.1f%%', startangle=90)
        ax2.set_title('买卖交易分布', fontsize=14, fontweight='bold')
        
        # 3. 交易规模分布
        ax3.hist(trade_history['quantity'], bins=30, 
                color=self.colors['info'], alpha=0.7, edgecolor='black')
        ax3.set_title('交易规模分布', fontsize=14, fontweight='bold')
        ax3.set_xlabel('交易数量', fontsize=12)
        ax3.set_ylabel('频次', fontsize=12)
        ax3.grid(True, alpha=0.3)
        
        # 4. 手续费分析
        if 'commission' in trade_history.columns:
            cumulative_commission = trade_history['commission'].cumsum()
            ax4.plot(range(len(cumulative_commission)), cumulative_commission, 
                    color=self.colors['warning'], linewidth=2)
            ax4.set_title('累计手续费', fontsize=14, fontweight='bold')
            ax4.set_xlabel('交易序号', fontsize=12)
            ax4.set_ylabel('累计手续费 ($)', fontsize=12)
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, '手续费数据不可用', ha='center', va='center', 
                    transform=ax4.transAxes, fontsize=14)
            ax4.set_title('手续费分析', fontsize=14, fontweight='bold')
        
        plt.suptitle(title, fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        
        # 保存图表
        filename = f"trade_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"交易分析图表已保存: {filepath}")
        return str(filepath)
    
    def generate_strategy_comparison_chart(self, comparison_data: Dict[str, Dict[str, float]],
                                         title: str = "策略比较") -> str:
        """生成策略比较图表"""
        strategies = list(comparison_data.keys())
        metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']
        metric_names = ['总收益率', '夏普比率', '最大回撤', '胜率']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            values = [comparison_data[strategy].get(metric, 0) for strategy in strategies]
            
            # 根据指标类型调整显示
            if metric in ['total_return', 'max_drawdown', 'win_rate']:
                values = [v * 100 for v in values]  # 转换为百分比
                ylabel = '百分比 (%)'
            else:
                ylabel = '比率'
            
            # 选择颜色
            if metric == 'max_drawdown':
                colors = [self.colors['danger'] if v > 10 else self.colors['warning'] for v in values]
            else:
                colors = [self.colors['success'] if v > 0 else self.colors['danger'] for v in values]
            
            bars = axes[i].bar(strategies, values, color=colors)
            axes[i].set_title(metric_name, fontsize=14, fontweight='bold')
            axes[i].set_ylabel(ylabel, fontsize=12)
            axes[i].grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(values) * 0.01,
                           f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
            
            # 旋转x轴标签
            plt.setp(axes[i].xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        plt.suptitle(title, fontsize=16, fontweight='bold', y=0.98)
        plt.tight_layout()
        
        # 保存图表
        filename = f"strategy_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"策略比较图表已保存: {filepath}")
        return str(filepath)
    
    def generate_comprehensive_dashboard(self, portfolio_history: pd.DataFrame,
                                       trade_history: pd.DataFrame,
                                       metrics: Dict[str, float],
                                       title: str = "回测仪表板") -> str:
        """生成综合仪表板"""
        fig = plt.figure(figsize=(20, 12))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # 1. 投资组合价值曲线 (占2列)
        ax1 = fig.add_subplot(gs[0, :2])
        ax1.plot(portfolio_history.index, portfolio_history['portfolio_value'], 
                color=self.colors['primary'], linewidth=2)
        ax1.set_title('投资组合价值变化', fontsize=14, fontweight='bold')
        ax1.set_ylabel('价值 ($)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # 2. 回撤图 (占2列)
        ax2 = fig.add_subplot(gs[0, 2:])
        portfolio_values = portfolio_history['portfolio_value']
        peak = portfolio_values.expanding().max()
        drawdown = (portfolio_values - peak) / peak
        ax2.fill_between(drawdown.index, drawdown * 100, 0, 
                        color=self.colors['danger'], alpha=0.3)
        ax2.set_title('回撤分析', fontsize=14, fontweight='bold')
        ax2.set_ylabel('回撤 (%)', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 3. 关键指标 (4个子图)
        key_metrics = [
            ('总收益率', metrics.get('total_return', 0) * 100, '%'),
            ('夏普比率', metrics.get('sharpe_ratio', 0), ''),
            ('最大回撤', metrics.get('max_drawdown', 0) * 100, '%'),
            ('胜率', metrics.get('win_rate', 0) * 100, '%')
        ]
        
        for i, (name, value, unit) in enumerate(key_metrics):
            ax = fig.add_subplot(gs[1, i])
            color = self.colors['success'] if value > 0 else self.colors['danger']
            if name == '最大回撤':
                color = self.colors['danger'] if value > 10 else self.colors['warning']
            
            ax.text(0.5, 0.5, f'{value:.2f}{unit}', ha='center', va='center',
                   fontsize=24, fontweight='bold', color=color, transform=ax.transAxes)
            ax.set_title(name, fontsize=12, fontweight='bold')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
        
        # 4. 交易分析
        if not trade_history.empty:
            # 每日交易数量
            ax5 = fig.add_subplot(gs[2, :2])
            trade_history['date'] = pd.to_datetime(trade_history['timestamp']).dt.date
            daily_trades = trade_history.groupby('date').size()
            ax5.bar(range(len(daily_trades)), daily_trades.values, 
                   color=self.colors['info'], alpha=0.7)
            ax5.set_title('每日交易数量', fontsize=14, fontweight='bold')
            ax5.set_ylabel('交易数量', fontsize=12)
            ax5.grid(True, alpha=0.3)
            
            # 买卖分布
            ax6 = fig.add_subplot(gs[2, 2])
            direction_counts = trade_history['direction'].value_counts()
            ax6.pie(direction_counts.values, labels=direction_counts.index,
                   colors=[self.colors['success'], self.colors['danger']],
                   autopct='%1.1f%%')
            ax6.set_title('买卖分布', fontsize=14, fontweight='bold')
            
            # 交易规模分布
            ax7 = fig.add_subplot(gs[2, 3])
            ax7.hist(trade_history['quantity'], bins=20, 
                    color=self.colors['primary'], alpha=0.7)
            ax7.set_title('交易规模分布', fontsize=14, fontweight='bold')
            ax7.set_xlabel('数量', fontsize=12)
            ax7.grid(True, alpha=0.3)
        
        plt.suptitle(title, fontsize=20, fontweight='bold', y=0.98)
        
        # 保存图表
        filename = f"dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"综合仪表板已保存: {filepath}")
        return str(filepath)
