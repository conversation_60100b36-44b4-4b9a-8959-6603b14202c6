"""
Web界面

提供基于Web的回测结果展示界面。
"""

import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging


class WebInterface:
    """Web界面生成器"""
    
    def __init__(self, output_dir: str = "web_output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
    
    def generate_html_dashboard(self, 
                              portfolio_history: pd.DataFrame,
                              trade_history: pd.DataFrame,
                              metrics: Dict[str, float],
                              strategy_name: str = "回测策略") -> str:
        """生成HTML仪表板"""
        
        # 准备数据
        portfolio_data = self._prepare_portfolio_data(portfolio_history)
        trade_data = self._prepare_trade_data(trade_history)
        
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{strategy_name} - 回测仪表板</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
            <style>
                {self._get_css_styles()}
            </style>
        </head>
        <body>
            <div class="container">
                <header class="header">
                    <h1>{strategy_name} - 回测仪表板</h1>
                    <p class="subtitle">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </header>
                
                <div class="metrics-grid">
                    {self._generate_metrics_cards(metrics)}
                </div>
                
                <div class="charts-grid">
                    <div class="chart-container">
                        <h3>投资组合价值变化</h3>
                        <canvas id="portfolioChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h3>回撤分析</h3>
                        <canvas id="drawdownChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h3>日收益率分布</h3>
                        <canvas id="returnsChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h3>交易分析</h3>
                        <canvas id="tradesChart"></canvas>
                    </div>
                </div>
                
                <div class="data-tables">
                    <div class="table-container">
                        <h3>交易记录</h3>
                        <div class="table-wrapper">
                            {self._generate_trade_table(trade_history)}
                        </div>
                    </div>
                </div>
            </div>
            
            <script>
                {self._generate_javascript(portfolio_data, trade_data, metrics)}
            </script>
        </body>
        </html>
        """
        
        # 保存HTML文件
        filename = f"dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"HTML仪表板已保存: {filepath}")
        return str(filepath)
    
    def _prepare_portfolio_data(self, portfolio_history: pd.DataFrame) -> Dict:
        """准备投资组合数据"""
        if portfolio_history.empty:
            return {'labels': [], 'values': [], 'returns': []}
        
        # 转换时间戳为字符串
        labels = [ts.strftime('%Y-%m-%d') for ts in portfolio_history.index]
        values = portfolio_history['portfolio_value'].tolist()
        
        # 计算日收益率
        returns = portfolio_history['portfolio_value'].pct_change().fillna(0).tolist()
        
        # 计算回撤
        peak = portfolio_history['portfolio_value'].expanding().max()
        drawdown = ((portfolio_history['portfolio_value'] - peak) / peak * 100).tolist()
        
        return {
            'labels': labels,
            'values': values,
            'returns': returns,
            'drawdown': drawdown
        }
    
    def _prepare_trade_data(self, trade_history: pd.DataFrame) -> Dict:
        """准备交易数据"""
        if trade_history.empty:
            return {'buy_count': 0, 'sell_count': 0, 'daily_trades': []}
        
        # 买卖统计
        buy_count = len(trade_history[trade_history['direction'] == 'BUY'])
        sell_count = len(trade_history[trade_history['direction'] == 'SELL'])
        
        # 每日交易数量
        trade_history['date'] = pd.to_datetime(trade_history['timestamp']).dt.date
        daily_trades = trade_history.groupby('date').size().tolist()
        
        return {
            'buy_count': buy_count,
            'sell_count': sell_count,
            'daily_trades': daily_trades
        }
    
    def _get_css_styles(self) -> str:
        """获取CSS样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.2s;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .neutral { color: #6c757d; }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .chart-container h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .data-tables {
            margin-top: 30px;
        }
        
        .table-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .table-wrapper {
            max-height: 400px;
            overflow-y: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .buy { color: #28a745; font-weight: bold; }
        .sell { color: #dc3545; font-weight: bold; }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
        """
    
    def _generate_metrics_cards(self, metrics: Dict[str, float]) -> str:
        """生成指标卡片HTML"""
        cards = []
        
        metric_configs = [
            ('total_return', '总收益率', '%', 100),
            ('annualized_return', '年化收益率', '%', 100),
            ('sharpe_ratio', '夏普比率', '', 1),
            ('max_drawdown', '最大回撤', '%', 100),
            ('win_rate', '胜率', '%', 100),
            ('volatility', '波动率', '%', 100)
        ]
        
        for key, label, unit, multiplier in metric_configs:
            value = metrics.get(key, 0) * multiplier
            
            # 确定颜色类
            if key == 'max_drawdown':
                color_class = 'negative' if value > 10 else 'neutral'
            elif key in ['total_return', 'annualized_return', 'sharpe_ratio', 'win_rate']:
                color_class = 'positive' if value > 0 else 'negative'
            else:
                color_class = 'neutral'
            
            cards.append(f"""
            <div class="metric-card">
                <div class="metric-value {color_class}">{value:.2f}{unit}</div>
                <div class="metric-label">{label}</div>
            </div>
            """)
        
        return ''.join(cards)
    
    def _generate_trade_table(self, trade_history: pd.DataFrame) -> str:
        """生成交易记录表格"""
        if trade_history.empty:
            return "<p>暂无交易记录</p>"
        
        # 只显示最近100条记录
        recent_trades = trade_history.tail(100)
        
        table_html = """
        <table>
            <thead>
                <tr>
                    <th>时间</th>
                    <th>交易对</th>
                    <th>方向</th>
                    <th>数量</th>
                    <th>价格</th>
                    <th>手续费</th>
                </tr>
            </thead>
            <tbody>
        """
        
        for _, row in recent_trades.iterrows():
            direction_class = 'buy' if row['direction'] == 'BUY' else 'sell'
            table_html += f"""
            <tr>
                <td>{row['timestamp']}</td>
                <td>{row.get('symbol', 'N/A')}</td>
                <td class="{direction_class}">{row['direction']}</td>
                <td>{row['quantity']:.6f}</td>
                <td>${row['price']:.2f}</td>
                <td>${row.get('commission', 0):.2f}</td>
            </tr>
            """
        
        table_html += """
            </tbody>
        </table>
        """
        
        return table_html
    
    def _generate_javascript(self, portfolio_data: Dict, trade_data: Dict, metrics: Dict) -> str:
        """生成JavaScript代码"""
        return f"""
        // 投资组合价值图表
        const portfolioCtx = document.getElementById('portfolioChart').getContext('2d');
        new Chart(portfolioCtx, {{
            type: 'line',
            data: {{
                labels: {json.dumps(portfolio_data['labels'])},
                datasets: [{{
                    label: '投资组合价值',
                    data: {json.dumps(portfolio_data['values'])},
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: false,
                        ticks: {{
                            callback: function(value) {{
                                return '$' + value.toLocaleString();
                            }}
                        }}
                    }}
                }}
            }}
        }});
        
        // 回撤图表
        const drawdownCtx = document.getElementById('drawdownChart').getContext('2d');
        new Chart(drawdownCtx, {{
            type: 'line',
            data: {{
                labels: {json.dumps(portfolio_data['labels'])},
                datasets: [{{
                    label: '回撤 (%)',
                    data: {json.dumps(portfolio_data['drawdown'])},
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 2,
                    fill: true
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        max: 0,
                        ticks: {{
                            callback: function(value) {{
                                return value.toFixed(2) + '%';
                            }}
                        }}
                    }}
                }}
            }}
        }});
        
        // 收益率分布图表
        const returnsCtx = document.getElementById('returnsChart').getContext('2d');
        new Chart(returnsCtx, {{
            type: 'histogram',
            data: {{
                datasets: [{{
                    label: '日收益率分布',
                    data: {json.dumps(portfolio_data['returns'])},
                    backgroundColor: 'rgba(40, 167, 69, 0.6)',
                    borderColor: '#28a745',
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        display: false
                    }}
                }}
            }}
        }});
        
        // 交易分析图表
        const tradesCtx = document.getElementById('tradesChart').getContext('2d');
        new Chart(tradesCtx, {{
            type: 'doughnut',
            data: {{
                labels: ['买入', '卖出'],
                datasets: [{{
                    data: [{trade_data['buy_count']}, {trade_data['sell_count']}],
                    backgroundColor: ['#28a745', '#dc3545'],
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'bottom'
                    }}
                }}
            }}
        }});
        """
