"""
参数化策略框架

支持用户自定义各种技术指标参数、交易条件和风险管理规则的高级策略框架。
用户可以通过配置文件或界面来构建复杂的交易策略。
"""

from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime, timedelta
from collections import deque
import json
import logging
from dataclasses import dataclass, field
from enum import Enum

from .base_strategy import BaseStrategy
from .technical_strategy import TechnicalIndicators
from ..core.events import MarketDataEvent


class ConditionOperator(Enum):
    """条件操作符"""
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_EQUAL = ">="
    LESS_EQUAL = "<="
    EQUAL = "=="
    NOT_EQUAL = "!="
    CROSS_ABOVE = "cross_above"
    CROSS_BELOW = "cross_below"


class LogicalOperator(Enum):
    """逻辑操作符"""
    AND = "and"
    OR = "or"
    NOT = "not"


@dataclass
class IndicatorConfig:
    """技术指标配置"""
    name: str  # 指标名称，如 'sma', 'ema', 'rsi', 'macd'
    params: Dict[str, Any] = field(default_factory=dict)  # 指标参数
    alias: Optional[str] = None  # 指标别名，用于引用


@dataclass
class TradingCondition:
    """交易条件"""
    left_operand: str  # 左操作数，可以是指标名、价格等
    operator: ConditionOperator  # 比较操作符
    right_operand: Union[str, float, int]  # 右操作数
    weight: float = 1.0  # 条件权重


@dataclass
class TradingRule:
    """交易规则"""
    name: str
    conditions: List[TradingCondition]
    action: str  # 'buy', 'sell', 'hold'
    logical_operator: LogicalOperator = LogicalOperator.AND
    confidence: float = 1.0  # 信号置信度


@dataclass
class RiskManagementConfig:
    """风险管理配置"""
    max_position_size: float = 0.2  # 最大仓位比例
    stop_loss_pct: Optional[float] = None  # 止损百分比
    take_profit_pct: Optional[float] = None  # 止盈百分比
    max_drawdown_pct: Optional[float] = None  # 最大回撤限制
    position_sizing_method: str = "fixed"  # 仓位计算方法: fixed, kelly, volatility


@dataclass
class ParametricStrategyConfig:
    """参数化策略配置"""
    name: str
    description: str = ""
    indicators: List[IndicatorConfig] = field(default_factory=list)
    buy_rules: List[TradingRule] = field(default_factory=list)
    sell_rules: List[TradingRule] = field(default_factory=list)
    risk_management: RiskManagementConfig = field(default_factory=RiskManagementConfig)
    rebalance_frequency: str = "1h"  # 重新平衡频率
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ParametricStrategyConfig':
        """从字典创建配置"""
        # 解析指标配置
        indicators = []
        for ind_config in config_dict.get('indicators', []):
            indicators.append(IndicatorConfig(**ind_config))
        
        # 解析买入规则
        buy_rules = []
        for rule_config in config_dict.get('buy_rules', []):
            conditions = []
            for cond_config in rule_config.get('conditions', []):
                conditions.append(TradingCondition(
                    left_operand=cond_config['left_operand'],
                    operator=ConditionOperator(cond_config['operator']),
                    right_operand=cond_config['right_operand'],
                    weight=cond_config.get('weight', 1.0)
                ))
            buy_rules.append(TradingRule(
                name=rule_config['name'],
                conditions=conditions,
                logical_operator=LogicalOperator(rule_config.get('logical_operator', 'and')),
                action='buy',
                confidence=rule_config.get('confidence', 1.0)
            ))
        
        # 解析卖出规则
        sell_rules = []
        for rule_config in config_dict.get('sell_rules', []):
            conditions = []
            for cond_config in rule_config.get('conditions', []):
                conditions.append(TradingCondition(
                    left_operand=cond_config['left_operand'],
                    operator=ConditionOperator(cond_config['operator']),
                    right_operand=cond_config['right_operand'],
                    weight=cond_config.get('weight', 1.0)
                ))
            sell_rules.append(TradingRule(
                name=rule_config['name'],
                conditions=conditions,
                logical_operator=LogicalOperator(rule_config.get('logical_operator', 'and')),
                action='sell',
                confidence=rule_config.get('confidence', 1.0)
            ))
        
        # 解析风险管理配置
        risk_config = config_dict.get('risk_management', {})
        risk_management = RiskManagementConfig(**risk_config)
        
        return cls(
            name=config_dict['name'],
            description=config_dict.get('description', ''),
            indicators=indicators,
            buy_rules=buy_rules,
            sell_rules=sell_rules,
            risk_management=risk_management,
            rebalance_frequency=config_dict.get('rebalance_frequency', '1h')
        )


class ParametricStrategy(BaseStrategy):
    """参数化策略"""
    
    def __init__(self, config: ParametricStrategyConfig):
        super().__init__(config.name)
        self.config = config
        
        # 数据存储
        self.price_history = {}  # 价格历史
        self.indicator_values = {}  # 指标值
        self.last_signals = {}  # 最后信号
        
        # 计算所需的最大历史长度
        self.max_history = self._calculate_max_history()
        
        # 风险管理
        self.entry_prices = {}  # 入场价格
        self.peak_values = {}  # 峰值记录（用于追踪止损）
        self.local_positions = {}  # 本地持仓跟踪（避免重复买入）
        
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
    
    def _calculate_max_history(self) -> int:
        """计算所需的最大历史数据长度"""
        max_period = 50  # 默认最小值
        
        for indicator in self.config.indicators:
            if indicator.name in ['sma', 'ema']:
                period = indicator.params.get('period', 20)
                max_period = max(max_period, period + 10)
            elif indicator.name == 'rsi':
                period = indicator.params.get('period', 14)
                max_period = max(max_period, period + 20)
            elif indicator.name == 'macd':
                slow_period = indicator.params.get('slow_period', 26)
                max_period = max(max_period, slow_period + 20)
            elif indicator.name == 'bollinger':
                period = indicator.params.get('period', 20)
                max_period = max(max_period, period + 10)
        
        return max_period

    def on_start(self):
        """策略开始"""
        self.start_time = datetime.now()
        self.logger.info(f"参数化策略开始: {self.config.name}")

        # 初始化数据结构
        for symbol in self.symbols:
            self.price_history[symbol] = deque(maxlen=self.max_history)
            self.indicator_values[symbol] = {}
            self.last_signals[symbol] = None
            self.entry_prices[symbol] = None
            self.peak_values[symbol] = None

            # 初始化指标值存储
            for indicator in self.config.indicators:
                alias = indicator.alias or f"{indicator.name}_{hash(str(indicator.params)) % 1000}"
                self.indicator_values[symbol][alias] = deque(maxlen=100)

    def on_market_data(self, event: MarketDataEvent):
        """处理市场数据"""
        self.update_market_data(event)

        symbol = event.symbol
        price = event.close

        # 更新价格历史
        if symbol not in self.price_history:
            self.price_history[symbol] = deque(maxlen=self.max_history)

        self.price_history[symbol].append({
            'timestamp': event.timestamp,
            'open': event.open,
            'high': event.high,
            'low': event.low,
            'close': event.close,
            'volume': event.volume
        })

        # 计算技术指标
        self._calculate_indicators(symbol)

        # 检查交易信号
        self._check_trading_signals(symbol, price, event.timestamp)

        # 风险管理检查
        self._check_risk_management(symbol, price)

    def _calculate_indicators(self, symbol: str):
        """计算技术指标"""
        if len(self.price_history[symbol]) < 2:
            return

        prices = [bar['close'] for bar in self.price_history[symbol]]
        highs = [bar['high'] for bar in self.price_history[symbol]]
        lows = [bar['low'] for bar in self.price_history[symbol]]
        volumes = [bar['volume'] for bar in self.price_history[symbol]]

        for indicator in self.config.indicators:
            alias = indicator.alias or f"{indicator.name}_{hash(str(indicator.params)) % 1000}"

            try:
                if indicator.name == 'sma':
                    period = indicator.params.get('period', 20)
                    value = TechnicalIndicators.sma(prices, period)

                elif indicator.name == 'ema':
                    period = indicator.params.get('period', 20)
                    alpha = indicator.params.get('alpha', None)
                    value = TechnicalIndicators.ema(prices, period, alpha)

                elif indicator.name == 'rsi':
                    period = indicator.params.get('period', 14)
                    value = TechnicalIndicators.rsi(prices, period)

                elif indicator.name == 'macd':
                    fast_period = indicator.params.get('fast_period', 12)
                    slow_period = indicator.params.get('slow_period', 26)
                    signal_period = indicator.params.get('signal_period', 9)
                    value = self._calculate_macd(prices, fast_period, slow_period, signal_period)

                elif indicator.name == 'bollinger':
                    period = indicator.params.get('period', 20)
                    std_dev = indicator.params.get('std_dev', 2)
                    value = TechnicalIndicators.bollinger_bands(prices, period, std_dev)

                elif indicator.name == 'stochastic':
                    k_period = indicator.params.get('k_period', 14)
                    d_period = indicator.params.get('d_period', 3)
                    value = self._calculate_stochastic(highs, lows, prices, k_period, d_period)

                else:
                    self.logger.warning(f"未知指标类型: {indicator.name}")
                    continue

                if value is not None:
                    self.indicator_values[symbol][alias].append(value)

            except Exception as e:
                self.logger.error(f"计算指标 {indicator.name} 时出错: {e}")

    def _calculate_macd(self, prices: List[float], fast_period: int, slow_period: int, signal_period: int) -> Optional[Dict[str, float]]:
        """计算MACD指标"""
        if len(prices) < slow_period:
            return None

        ema_fast = TechnicalIndicators.ema(prices, fast_period)
        ema_slow = TechnicalIndicators.ema(prices, slow_period)

        if ema_fast is None or ema_slow is None:
            return None

        macd_line = ema_fast - ema_slow

        # 简化的信号线计算（实际应该用MACD线的EMA）
        signal_line = macd_line  # 这里简化处理
        histogram = macd_line - signal_line

        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }

    def _calculate_stochastic(self, highs: List[float], lows: List[float], closes: List[float],
                            k_period: int, d_period: int) -> Optional[Dict[str, float]]:
        """计算随机指标"""
        if len(closes) < k_period:
            return None

        recent_highs = highs[-k_period:]
        recent_lows = lows[-k_period:]
        current_close = closes[-1]

        highest_high = max(recent_highs)
        lowest_low = min(recent_lows)

        if highest_high == lowest_low:
            k_percent = 50
        else:
            k_percent = ((current_close - lowest_low) / (highest_high - lowest_low)) * 100

        # 简化的D%计算
        d_percent = k_percent  # 实际应该是K%的移动平均

        return {
            'k': k_percent,
            'd': d_percent
        }

    def _check_trading_signals(self, symbol: str, current_price: float, timestamp: datetime):
        """检查交易信号"""
        # 检查买入信号
        buy_signal_strength = self._evaluate_rules(symbol, self.config.buy_rules)
        if buy_signal_strength > 0:
            self._execute_buy_signal(symbol, current_price, buy_signal_strength, timestamp)

        # 检查卖出信号
        sell_signal_strength = self._evaluate_rules(symbol, self.config.sell_rules)
        if sell_signal_strength > 0:
            self._execute_sell_signal(symbol, current_price, sell_signal_strength, timestamp)

    def _evaluate_rules(self, symbol: str, rules: List[TradingRule]) -> float:
        """评估交易规则"""
        total_strength = 0.0

        for rule in rules:
            rule_satisfied = self._evaluate_rule_conditions(symbol, rule)
            if rule_satisfied:
                total_strength += rule.confidence

        return total_strength

    def _evaluate_rule_conditions(self, symbol: str, rule: TradingRule) -> bool:
        """评估单个规则的条件"""
        condition_results = []

        for condition in rule.conditions:
            result = self._evaluate_condition(symbol, condition)
            condition_results.append(result)

        # 根据逻辑操作符组合结果
        if rule.logical_operator == LogicalOperator.AND:
            return all(condition_results)
        elif rule.logical_operator == LogicalOperator.OR:
            return any(condition_results)
        else:  # NOT
            return not all(condition_results)

    def _evaluate_condition(self, symbol: str, condition: TradingCondition) -> bool:
        """评估单个条件"""
        try:
            # 获取左操作数的值
            left_value = self._get_operand_value(symbol, condition.left_operand)
            if left_value is None:
                return False

            # 获取右操作数的值
            right_value = self._get_operand_value(symbol, condition.right_operand)
            if right_value is None:
                return False

            # 执行比较
            if condition.operator == ConditionOperator.GREATER_THAN:
                return left_value > right_value
            elif condition.operator == ConditionOperator.LESS_THAN:
                return left_value < right_value
            elif condition.operator == ConditionOperator.GREATER_EQUAL:
                return left_value >= right_value
            elif condition.operator == ConditionOperator.LESS_EQUAL:
                return left_value <= right_value
            elif condition.operator == ConditionOperator.EQUAL:
                return abs(left_value - right_value) < 1e-6
            elif condition.operator == ConditionOperator.NOT_EQUAL:
                return abs(left_value - right_value) >= 1e-6
            elif condition.operator == ConditionOperator.CROSS_ABOVE:
                return self._check_cross_above(symbol, condition.left_operand, condition.right_operand)
            elif condition.operator == ConditionOperator.CROSS_BELOW:
                return self._check_cross_below(symbol, condition.left_operand, condition.right_operand)

        except Exception as e:
            self.logger.error(f"评估条件时出错: {e}")
            return False

        return False

    def _get_operand_value(self, symbol: str, operand: Union[str, float, int]) -> Optional[float]:
        """获取操作数的值"""
        if isinstance(operand, (int, float)):
            return float(operand)

        if isinstance(operand, str):
            # 特殊关键字
            if operand == 'price' or operand == 'close':
                if self.price_history[symbol]:
                    return self.price_history[symbol][-1]['close']
            elif operand == 'open':
                if self.price_history[symbol]:
                    return self.price_history[symbol][-1]['open']
            elif operand == 'high':
                if self.price_history[symbol]:
                    return self.price_history[symbol][-1]['high']
            elif operand == 'low':
                if self.price_history[symbol]:
                    return self.price_history[symbol][-1]['low']
            elif operand == 'volume':
                if self.price_history[symbol]:
                    return self.price_history[symbol][-1]['volume']

            # 技术指标值
            elif operand in self.indicator_values[symbol]:
                indicator_history = self.indicator_values[symbol][operand]
                if indicator_history:
                    latest_value = indicator_history[-1]
                    if isinstance(latest_value, dict):
                        # 对于复合指标（如MACD），返回主要值
                        if 'macd' in latest_value:
                            return latest_value['macd']
                        elif 'k' in latest_value:
                            return latest_value['k']
                        elif 'upper' in latest_value:
                            return latest_value['middle']  # 布林带中线
                    else:
                        return float(latest_value)

        return None

    def _check_cross_above(self, symbol: str, left_operand: str, right_operand: str) -> bool:
        """检查向上穿越"""
        if len(self.indicator_values[symbol].get(left_operand, [])) < 2:
            return False
        if len(self.indicator_values[symbol].get(right_operand, [])) < 2:
            return False

        left_current = self._get_operand_value(symbol, left_operand)
        left_previous = self.indicator_values[symbol][left_operand][-2]
        right_current = self._get_operand_value(symbol, right_operand)
        right_previous = self.indicator_values[symbol][right_operand][-2]

        if None in [left_current, left_previous, right_current, right_previous]:
            return False

        # 之前在下方，现在在上方
        return (left_previous <= right_previous) and (left_current > right_current)

    def _check_cross_below(self, symbol: str, left_operand: str, right_operand: str) -> bool:
        """检查向下穿越"""
        if len(self.indicator_values[symbol].get(left_operand, [])) < 2:
            return False
        if len(self.indicator_values[symbol].get(right_operand, [])) < 2:
            return False

        left_current = self._get_operand_value(symbol, left_operand)
        left_previous = self.indicator_values[symbol][left_operand][-2]
        right_current = self._get_operand_value(symbol, right_operand)
        right_previous = self.indicator_values[symbol][right_operand][-2]

        if None in [left_current, left_previous, right_current, right_previous]:
            return False

        # 之前在上方，现在在下方
        return (left_previous >= right_previous) and (left_current < right_current)

    def _execute_buy_signal(self, symbol: str, current_price: float, signal_strength: float, timestamp: datetime):
        """执行买入信号"""
        # 使用本地持仓跟踪避免重复买入
        local_quantity = self.local_positions.get(symbol, 0)

        # 调试信息
        self.logger.debug(f"买入检查 {symbol}: 本地持仓={local_quantity}, 价格={current_price:.2f}")

        # 如果已经有多头仓位，不重复买入
        if local_quantity > 0:
            self.logger.debug(f"已有本地持仓 {local_quantity}，跳过买入")
            return

        # 计算仓位大小
        position_size = self._calculate_position_size(symbol, current_price, signal_strength)
        if position_size <= 0:
            return

        # 生成交易信号
        self.generate_signal(symbol, 'BUY', signal_strength, {
            'price': current_price,
            'position_size': position_size,
            'timestamp': timestamp.isoformat()
        })

        # 执行买入
        cash = self.get_cash()
        position_value = cash * position_size
        quantity = position_value / current_price

        if quantity > 0:
            self.buy_market(symbol, quantity)
            self.entry_prices[symbol] = current_price
            self.peak_values[symbol] = current_price
            self.last_signals[symbol] = 'BUY'

            # 立即更新本地持仓记录（避免重复买入）
            self.local_positions[symbol] = self.local_positions.get(symbol, 0) + quantity

            self.logger.info(f"买入 {symbol}: 数量={quantity:.6f}, 价格={current_price:.2f}, 信号强度={signal_strength:.2f}")
            self.logger.debug(f"更新本地持仓 {symbol}: {self.local_positions[symbol]:.6f}")

    def _execute_sell_signal(self, symbol: str, current_price: float, signal_strength: float, timestamp: datetime):
        """执行卖出信号"""
        # 使用本地持仓跟踪
        local_quantity = self.local_positions.get(symbol, 0)

        # 如果没有仓位，不能卖出
        if local_quantity <= 0:
            return

        # 生成交易信号
        self.generate_signal(symbol, 'SELL', signal_strength, {
            'price': current_price,
            'quantity': local_quantity,
            'timestamp': timestamp.isoformat()
        })

        # 执行卖出
        self.sell_market(symbol, local_quantity)
        self.entry_prices[symbol] = None
        self.peak_values[symbol] = None
        self.last_signals[symbol] = 'SELL'

        # 更新本地持仓记录
        self.local_positions[symbol] = 0

        self.logger.info(f"卖出 {symbol}: 数量={local_quantity:.6f}, 价格={current_price:.2f}, 信号强度={signal_strength:.2f}")

    def _calculate_position_size(self, symbol: str, current_price: float, signal_strength: float) -> float:
        """计算仓位大小"""
        base_size = self.config.risk_management.max_position_size

        if self.config.risk_management.position_sizing_method == "fixed":
            return base_size
        elif self.config.risk_management.position_sizing_method == "signal_weighted":
            # 根据信号强度调整仓位
            return base_size * min(signal_strength, 1.0)
        elif self.config.risk_management.position_sizing_method == "volatility":
            # 根据波动率调整仓位（简化实现）
            if len(self.price_history[symbol]) >= 20:
                # 将deque转换为列表，然后取最后20个元素
                price_list = list(self.price_history[symbol])
                prices = [bar['close'] for bar in price_list[-20:]]
                returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                volatility = (sum(r**2 for r in returns) / len(returns)) ** 0.5
                # 波动率越高，仓位越小
                volatility_factor = max(0.1, 1.0 - volatility * 10)
                return base_size * volatility_factor

        return base_size

    def _check_risk_management(self, symbol: str, current_price: float):
        """检查风险管理"""
        # 使用本地持仓跟踪
        local_quantity = self.local_positions.get(symbol, 0)
        if local_quantity <= 0:
            return

        entry_price = self.entry_prices.get(symbol)
        if not entry_price:
            # 如果没有入场价格记录，可能是因为持仓是从其他地方来的
            # 使用当前价格作为入场价格的近似值
            self.entry_prices[symbol] = current_price
            self.peak_values[symbol] = current_price
            return

        # 更新峰值
        if self.peak_values[symbol] is None or current_price > self.peak_values[symbol]:
            self.peak_values[symbol] = current_price

        # 检查止损
        if self.config.risk_management.stop_loss_pct:
            stop_loss_price = entry_price * (1 - self.config.risk_management.stop_loss_pct / 100)
            if current_price <= stop_loss_price:
                self.logger.info(f"触发止损: {symbol}, 入场价格={entry_price:.2f}, 当前价格={current_price:.2f}, 止损价格={stop_loss_price:.2f}")
                self._execute_stop_loss(symbol, current_price, "止损")
                return

        # 检查止盈
        if self.config.risk_management.take_profit_pct:
            take_profit_price = entry_price * (1 + self.config.risk_management.take_profit_pct / 100)
            if current_price >= take_profit_price:
                self.logger.info(f"触发止盈: {symbol}, 入场价格={entry_price:.2f}, 当前价格={current_price:.2f}, 止盈价格={take_profit_price:.2f}")
                self._execute_stop_loss(symbol, current_price, "止盈")
                return

        # 检查最大回撤
        if self.config.risk_management.max_drawdown_pct and self.peak_values[symbol]:
            max_drawdown_price = self.peak_values[symbol] * (1 - self.config.risk_management.max_drawdown_pct / 100)
            if current_price <= max_drawdown_price:
                self._execute_stop_loss(symbol, current_price, "最大回撤")
                return

    def _execute_stop_loss(self, symbol: str, current_price: float, reason: str):
        """执行止损/止盈"""
        # 使用本地持仓跟踪
        local_quantity = self.local_positions.get(symbol, 0)
        if local_quantity <= 0:
            return

        # 生成信号
        self.generate_signal(symbol, 'SELL', 1.0, {
            'price': current_price,
            'reason': reason,
            'quantity': local_quantity
        })

        # 执行卖出
        self.sell_market(symbol, local_quantity)
        self.entry_prices[symbol] = None
        self.peak_values[symbol] = None
        self.last_signals[symbol] = 'SELL'

        # 更新本地持仓记录
        self.local_positions[symbol] = 0

        self.logger.info(f"{reason} {symbol}: 数量={local_quantity:.6f}, 价格={current_price:.2f}")

    def on_end(self):
        """策略结束"""
        self.end_time = datetime.now()
        self.logger.info(f"参数化策略结束: {self.config.name}")

        # 平仓所有持仓
        for symbol in self.symbols:
            position = self.get_position(symbol)
            if position and position.quantity > 0:
                current_price = self.get_current_price(symbol)
                if current_price:
                    self.sell_market(symbol, position.quantity)
                    self.logger.info(f"策略结束平仓 {symbol}: 数量={position.quantity:.6f}")

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.config.name,
            'description': self.config.description,
            'indicators': [
                {
                    'name': ind.name,
                    'params': ind.params,
                    'alias': ind.alias
                } for ind in self.config.indicators
            ],
            'buy_rules_count': len(self.config.buy_rules),
            'sell_rules_count': len(self.config.sell_rules),
            'risk_management': {
                'max_position_size': self.config.risk_management.max_position_size,
                'stop_loss_pct': self.config.risk_management.stop_loss_pct,
                'take_profit_pct': self.config.risk_management.take_profit_pct,
                'position_sizing_method': self.config.risk_management.position_sizing_method
            }
        }
