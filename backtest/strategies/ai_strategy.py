"""
AI预测策略

集成现有的AI预测系统，基于AI预测结果进行交易决策。
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from .base_strategy import BaseStrategy
from ..core.events import MarketDataEvent

# 导入现有的预测系统
try:
    from professional_prediction_system import ProfessionalPredictionSystem
    from professional_prediction_system_gpu import ProfessionalPredictionSystemGPU
    from interactive_enhanced_binary_system import InteractiveEnhancedBinarySystem
except ImportError as e:
    print(f"警告: 无法导入预测系统: {e}")


class AIStrategy(BaseStrategy):
    """AI预测策略"""
    
    def __init__(self, name: str = "AIStrategy", 
                 model_type: str = "professional",
                 confidence_threshold: float = 0.6,
                 position_size: float = 0.1):
        super().__init__(name)
        
        self.model_type = model_type
        self.confidence_threshold = confidence_threshold
        self.position_size = position_size  # 每次交易使用的资金比例
        
        # 预测系统
        self.predictor = None
        self.prediction_cache = {}
        self.last_prediction_time = {}
        
        # 策略参数
        self.prediction_interval = timedelta(hours=1)  # 预测间隔
        self.max_positions = 3  # 最大持仓数量
        
        # 初始化预测系统
        self._initialize_predictor()
    
    def _initialize_predictor(self):
        """初始化预测系统"""
        try:
            if self.model_type == "professional":
                self.predictor = ProfessionalPredictionSystem(enable_colors=False)
                self.log_info("初始化专业预测系统")
            elif self.model_type == "gpu":
                self.predictor = ProfessionalPredictionSystemGPU(enable_colors=False)
                self.log_info("初始化GPU预测系统")
            elif self.model_type == "binary":
                self.predictor = InteractiveEnhancedBinarySystem()
                self.log_info("初始化增强二分类系统")
            else:
                raise ValueError(f"不支持的模型类型: {self.model_type}")
                
        except Exception as e:
            self.log_error(f"初始化预测系统失败: {e}")
            self.predictor = None
    
    def on_start(self):
        """策略开始"""
        self.start_time = datetime.now()
        self.log_info(f"AI策略开始运行 (模型: {self.model_type})")
        
        # 初始化预测时间
        for symbol in self.symbols:
            self.last_prediction_time[symbol] = datetime.min
    
    def on_market_data(self, event: MarketDataEvent):
        """处理市场数据"""
        self.update_market_data(event)
        
        # 检查是否需要进行预测
        if self._should_predict(event.symbol, event.timestamp):
            self._make_prediction(event.symbol, event.timestamp)
    
    def _should_predict(self, symbol: str, timestamp: datetime) -> bool:
        """检查是否需要进行预测"""
        if symbol not in self.last_prediction_time:
            return True
        
        time_since_last = timestamp - self.last_prediction_time[symbol]
        return time_since_last >= self.prediction_interval
    
    def _make_prediction(self, symbol: str, timestamp: datetime):
        """进行AI预测"""
        if not self.predictor:
            return
        
        try:
            self.log_info(f"开始预测 {symbol}")
            
            # 调用预测系统
            if self.model_type in ["professional", "gpu"]:
                result = self.predictor.make_professional_prediction(symbol)
            else:  # binary
                result = self.predictor.predict_single_symbol(symbol)
            
            if result:
                self._process_prediction_result(symbol, result, timestamp)
                self.last_prediction_time[symbol] = timestamp
            
        except Exception as e:
            self.log_error(f"预测失败 {symbol}: {e}")
    
    def _process_prediction_result(self, symbol: str, result: Dict, timestamp: datetime):
        """处理预测结果"""
        try:
            # 解析预测结果
            if self.model_type in ["professional", "gpu"]:
                prediction_data = result.get('prediction_result', {})
                prediction_label = prediction_data.get('prediction_label', 'HOLD')
                confidence = prediction_data.get('confidence', 0.0)
                
                # 转换预测标签
                if prediction_label == '上涨':
                    signal_type = 'BUY'
                elif prediction_label == '下跌':
                    signal_type = 'SELL'
                else:
                    signal_type = 'HOLD'
                    
            else:  # binary
                prediction_data = result.get('prediction', {})
                prediction_direction = prediction_data.get('prediction_direction', 'HOLD')
                confidence = prediction_data.get('confidence', 0.0)
                
                # 转换预测方向
                if prediction_direction == 'UP':
                    signal_type = 'BUY'
                elif prediction_direction == 'DOWN':
                    signal_type = 'SELL'
                else:
                    signal_type = 'HOLD'
            
            # 缓存预测结果
            self.prediction_cache[symbol] = {
                'signal_type': signal_type,
                'confidence': confidence,
                'timestamp': timestamp,
                'raw_result': result
            }
            
            self.log_info(f"预测结果 {symbol}: {signal_type} (置信度: {confidence:.2f})")
            
            # 生成交易信号
            if confidence >= self.confidence_threshold:
                self.generate_signal(symbol, signal_type, confidence, {
                    'model_type': self.model_type,
                    'prediction_data': prediction_data
                })
                
                # 执行交易决策
                self._execute_trading_decision(symbol, signal_type, confidence)
            
        except Exception as e:
            self.log_error(f"处理预测结果失败 {symbol}: {e}")
    
    def _execute_trading_decision(self, symbol: str, signal_type: str, confidence: float):
        """执行交易决策"""
        if signal_type == 'HOLD':
            return
        
        current_position = self.get_position(symbol)
        current_quantity = current_position.quantity if current_position else 0
        current_price = self.get_current_price(symbol)
        
        if not current_price:
            return
        
        # 检查持仓数量限制
        active_positions = len([s for s in self.symbols 
                              if self.get_position(s) and self.get_position(s).quantity != 0])
        
        if signal_type == 'BUY':
            # 买入逻辑
            if current_quantity <= 0 and active_positions < self.max_positions:
                # 计算买入数量
                cash = self.get_cash()
                position_value = cash * self.position_size * confidence
                quantity = position_value / current_price
                
                if quantity > 0:
                    self.buy_market(symbol, quantity)
                    self.log_info(f"买入 {symbol}: {quantity:.6f} @ {current_price:.4f}")
        
        elif signal_type == 'SELL':
            # 卖出逻辑
            if current_quantity > 0:
                # 全部卖出
                self.sell_market(symbol, current_quantity)
                self.log_info(f"卖出 {symbol}: {current_quantity:.6f} @ {current_price:.4f}")
    
    def get_latest_prediction(self, symbol: str) -> Optional[Dict]:
        """获取最新预测结果"""
        return self.prediction_cache.get(symbol)
    
    def on_end(self):
        """策略结束"""
        self.end_time = datetime.now()
        self.log_info("AI策略运行结束")
        
        # 平仓所有持仓
        for symbol in self.symbols:
            position = self.get_position(symbol)
            if position and position.quantity > 0:
                self.sell_market(symbol, position.quantity)
                self.log_info(f"平仓 {symbol}: {position.quantity}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        stats = super().get_statistics()
        stats.update({
            'model_type': self.model_type,
            'confidence_threshold': self.confidence_threshold,
            'position_size': self.position_size,
            'prediction_count': len(self.prediction_cache),
            'predictor_available': self.predictor is not None
        })
        return stats


class EnsembleAIStrategy(BaseStrategy):
    """集成AI策略 - 结合多个AI模型"""
    
    def __init__(self, name: str = "EnsembleAIStrategy",
                 models: List[str] = None,
                 weights: List[float] = None,
                 confidence_threshold: float = 0.65):
        super().__init__(name)
        
        self.models = models or ["professional", "gpu", "binary"]
        self.weights = weights or [0.4, 0.4, 0.2]  # 权重分配
        self.confidence_threshold = confidence_threshold
        
        # 确保权重和模型数量匹配
        if len(self.weights) != len(self.models):
            self.weights = [1.0 / len(self.models)] * len(self.models)
        
        # 标准化权重
        total_weight = sum(self.weights)
        self.weights = [w / total_weight for w in self.weights]
        
        # 初始化多个AI策略
        self.ai_strategies = []
        for i, model_type in enumerate(self.models):
            strategy = AIStrategy(
                name=f"AI_{model_type}",
                model_type=model_type,
                confidence_threshold=0.5,  # 单个模型使用较低阈值
                position_size=0.05  # 较小的仓位
            )
            self.ai_strategies.append(strategy)
    
    def set_engine(self, engine):
        """设置回测引擎"""
        super().set_engine(engine)
        for strategy in self.ai_strategies:
            strategy.set_engine(engine)
    
    def set_symbols(self, symbols: List[str]):
        """设置交易对"""
        super().set_symbols(symbols)
        for strategy in self.ai_strategies:
            strategy.set_symbols(symbols)
    
    def on_start(self):
        """策略开始"""
        self.start_time = datetime.now()
        self.log_info(f"集成AI策略开始运行 (模型: {self.models})")
        
        for strategy in self.ai_strategies:
            strategy.on_start()
    
    def on_market_data(self, event: MarketDataEvent):
        """处理市场数据"""
        self.update_market_data(event)
        
        # 让所有子策略处理数据
        for strategy in self.ai_strategies:
            strategy.on_market_data(event)
        
        # 集成预测结果
        self._ensemble_predictions(event.symbol)
    
    def _ensemble_predictions(self, symbol: str):
        """集成多个模型的预测结果"""
        predictions = []
        confidences = []
        
        for i, strategy in enumerate(self.ai_strategies):
            prediction = strategy.get_latest_prediction(symbol)
            if prediction:
                signal_type = prediction['signal_type']
                confidence = prediction['confidence']
                
                # 转换信号为数值
                if signal_type == 'BUY':
                    signal_value = 1.0
                elif signal_type == 'SELL':
                    signal_value = -1.0
                else:
                    signal_value = 0.0
                
                predictions.append(signal_value * confidence)
                confidences.append(confidence)
        
        if predictions:
            # 加权平均
            weighted_prediction = sum(p * w for p, w in zip(predictions, self.weights))
            avg_confidence = sum(c * w for c, w in zip(confidences, self.weights))
            
            # 确定最终信号
            if weighted_prediction > 0.1:
                final_signal = 'BUY'
            elif weighted_prediction < -0.1:
                final_signal = 'SELL'
            else:
                final_signal = 'HOLD'
            
            # 生成集成信号
            if avg_confidence >= self.confidence_threshold:
                self.generate_signal(symbol, final_signal, avg_confidence, {
                    'ensemble_prediction': weighted_prediction,
                    'individual_predictions': predictions,
                    'weights': self.weights
                })
    
    def on_end(self):
        """策略结束"""
        self.end_time = datetime.now()
        self.log_info("集成AI策略运行结束")
        
        for strategy in self.ai_strategies:
            strategy.on_end()
