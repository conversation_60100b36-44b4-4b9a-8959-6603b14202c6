"""
策略模板

提供各种预定义的参数化策略模板，用户可以基于这些模板快速创建自己的策略。
"""

from typing import Dict, Any
from .parametric_strategy import ParametricStrategyConfig, IndicatorConfig, TradingRule, TradingCondition, RiskManagementConfig, ConditionOperator, LogicalOperator


class StrategyTemplates:
    """策略模板集合"""
    
    @staticmethod
    def get_dual_moving_average_template() -> Dict[str, Any]:
        """双移动平均线策略模板"""
        return {
            "name": "双移动平均线策略",
            "description": "基于快慢移动平均线交叉的经典策略",
            "indicators": [
                {
                    "name": "sma",
                    "params": {"period": 10},
                    "alias": "sma_fast"
                },
                {
                    "name": "sma", 
                    "params": {"period": 20},
                    "alias": "sma_slow"
                }
            ],
            "buy_rules": [
                {
                    "name": "金叉买入",
                    "conditions": [
                        {
                            "left_operand": "sma_fast",
                            "operator": "cross_above",
                            "right_operand": "sma_slow",
                            "weight": 1.0
                        }
                    ],
                    "logical_operator": "and",
                    "confidence": 0.8
                }
            ],
            "sell_rules": [
                {
                    "name": "死叉卖出",
                    "conditions": [
                        {
                            "left_operand": "sma_fast",
                            "operator": "cross_below", 
                            "right_operand": "sma_slow",
                            "weight": 1.0
                        }
                    ],
                    "logical_operator": "and",
                    "confidence": 0.8
                }
            ],
            "risk_management": {
                "max_position_size": 0.2,
                "stop_loss_pct": 5.0,
                "take_profit_pct": 10.0,
                "position_sizing_method": "fixed"
            }
        }
    
    @staticmethod
    def get_rsi_oversold_template() -> Dict[str, Any]:
        """RSI超买超卖策略模板"""
        return {
            "name": "RSI超买超卖策略",
            "description": "基于RSI指标的超买超卖策略",
            "indicators": [
                {
                    "name": "rsi",
                    "params": {"period": 14},
                    "alias": "rsi_14"
                }
            ],
            "buy_rules": [
                {
                    "name": "RSI超卖买入",
                    "conditions": [
                        {
                            "left_operand": "rsi_14",
                            "operator": "<",
                            "right_operand": 30,
                            "weight": 1.0
                        }
                    ],
                    "logical_operator": "and",
                    "confidence": 0.7
                }
            ],
            "sell_rules": [
                {
                    "name": "RSI超买卖出",
                    "conditions": [
                        {
                            "left_operand": "rsi_14",
                            "operator": ">",
                            "right_operand": 70,
                            "weight": 1.0
                        }
                    ],
                    "logical_operator": "and",
                    "confidence": 0.7
                }
            ],
            "risk_management": {
                "max_position_size": 0.15,
                "stop_loss_pct": 3.0,
                "take_profit_pct": 6.0,
                "position_sizing_method": "signal_weighted"
            }
        }
    
    @staticmethod
    def get_bollinger_bands_template() -> Dict[str, Any]:
        """布林带策略模板"""
        return {
            "name": "布林带均值回归策略",
            "description": "基于布林带的均值回归策略",
            "indicators": [
                {
                    "name": "bollinger",
                    "params": {"period": 20, "std_dev": 2},
                    "alias": "bb_20"
                },
                {
                    "name": "rsi",
                    "params": {"period": 14},
                    "alias": "rsi_14"
                }
            ],
            "buy_rules": [
                {
                    "name": "触及下轨买入",
                    "conditions": [
                        {
                            "left_operand": "price",
                            "operator": "<=",
                            "right_operand": "bb_20_lower",
                            "weight": 1.0
                        },
                        {
                            "left_operand": "rsi_14",
                            "operator": "<",
                            "right_operand": 40,
                            "weight": 0.5
                        }
                    ],
                    "logical_operator": "and",
                    "confidence": 0.8
                }
            ],
            "sell_rules": [
                {
                    "name": "触及上轨卖出",
                    "conditions": [
                        {
                            "left_operand": "price",
                            "operator": ">=",
                            "right_operand": "bb_20_upper",
                            "weight": 1.0
                        },
                        {
                            "left_operand": "rsi_14",
                            "operator": ">",
                            "right_operand": 60,
                            "weight": 0.5
                        }
                    ],
                    "logical_operator": "and",
                    "confidence": 0.8
                }
            ],
            "risk_management": {
                "max_position_size": 0.25,
                "stop_loss_pct": 4.0,
                "take_profit_pct": 8.0,
                "position_sizing_method": "volatility"
            }
        }
    
    @staticmethod
    def get_macd_momentum_template() -> Dict[str, Any]:
        """MACD动量策略模板"""
        return {
            "name": "MACD动量策略",
            "description": "基于MACD指标的动量策略",
            "indicators": [
                {
                    "name": "macd",
                    "params": {"fast_period": 12, "slow_period": 26, "signal_period": 9},
                    "alias": "macd_12_26_9"
                },
                {
                    "name": "sma",
                    "params": {"period": 50},
                    "alias": "sma_50"
                }
            ],
            "buy_rules": [
                {
                    "name": "MACD金叉买入",
                    "conditions": [
                        {
                            "left_operand": "macd_12_26_9",
                            "operator": "cross_above",
                            "right_operand": 0,
                            "weight": 1.0
                        },
                        {
                            "left_operand": "price",
                            "operator": ">",
                            "right_operand": "sma_50",
                            "weight": 0.5
                        }
                    ],
                    "logical_operator": "and",
                    "confidence": 0.9
                }
            ],
            "sell_rules": [
                {
                    "name": "MACD死叉卖出",
                    "conditions": [
                        {
                            "left_operand": "macd_12_26_9",
                            "operator": "cross_below",
                            "right_operand": 0,
                            "weight": 1.0
                        }
                    ],
                    "logical_operator": "and",
                    "confidence": 0.9
                }
            ],
            "risk_management": {
                "max_position_size": 0.3,
                "stop_loss_pct": 6.0,
                "take_profit_pct": 12.0,
                "position_sizing_method": "signal_weighted"
            }
        }
    
    @staticmethod
    def get_multi_indicator_template() -> Dict[str, Any]:
        """多指标组合策略模板"""
        return {
            "name": "多指标组合策略",
            "description": "结合多个技术指标的综合策略",
            "indicators": [
                {
                    "name": "sma",
                    "params": {"period": 20},
                    "alias": "sma_20"
                },
                {
                    "name": "rsi",
                    "params": {"period": 14},
                    "alias": "rsi_14"
                },
                {
                    "name": "macd",
                    "params": {"fast_period": 12, "slow_period": 26, "signal_period": 9},
                    "alias": "macd_12_26_9"
                }
            ],
            "buy_rules": [
                {
                    "name": "多指标买入信号",
                    "conditions": [
                        {
                            "left_operand": "price",
                            "operator": ">",
                            "right_operand": "sma_20",
                            "weight": 1.0
                        },
                        {
                            "left_operand": "rsi_14",
                            "operator": "<",
                            "right_operand": 50,
                            "weight": 0.8
                        },
                        {
                            "left_operand": "macd_12_26_9",
                            "operator": ">",
                            "right_operand": 0,
                            "weight": 0.9
                        }
                    ],
                    "logical_operator": "and",
                    "confidence": 0.85
                }
            ],
            "sell_rules": [
                {
                    "name": "多指标卖出信号",
                    "conditions": [
                        {
                            "left_operand": "price",
                            "operator": "<",
                            "right_operand": "sma_20",
                            "weight": 1.0
                        },
                        {
                            "left_operand": "rsi_14",
                            "operator": ">",
                            "right_operand": 70,
                            "weight": 0.8
                        }
                    ],
                    "logical_operator": "or",
                    "confidence": 0.75
                }
            ],
            "risk_management": {
                "max_position_size": 0.2,
                "stop_loss_pct": 4.0,
                "take_profit_pct": 8.0,
                "max_drawdown_pct": 3.0,
                "position_sizing_method": "volatility"
            }
        }
    
    @staticmethod
    def get_all_templates() -> Dict[str, Dict[str, Any]]:
        """获取所有策略模板"""
        return {
            "dual_ma": StrategyTemplates.get_dual_moving_average_template(),
            "rsi_oversold": StrategyTemplates.get_rsi_oversold_template(),
            "bollinger_bands": StrategyTemplates.get_bollinger_bands_template(),
            "macd_momentum": StrategyTemplates.get_macd_momentum_template(),
            "multi_indicator": StrategyTemplates.get_multi_indicator_template()
        }
