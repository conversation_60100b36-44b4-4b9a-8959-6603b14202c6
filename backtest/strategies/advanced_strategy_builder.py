"""
高级策略构建器

提供可视化的策略构建界面，支持拖拽式策略设计、多指标组合、复杂条件逻辑等。
"""

from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
import json
import logging
from dataclasses import dataclass, field
from enum import Enum

from .parametric_strategy import (
    ParametricStrategy, ParametricStrategyConfig, IndicatorConfig, 
    TradingRule, TradingCondition, RiskManagementConfig,
    ConditionOperator, LogicalOperator
)


class IndicatorType(Enum):
    """指标类型"""
    TREND = "trend"  # 趋势指标
    MOMENTUM = "momentum"  # 动量指标
    VOLATILITY = "volatility"  # 波动率指标
    VOLUME = "volume"  # 成交量指标
    SUPPORT_RESISTANCE = "support_resistance"  # 支撑阻力指标


class SignalStrength(Enum):
    """信号强度"""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


@dataclass
class IndicatorDefinition:
    """指标定义"""
    name: str
    display_name: str
    type: IndicatorType
    description: str
    parameters: Dict[str, Dict[str, Any]]  # 参数定义
    output_fields: List[str]  # 输出字段
    calculation_function: Optional[str] = None


@dataclass
class StrategyComponent:
    """策略组件"""
    id: str
    type: str  # 'indicator', 'condition', 'rule', 'risk_management'
    config: Dict[str, Any]
    position: Dict[str, float] = field(default_factory=dict)  # UI位置信息
    connections: List[str] = field(default_factory=list)  # 连接的组件ID


@dataclass
class StrategyBlueprint:
    """策略蓝图"""
    name: str
    description: str
    components: List[StrategyComponent]
    connections: List[Dict[str, str]]  # 组件间的连接关系
    metadata: Dict[str, Any] = field(default_factory=dict)


class AdvancedStrategyBuilder:
    """高级策略构建器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.indicator_library = self._initialize_indicator_library()
        self.strategy_templates = {}
        self.custom_indicators = {}
    
    def _initialize_indicator_library(self) -> Dict[str, IndicatorDefinition]:
        """初始化指标库"""
        library = {}
        
        # 趋势指标
        library['sma'] = IndicatorDefinition(
            name='sma',
            display_name='简单移动平均',
            type=IndicatorType.TREND,
            description='计算指定周期的简单移动平均线',
            parameters={
                'period': {'type': 'int', 'default': 20, 'min': 5, 'max': 200, 'description': '计算周期'}
            },
            output_fields=['value']
        )
        
        library['ema'] = IndicatorDefinition(
            name='ema',
            display_name='指数移动平均',
            type=IndicatorType.TREND,
            description='计算指定周期的指数移动平均线',
            parameters={
                'period': {'type': 'int', 'default': 20, 'min': 5, 'max': 200, 'description': '计算周期'},
                'alpha': {'type': 'float', 'default': None, 'min': 0.01, 'max': 1.0, 'description': '平滑因子'}
            },
            output_fields=['value']
        )
        
        # 动量指标
        library['rsi'] = IndicatorDefinition(
            name='rsi',
            display_name='相对强弱指数',
            type=IndicatorType.MOMENTUM,
            description='计算相对强弱指数，用于判断超买超卖',
            parameters={
                'period': {'type': 'int', 'default': 14, 'min': 5, 'max': 50, 'description': '计算周期'}
            },
            output_fields=['value']
        )
        
        library['macd'] = IndicatorDefinition(
            name='macd',
            display_name='MACD指标',
            type=IndicatorType.MOMENTUM,
            description='移动平均收敛发散指标',
            parameters={
                'fast_period': {'type': 'int', 'default': 12, 'min': 5, 'max': 50, 'description': '快线周期'},
                'slow_period': {'type': 'int', 'default': 26, 'min': 10, 'max': 100, 'description': '慢线周期'},
                'signal_period': {'type': 'int', 'default': 9, 'min': 3, 'max': 30, 'description': '信号线周期'}
            },
            output_fields=['macd', 'signal', 'histogram']
        )
        
        library['stochastic'] = IndicatorDefinition(
            name='stochastic',
            display_name='随机指标',
            type=IndicatorType.MOMENTUM,
            description='随机振荡器，用于判断超买超卖',
            parameters={
                'k_period': {'type': 'int', 'default': 14, 'min': 5, 'max': 50, 'description': 'K值周期'},
                'd_period': {'type': 'int', 'default': 3, 'min': 1, 'max': 10, 'description': 'D值周期'}
            },
            output_fields=['k', 'd']
        )
        
        # 波动率指标
        library['bollinger'] = IndicatorDefinition(
            name='bollinger',
            display_name='布林带',
            type=IndicatorType.VOLATILITY,
            description='布林带指标，用于判断价格通道',
            parameters={
                'period': {'type': 'int', 'default': 20, 'min': 5, 'max': 100, 'description': '计算周期'},
                'std_dev': {'type': 'float', 'default': 2.0, 'min': 0.5, 'max': 5.0, 'description': '标准差倍数'}
            },
            output_fields=['upper', 'middle', 'lower', 'width', 'percent_b']
        )
        
        library['atr'] = IndicatorDefinition(
            name='atr',
            display_name='平均真实范围',
            type=IndicatorType.VOLATILITY,
            description='平均真实范围，衡量价格波动性',
            parameters={
                'period': {'type': 'int', 'default': 14, 'min': 5, 'max': 50, 'description': '计算周期'}
            },
            output_fields=['value']
        )
        
        # 成交量指标
        library['volume_sma'] = IndicatorDefinition(
            name='volume_sma',
            display_name='成交量移动平均',
            type=IndicatorType.VOLUME,
            description='成交量的简单移动平均',
            parameters={
                'period': {'type': 'int', 'default': 20, 'min': 5, 'max': 100, 'description': '计算周期'}
            },
            output_fields=['value']
        )
        
        library['obv'] = IndicatorDefinition(
            name='obv',
            display_name='能量潮指标',
            type=IndicatorType.VOLUME,
            description='能量潮指标，结合价格和成交量',
            parameters={},
            output_fields=['value']
        )
        
        return library
    
    def get_indicator_library(self) -> Dict[str, Dict[str, Any]]:
        """获取指标库信息"""
        library_info = {}
        for name, definition in self.indicator_library.items():
            library_info[name] = {
                'name': definition.name,
                'display_name': definition.display_name,
                'type': definition.type.value,
                'description': definition.description,
                'parameters': definition.parameters,
                'output_fields': definition.output_fields
            }
        return library_info
    
    def create_strategy_component(self, component_type: str, config: Dict[str, Any]) -> StrategyComponent:
        """创建策略组件"""
        import uuid
        component_id = str(uuid.uuid4())
        
        return StrategyComponent(
            id=component_id,
            type=component_type,
            config=config
        )
    
    def validate_strategy_blueprint(self, blueprint: StrategyBlueprint) -> List[str]:
        """验证策略蓝图"""
        errors = []
        
        # 检查必要组件
        has_indicators = any(comp.type == 'indicator' for comp in blueprint.components)
        has_buy_rules = any(comp.type == 'buy_rule' for comp in blueprint.components)
        has_sell_rules = any(comp.type == 'sell_rule' for comp in blueprint.components)
        
        if not has_indicators:
            errors.append("策略必须包含至少一个技术指标")
        if not has_buy_rules:
            errors.append("策略必须包含至少一个买入规则")
        if not has_sell_rules:
            errors.append("策略必须包含至少一个卖出规则")
        
        # 检查组件配置
        for component in blueprint.components:
            if component.type == 'indicator':
                indicator_name = component.config.get('name')
                if indicator_name not in self.indicator_library:
                    errors.append(f"未知的指标类型: {indicator_name}")
                else:
                    # 验证参数
                    definition = self.indicator_library[indicator_name]
                    for param_name, param_def in definition.parameters.items():
                        if param_name in component.config.get('params', {}):
                            value = component.config['params'][param_name]
                            if param_def['type'] == 'int' and not isinstance(value, int):
                                errors.append(f"指标 {indicator_name} 的参数 {param_name} 必须是整数")
                            elif param_def['type'] == 'float' and not isinstance(value, (int, float)):
                                errors.append(f"指标 {indicator_name} 的参数 {param_name} 必须是数字")
        
        return errors
    
    def build_strategy_from_blueprint(self, blueprint: StrategyBlueprint) -> ParametricStrategyConfig:
        """从蓝图构建策略配置"""
        # 验证蓝图
        errors = self.validate_strategy_blueprint(blueprint)
        if errors:
            raise ValueError(f"策略蓝图验证失败: {'; '.join(errors)}")
        
        # 提取指标配置
        indicators = []
        for component in blueprint.components:
            if component.type == 'indicator':
                indicator_config = IndicatorConfig(
                    name=component.config['name'],
                    params=component.config.get('params', {}),
                    alias=component.config.get('alias')
                )
                indicators.append(indicator_config)
        
        # 提取交易规则
        buy_rules = []
        sell_rules = []
        
        for component in blueprint.components:
            if component.type in ['buy_rule', 'sell_rule']:
                conditions = []
                for cond_config in component.config.get('conditions', []):
                    condition = TradingCondition(
                        left_operand=cond_config['left_operand'],
                        operator=ConditionOperator(cond_config['operator']),
                        right_operand=cond_config['right_operand'],
                        weight=cond_config.get('weight', 1.0)
                    )
                    conditions.append(condition)
                
                rule = TradingRule(
                    name=component.config['name'],
                    conditions=conditions,
                    logical_operator=LogicalOperator(component.config.get('logical_operator', 'and')),
                    action=component.type.replace('_rule', ''),
                    confidence=component.config.get('confidence', 1.0)
                )
                
                if component.type == 'buy_rule':
                    buy_rules.append(rule)
                else:
                    sell_rules.append(rule)
        
        # 提取风险管理配置
        risk_management = RiskManagementConfig()
        for component in blueprint.components:
            if component.type == 'risk_management':
                risk_management = RiskManagementConfig(**component.config)
                break
        
        # 创建策略配置
        strategy_config = ParametricStrategyConfig(
            name=blueprint.name,
            description=blueprint.description,
            indicators=indicators,
            buy_rules=buy_rules,
            sell_rules=sell_rules,
            risk_management=risk_management
        )
        
        return strategy_config

    def generate_strategy_combinations(self, base_indicators: List[str],
                                     max_indicators: int = 3) -> List[StrategyBlueprint]:
        """生成策略组合"""
        from itertools import combinations

        strategy_combinations = []

        # 生成不同的指标组合
        for r in range(1, min(max_indicators + 1, len(base_indicators) + 1)):
            for indicator_combo in combinations(base_indicators, r):
                # 为每个组合创建策略蓝图
                blueprint = self._create_combination_blueprint(list(indicator_combo))
                strategy_combinations.append(blueprint)

        return strategy_combinations

    def _create_combination_blueprint(self, indicators: List[str]) -> StrategyBlueprint:
        """创建组合策略蓝图"""
        import uuid

        components = []

        # 添加指标组件
        for i, indicator_name in enumerate(indicators):
            if indicator_name in self.indicator_library:
                definition = self.indicator_library[indicator_name]

                # 使用默认参数
                params = {}
                for param_name, param_def in definition.parameters.items():
                    params[param_name] = param_def['default']

                component = StrategyComponent(
                    id=str(uuid.uuid4()),
                    type='indicator',
                    config={
                        'name': indicator_name,
                        'params': params,
                        'alias': f"{indicator_name}_{i}"
                    }
                )
                components.append(component)

        # 添加基本的买入/卖出规则
        buy_rule = self._create_default_buy_rule(indicators)
        sell_rule = self._create_default_sell_rule(indicators)

        components.extend([buy_rule, sell_rule])

        # 添加风险管理
        risk_component = StrategyComponent(
            id=str(uuid.uuid4()),
            type='risk_management',
            config={
                'max_position_size': 0.2,
                'stop_loss_pct': 5.0,
                'take_profit_pct': 10.0,
                'position_sizing_method': 'fixed'
            }
        )
        components.append(risk_component)

        blueprint = StrategyBlueprint(
            name=f"组合策略_{'+'.join(indicators)}",
            description=f"基于{', '.join(indicators)}的组合策略",
            components=components,
            connections=[]
        )

        return blueprint

    def _create_default_buy_rule(self, indicators: List[str]) -> StrategyComponent:
        """创建默认买入规则"""
        import uuid

        conditions = []

        # 根据指标类型创建不同的条件
        for indicator in indicators:
            if indicator == 'rsi':
                conditions.append({
                    'left_operand': 'rsi_0',
                    'operator': '<',
                    'right_operand': 30,
                    'weight': 1.0
                })
            elif indicator == 'sma':
                conditions.append({
                    'left_operand': 'price',
                    'operator': '>',
                    'right_operand': 'sma_0',
                    'weight': 1.0
                })
            elif indicator == 'macd':
                conditions.append({
                    'left_operand': 'macd_0',
                    'operator': '>',
                    'right_operand': 0,
                    'weight': 1.0
                })

        return StrategyComponent(
            id=str(uuid.uuid4()),
            type='buy_rule',
            config={
                'name': '组合买入信号',
                'conditions': conditions,
                'logical_operator': 'and',
                'confidence': 0.8
            }
        )

    def _create_default_sell_rule(self, indicators: List[str]) -> StrategyComponent:
        """创建默认卖出规则"""
        import uuid

        conditions = []

        # 根据指标类型创建不同的条件
        for indicator in indicators:
            if indicator == 'rsi':
                conditions.append({
                    'left_operand': 'rsi_0',
                    'operator': '>',
                    'right_operand': 70,
                    'weight': 1.0
                })
            elif indicator == 'sma':
                conditions.append({
                    'left_operand': 'price',
                    'operator': '<',
                    'right_operand': 'sma_0',
                    'weight': 1.0
                })
            elif indicator == 'macd':
                conditions.append({
                    'left_operand': 'macd_0',
                    'operator': '<',
                    'right_operand': 0,
                    'weight': 1.0
                })

        return StrategyComponent(
            id=str(uuid.uuid4()),
            type='sell_rule',
            config={
                'name': '组合卖出信号',
                'conditions': conditions,
                'logical_operator': 'or',
                'confidence': 0.8
            }
        )

    def export_strategy_blueprint(self, blueprint: StrategyBlueprint) -> str:
        """导出策略蓝图为JSON"""
        blueprint_dict = {
            'name': blueprint.name,
            'description': blueprint.description,
            'components': [
                {
                    'id': comp.id,
                    'type': comp.type,
                    'config': comp.config,
                    'position': comp.position,
                    'connections': comp.connections
                } for comp in blueprint.components
            ],
            'connections': blueprint.connections,
            'metadata': blueprint.metadata
        }

        return json.dumps(blueprint_dict, indent=2, ensure_ascii=False)

    def import_strategy_blueprint(self, blueprint_json: str) -> StrategyBlueprint:
        """从JSON导入策略蓝图"""
        blueprint_dict = json.loads(blueprint_json)

        components = []
        for comp_dict in blueprint_dict['components']:
            component = StrategyComponent(
                id=comp_dict['id'],
                type=comp_dict['type'],
                config=comp_dict['config'],
                position=comp_dict.get('position', {}),
                connections=comp_dict.get('connections', [])
            )
            components.append(component)

        blueprint = StrategyBlueprint(
            name=blueprint_dict['name'],
            description=blueprint_dict['description'],
            components=components,
            connections=blueprint_dict.get('connections', []),
            metadata=blueprint_dict.get('metadata', {})
        )

        return blueprint
