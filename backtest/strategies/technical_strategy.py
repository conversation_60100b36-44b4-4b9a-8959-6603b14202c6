"""
技术指标策略

基于技术指标的交易策略实现。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import deque

from .base_strategy import BaseStrategy
from ..core.events import MarketDataEvent


class TechnicalIndicators:
    """技术指标计算类"""
    
    @staticmethod
    def sma(prices: List[float], period: int) -> Optional[float]:
        """简单移动平均"""
        if len(prices) < period:
            return None
        return sum(prices[-period:]) / period
    
    @staticmethod
    def ema(prices: List[float], period: int, alpha: Optional[float] = None) -> Optional[float]:
        """指数移动平均"""
        if len(prices) < period:
            return None
        
        if alpha is None:
            alpha = 2.0 / (period + 1)
        
        ema_value = prices[0]
        for price in prices[1:]:
            ema_value = alpha * price + (1 - alpha) * ema_value
        
        return ema_value
    
    @staticmethod
    def rsi(prices: List[float], period: int = 14) -> Optional[float]:
        """相对强弱指数"""
        if len(prices) < period + 1:
            return None
        
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def macd(prices: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, Optional[float]]:
        """MACD指标"""
        if len(prices) < slow:
            return {'macd': None, 'signal': None, 'histogram': None}
        
        ema_fast = TechnicalIndicators.ema(prices, fast)
        ema_slow = TechnicalIndicators.ema(prices, slow)
        
        if ema_fast is None or ema_slow is None:
            return {'macd': None, 'signal': None, 'histogram': None}
        
        macd_line = ema_fast - ema_slow
        
        # 计算信号线需要MACD历史数据，这里简化处理
        signal_line = macd_line  # 简化版本
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    @staticmethod
    def bollinger_bands(prices: List[float], period: int = 20, std_dev: float = 2.0) -> Dict[str, Optional[float]]:
        """布林带"""
        if len(prices) < period:
            return {'upper': None, 'middle': None, 'lower': None}
        
        recent_prices = prices[-period:]
        middle = sum(recent_prices) / period
        variance = sum((p - middle) ** 2 for p in recent_prices) / period
        std = variance ** 0.5
        
        upper = middle + (std_dev * std)
        lower = middle - (std_dev * std)
        
        return {
            'upper': upper,
            'middle': middle,
            'lower': lower
        }


class MovingAverageStrategy(BaseStrategy):
    """移动平均策略"""
    
    def __init__(self, name: str = "MA_Strategy",
                 fast_period: int = 10,
                 slow_period: int = 20,
                 position_size: float = 0.2):
        super().__init__(name)

        self.fast_period = fast_period
        self.slow_period = slow_period
        self.position_size = position_size

        # 价格历史
        self.price_history = {}
        self.max_history = max(fast_period, slow_period) + 10

        # 移动平均线历史，用于判断穿越
        self.ma_history = {}
        self.last_signal = {}  # 记录最后一次信号，避免重复交易
    
    def on_start(self):
        """策略开始"""
        self.start_time = datetime.now()
        self.log_info(f"移动平均策略开始 (快线: {self.fast_period}, 慢线: {self.slow_period})")

        for symbol in self.symbols:
            self.price_history[symbol] = deque(maxlen=self.max_history)
            self.ma_history[symbol] = {'fast': [], 'slow': []}
            self.last_signal[symbol] = None
    
    def on_market_data(self, event: MarketDataEvent):
        """处理市场数据"""
        self.update_market_data(event)
        
        symbol = event.symbol
        price = event.close
        
        # 更新价格历史
        if symbol not in self.price_history:
            self.price_history[symbol] = deque(maxlen=self.max_history)
        
        self.price_history[symbol].append(price)
        
        # 计算移动平均
        if len(self.price_history[symbol]) >= self.slow_period:
            prices = list(self.price_history[symbol])

            fast_ma = TechnicalIndicators.sma(prices, self.fast_period)
            slow_ma = TechnicalIndicators.sma(prices, self.slow_period)

            if fast_ma and slow_ma:
                # 保存移动平均线历史
                self.ma_history[symbol]['fast'].append(fast_ma)
                self.ma_history[symbol]['slow'].append(slow_ma)

                # 只保留最近的几个值
                if len(self.ma_history[symbol]['fast']) > 5:
                    self.ma_history[symbol]['fast'].pop(0)
                    self.ma_history[symbol]['slow'].pop(0)

                self._check_signals(symbol, price, fast_ma, slow_ma)
    
    def _check_signals(self, symbol: str, current_price: float, fast_ma: float, slow_ma: float):
        """检查交易信号"""
        position = self.get_position(symbol)
        current_quantity = position.quantity if position else 0

        # 需要至少2个数据点来判断穿越
        if len(self.ma_history[symbol]['fast']) < 2:
            return

        # 获取前一个移动平均值
        prev_fast_ma = self.ma_history[symbol]['fast'][-2]
        prev_slow_ma = self.ma_history[symbol]['slow'][-2]

        # 检查金叉：快线从下方穿越到上方
        golden_cross = (prev_fast_ma <= prev_slow_ma and fast_ma > slow_ma)

        # 检查死叉：快线从上方穿越到下方
        death_cross = (prev_fast_ma >= prev_slow_ma and fast_ma < slow_ma)

        # 金叉买入信号
        if golden_cross and current_quantity == 0 and self.last_signal[symbol] != 'BUY':
            self.generate_signal(symbol, 'BUY', 0.7, {
                'fast_ma': fast_ma,
                'slow_ma': slow_ma,
                'signal_type': 'golden_cross'
            })

            # 执行买入
            cash = self.get_cash()
            if cash > 1000:  # 确保有足够现金
                position_value = cash * self.position_size
                quantity = position_value / current_price

                if quantity > 0:
                    self.buy_market(symbol, quantity)
                    self.last_signal[symbol] = 'BUY'

        # 死叉卖出信号
        elif death_cross and current_quantity > 0 and self.last_signal[symbol] != 'SELL':
            self.generate_signal(symbol, 'SELL', 0.7, {
                'fast_ma': fast_ma,
                'slow_ma': slow_ma,
                'signal_type': 'death_cross'
            })

            # 执行卖出
            self.sell_market(symbol, current_quantity)
            self.last_signal[symbol] = 'SELL'
    
    def on_end(self):
        """策略结束"""
        self.end_time = datetime.now()
        self.log_info("移动平均策略结束")
        
        # 平仓
        for symbol in self.symbols:
            position = self.get_position(symbol)
            if position and position.quantity > 0:
                self.sell_market(symbol, position.quantity)


class RSIStrategy(BaseStrategy):
    """RSI策略"""
    
    def __init__(self, name: str = "RSI_Strategy",
                 rsi_period: int = 14,
                 oversold_threshold: float = 30,
                 overbought_threshold: float = 70,
                 position_size: float = 0.15):
        super().__init__(name)
        
        self.rsi_period = rsi_period
        self.oversold_threshold = oversold_threshold
        self.overbought_threshold = overbought_threshold
        self.position_size = position_size
        
        # 价格历史
        self.price_history = {}
        self.max_history = rsi_period + 20
    
    def on_start(self):
        """策略开始"""
        self.start_time = datetime.now()
        self.log_info(f"RSI策略开始 (周期: {self.rsi_period}, 超卖: {self.oversold_threshold}, 超买: {self.overbought_threshold})")
        
        for symbol in self.symbols:
            self.price_history[symbol] = deque(maxlen=self.max_history)
    
    def on_market_data(self, event: MarketDataEvent):
        """处理市场数据"""
        self.update_market_data(event)
        
        symbol = event.symbol
        price = event.close
        
        # 更新价格历史
        if symbol not in self.price_history:
            self.price_history[symbol] = deque(maxlen=self.max_history)
        
        self.price_history[symbol].append(price)
        
        # 计算RSI
        if len(self.price_history[symbol]) >= self.rsi_period + 1:
            prices = list(self.price_history[symbol])
            rsi = TechnicalIndicators.rsi(prices, self.rsi_period)
            
            if rsi is not None:
                self._check_rsi_signals(symbol, price, rsi)
    
    def _check_rsi_signals(self, symbol: str, current_price: float, rsi: float):
        """检查RSI信号"""
        position = self.get_position(symbol)
        current_quantity = position.quantity if position else 0
        
        # 超卖信号：RSI < 30，买入
        if rsi < self.oversold_threshold and current_quantity <= 0:
            self.generate_signal(symbol, 'BUY', 0.8, {
                'rsi': rsi,
                'signal_type': 'oversold'
            })
            
            # 执行买入
            cash = self.get_cash()
            position_value = cash * self.position_size
            quantity = position_value / current_price
            
            if quantity > 0:
                self.buy_market(symbol, quantity)
        
        # 超买信号：RSI > 70，卖出
        elif rsi > self.overbought_threshold and current_quantity > 0:
            self.generate_signal(symbol, 'SELL', 0.8, {
                'rsi': rsi,
                'signal_type': 'overbought'
            })
            
            # 执行卖出
            self.sell_market(symbol, current_quantity)
    
    def on_end(self):
        """策略结束"""
        self.end_time = datetime.now()
        self.log_info("RSI策略结束")
        
        # 平仓
        for symbol in self.symbols:
            position = self.get_position(symbol)
            if position and position.quantity > 0:
                self.sell_market(symbol, position.quantity)


class BollingerBandsStrategy(BaseStrategy):
    """布林带策略"""
    
    def __init__(self, name: str = "BB_Strategy",
                 bb_period: int = 20,
                 bb_std: float = 2.0,
                 position_size: float = 0.15):
        super().__init__(name)
        
        self.bb_period = bb_period
        self.bb_std = bb_std
        self.position_size = position_size
        
        # 价格历史
        self.price_history = {}
        self.max_history = bb_period + 10
    
    def on_start(self):
        """策略开始"""
        self.start_time = datetime.now()
        self.log_info(f"布林带策略开始 (周期: {self.bb_period}, 标准差: {self.bb_std})")
        
        for symbol in self.symbols:
            self.price_history[symbol] = deque(maxlen=self.max_history)
    
    def on_market_data(self, event: MarketDataEvent):
        """处理市场数据"""
        self.update_market_data(event)
        
        symbol = event.symbol
        price = event.close
        
        # 更新价格历史
        if symbol not in self.price_history:
            self.price_history[symbol] = deque(maxlen=self.max_history)
        
        self.price_history[symbol].append(price)
        
        # 计算布林带
        if len(self.price_history[symbol]) >= self.bb_period:
            prices = list(self.price_history[symbol])
            bb = TechnicalIndicators.bollinger_bands(prices, self.bb_period, self.bb_std)
            
            if all(v is not None for v in bb.values()):
                self._check_bb_signals(symbol, price, bb)
    
    def _check_bb_signals(self, symbol: str, current_price: float, bb: Dict[str, float]):
        """检查布林带信号"""
        position = self.get_position(symbol)
        current_quantity = position.quantity if position else 0
        
        upper_band = bb['upper']
        lower_band = bb['lower']
        middle_band = bb['middle']
        
        # 价格触及下轨，买入信号
        if current_price <= lower_band and current_quantity <= 0:
            self.generate_signal(symbol, 'BUY', 0.7, {
                'price': current_price,
                'lower_band': lower_band,
                'signal_type': 'lower_band_touch'
            })
            
            # 执行买入
            cash = self.get_cash()
            position_value = cash * self.position_size
            quantity = position_value / current_price
            
            if quantity > 0:
                self.buy_market(symbol, quantity)
        
        # 价格触及上轨，卖出信号
        elif current_price >= upper_band and current_quantity > 0:
            self.generate_signal(symbol, 'SELL', 0.7, {
                'price': current_price,
                'upper_band': upper_band,
                'signal_type': 'upper_band_touch'
            })
            
            # 执行卖出
            self.sell_market(symbol, current_quantity)
    
    def on_end(self):
        """策略结束"""
        self.end_time = datetime.now()
        self.log_info("布林带策略结束")
        
        # 平仓
        for symbol in self.symbols:
            position = self.get_position(symbol)
            if position and position.quantity > 0:
                self.sell_market(symbol, position.quantity)
