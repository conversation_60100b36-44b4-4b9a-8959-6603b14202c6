"""
组合策略

结合多种策略的组合策略实现。
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
import numpy as np

from .base_strategy import BaseStrategy
from .ai_strategy import AIStrategy
from .technical_strategy import MovingAverageStrategy, RSIStrategy, BollingerBandsStrategy
from ..core.events import MarketDataEvent, SignalEvent


class MultiStrategyEnsemble(BaseStrategy):
    """多策略集成"""
    
    def __init__(self, name: str = "MultiStrategy",
                 strategy_configs: List[Dict] = None,
                 voting_method: str = "weighted",
                 min_agreement: float = 0.6):
        super().__init__(name)
        
        self.voting_method = voting_method  # "weighted", "majority", "unanimous"
        self.min_agreement = min_agreement
        
        # 默认策略配置
        if strategy_configs is None:
            strategy_configs = [
                {"type": "MA", "weight": 0.3, "params": {"fast_period": 10, "slow_period": 20}},
                {"type": "RSI", "weight": 0.3, "params": {"rsi_period": 14}},
                {"type": "BB", "weight": 0.2, "params": {"bb_period": 20}},
                {"type": "AI", "weight": 0.2, "params": {"model_type": "professional"}}
            ]
        
        self.strategy_configs = strategy_configs
        self.sub_strategies = []
        self.strategy_weights = {}
        
        # 信号历史
        self.signal_history = {}
        
        # 初始化子策略
        self._initialize_strategies()
    
    def _initialize_strategies(self):
        """初始化子策略"""
        for i, config in enumerate(self.strategy_configs):
            strategy_type = config["type"]
            weight = config.get("weight", 1.0)
            params = config.get("params", {})
            
            strategy_name = f"{strategy_type}_{i}"
            
            try:
                if strategy_type == "MA":
                    strategy = MovingAverageStrategy(
                        name=strategy_name,
                        fast_period=params.get("fast_period", 10),
                        slow_period=params.get("slow_period", 20),
                        position_size=0.05  # 较小仓位
                    )
                elif strategy_type == "RSI":
                    strategy = RSIStrategy(
                        name=strategy_name,
                        rsi_period=params.get("rsi_period", 14),
                        position_size=0.05
                    )
                elif strategy_type == "BB":
                    strategy = BollingerBandsStrategy(
                        name=strategy_name,
                        bb_period=params.get("bb_period", 20),
                        position_size=0.05
                    )
                elif strategy_type == "AI":
                    strategy = AIStrategy(
                        name=strategy_name,
                        model_type=params.get("model_type", "professional"),
                        position_size=0.05
                    )
                else:
                    self.log_warning(f"未知策略类型: {strategy_type}")
                    continue
                
                self.sub_strategies.append(strategy)
                self.strategy_weights[strategy_name] = weight
                
                self.log_info(f"初始化子策略: {strategy_name} (权重: {weight})")
                
            except Exception as e:
                self.log_error(f"初始化策略失败 {strategy_type}: {e}")
    
    def set_engine(self, engine):
        """设置回测引擎"""
        super().set_engine(engine)
        for strategy in self.sub_strategies:
            strategy.set_engine(engine)
    
    def set_symbols(self, symbols: List[str]):
        """设置交易对"""
        super().set_symbols(symbols)
        for strategy in self.sub_strategies:
            strategy.set_symbols(symbols)
        
        # 初始化信号历史
        for symbol in symbols:
            self.signal_history[symbol] = []
    
    def on_start(self):
        """策略开始"""
        self.start_time = datetime.now()
        self.log_info(f"多策略集成开始 (子策略数: {len(self.sub_strategies)})")
        
        # 启动所有子策略
        for strategy in self.sub_strategies:
            strategy.on_start()
    
    def on_market_data(self, event: MarketDataEvent):
        """处理市场数据"""
        self.update_market_data(event)
        
        # 让所有子策略处理数据
        for strategy in self.sub_strategies:
            strategy.on_market_data(event)
        
        # 收集和分析信号
        self._analyze_signals(event.symbol)
    
    def _analyze_signals(self, symbol: str):
        """分析子策略信号"""
        current_signals = []
        
        # 收集所有子策略的最新信号
        for strategy in self.sub_strategies:
            signal = strategy.get_latest_signal(symbol)
            if signal:
                weight = self.strategy_weights.get(strategy.name, 1.0)
                current_signals.append({
                    'strategy': strategy.name,
                    'signal': signal,
                    'weight': weight
                })
        
        if current_signals:
            # 根据投票方法决定最终信号
            final_signal = self._vote_on_signals(current_signals)
            
            if final_signal:
                self._execute_ensemble_decision(symbol, final_signal)
    
    def _vote_on_signals(self, signals: List[Dict]) -> Optional[Dict]:
        """对信号进行投票"""
        if not signals:
            return None
        
        if self.voting_method == "weighted":
            return self._weighted_voting(signals)
        elif self.voting_method == "majority":
            return self._majority_voting(signals)
        elif self.voting_method == "unanimous":
            return self._unanimous_voting(signals)
        else:
            return self._weighted_voting(signals)
    
    def _weighted_voting(self, signals: List[Dict]) -> Optional[Dict]:
        """加权投票"""
        buy_weight = 0.0
        sell_weight = 0.0
        hold_weight = 0.0
        total_weight = 0.0
        
        for signal_data in signals:
            signal = signal_data['signal']
            weight = signal_data['weight']
            strength = signal.strength
            
            weighted_strength = weight * strength
            total_weight += weight
            
            if signal.signal_type == 'BUY':
                buy_weight += weighted_strength
            elif signal.signal_type == 'SELL':
                sell_weight += weighted_strength
            else:
                hold_weight += weighted_strength
        
        if total_weight == 0:
            return None
        
        # 标准化权重
        buy_score = buy_weight / total_weight
        sell_score = sell_weight / total_weight
        hold_score = hold_weight / total_weight
        
        # 确定最终信号
        max_score = max(buy_score, sell_score, hold_score)
        
        if max_score < self.min_agreement:
            return None
        
        if max_score == buy_score:
            signal_type = 'BUY'
        elif max_score == sell_score:
            signal_type = 'SELL'
        else:
            signal_type = 'HOLD'
        
        return {
            'signal_type': signal_type,
            'confidence': max_score,
            'buy_score': buy_score,
            'sell_score': sell_score,
            'hold_score': hold_score,
            'contributing_signals': len(signals)
        }
    
    def _majority_voting(self, signals: List[Dict]) -> Optional[Dict]:
        """多数投票"""
        buy_count = 0
        sell_count = 0
        hold_count = 0
        
        for signal_data in signals:
            signal = signal_data['signal']
            
            if signal.signal_type == 'BUY':
                buy_count += 1
            elif signal.signal_type == 'SELL':
                sell_count += 1
            else:
                hold_count += 1
        
        total_count = len(signals)
        majority_threshold = total_count * self.min_agreement
        
        if buy_count >= majority_threshold:
            signal_type = 'BUY'
            confidence = buy_count / total_count
        elif sell_count >= majority_threshold:
            signal_type = 'SELL'
            confidence = sell_count / total_count
        elif hold_count >= majority_threshold:
            signal_type = 'HOLD'
            confidence = hold_count / total_count
        else:
            return None
        
        return {
            'signal_type': signal_type,
            'confidence': confidence,
            'buy_count': buy_count,
            'sell_count': sell_count,
            'hold_count': hold_count
        }
    
    def _unanimous_voting(self, signals: List[Dict]) -> Optional[Dict]:
        """一致投票"""
        if not signals:
            return None
        
        first_signal_type = signals[0]['signal'].signal_type
        
        # 检查是否所有信号一致
        for signal_data in signals:
            if signal_data['signal'].signal_type != first_signal_type:
                return None
        
        # 计算平均置信度
        avg_confidence = sum(s['signal'].strength for s in signals) / len(signals)
        
        return {
            'signal_type': first_signal_type,
            'confidence': avg_confidence,
            'unanimous': True
        }
    
    def _execute_ensemble_decision(self, symbol: str, decision: Dict):
        """执行集成决策"""
        signal_type = decision['signal_type']
        confidence = decision['confidence']
        
        if signal_type == 'HOLD':
            return
        
        # 生成集成信号
        self.generate_signal(symbol, signal_type, confidence, decision)
        
        # 执行交易
        position = self.get_position(symbol)
        current_quantity = position.quantity if position else 0
        current_price = self.get_current_price(symbol)
        
        if not current_price:
            return
        
        # 计算仓位大小（基于置信度调整）
        base_position_size = 0.1
        adjusted_position_size = base_position_size * confidence
        
        if signal_type == 'BUY' and current_quantity <= 0:
            cash = self.get_cash()
            position_value = cash * adjusted_position_size
            quantity = position_value / current_price
            
            if quantity > 0:
                self.buy_market(symbol, quantity)
                self.log_info(f"集成买入 {symbol}: {quantity:.6f} (置信度: {confidence:.2f})")
        
        elif signal_type == 'SELL' and current_quantity > 0:
            self.sell_market(symbol, current_quantity)
            self.log_info(f"集成卖出 {symbol}: {current_quantity:.6f} (置信度: {confidence:.2f})")
    
    def on_end(self):
        """策略结束"""
        self.end_time = datetime.now()
        self.log_info("多策略集成结束")
        
        # 结束所有子策略
        for strategy in self.sub_strategies:
            strategy.on_end()
        
        # 平仓
        for symbol in self.symbols:
            position = self.get_position(symbol)
            if position and position.quantity > 0:
                self.sell_market(symbol, position.quantity)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        stats = super().get_statistics()
        
        # 添加子策略统计
        sub_strategy_stats = {}
        for strategy in self.sub_strategies:
            sub_strategy_stats[strategy.name] = strategy.get_statistics()
        
        stats.update({
            'voting_method': self.voting_method,
            'min_agreement': self.min_agreement,
            'sub_strategies': sub_strategy_stats,
            'strategy_weights': self.strategy_weights
        })
        
        return stats
