"""
策略基类

定义交易策略的基本接口和通用功能。
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

from ..core.events import MarketDataEvent, SignalEvent


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.engine = None
        self.symbols = []
        self.is_active = True
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        # 策略状态
        self.positions = {}  # symbol -> position_size
        self.signals = {}    # symbol -> latest_signal
        self.market_data = {}  # symbol -> latest_market_data
        
        # 策略参数
        self.parameters = {}
        
        # 统计信息
        self.total_signals = 0
        self.total_trades = 0
        self.start_time = None
        self.end_time = None


    
    def set_engine(self, engine):
        """设置回测引擎"""
        self.engine = engine
    
    def set_symbols(self, symbols: List[str]):
        """设置交易对"""
        self.symbols = symbols
        for symbol in symbols:
            self.positions[symbol] = 0.0
            self.signals[symbol] = None
            self.market_data[symbol] = None
    
    def set_parameters(self, parameters: Dict[str, Any]):
        """设置策略参数"""
        self.parameters.update(parameters)
    
    def get_parameter(self, key: str, default=None):
        """获取策略参数"""
        return self.parameters.get(key, default)
    
    @abstractmethod
    def on_start(self):
        """策略开始时调用"""
        pass
    
    @abstractmethod
    def on_market_data(self, event: MarketDataEvent):
        """处理市场数据"""
        pass
    
    @abstractmethod
    def on_end(self):
        """策略结束时调用"""
        pass
    
    def on_signal(self, event: SignalEvent):
        """处理交易信号（可选重写）"""
        pass
    
    def place_order(self, symbol: str, order_type: str, quantity: float,
                   direction: str, price: Optional[float] = None):
        """下单"""
        if not self.is_active:
            return

        if self.engine:
            # 对于买入订单，严格检查现金是否足够
            if direction == 'BUY':
                # 直接检查投资组合的实时现金
                available_cash = self.engine.portfolio.cash
                current_price = price or self.get_current_price(symbol)
                required_cash = quantity * current_price * 1.02  # 加2%缓冲

                # 严格的现金检查：现金必须为正数且足够支付订单
                if available_cash <= 0:
                    self.logger.warning(f"现金为负或零，停止买入: 可用现金 ${available_cash:.2f}")
                    return

                if available_cash < required_cash:
                    self.logger.warning(f"现金不足，跳过买入: 需要 ${required_cash:.2f}, 可用 ${available_cash:.2f}")
                    return

                if available_cash < 1000:  # 保留最低现金缓冲
                    self.logger.warning(f"现金过低，停止买入: 可用现金 ${available_cash:.2f}")
                    return

            self.engine.place_order(symbol, order_type, quantity, direction, price)
            self.total_trades += 1
            self.logger.info(f"下单: {symbol} {direction} {quantity} @ {price or 'MARKET'}")
    
    def buy_market(self, symbol: str, quantity: float):
        """市价买入"""
        self.place_order(symbol, 'MARKET', quantity, 'BUY')
    
    def sell_market(self, symbol: str, quantity: float):
        """市价卖出"""
        self.place_order(symbol, 'MARKET', quantity, 'SELL')
    
    def buy_limit(self, symbol: str, quantity: float, price: float):
        """限价买入"""
        self.place_order(symbol, 'LIMIT', quantity, 'BUY', price)
    
    def sell_limit(self, symbol: str, quantity: float, price: float):
        """限价卖出"""
        self.place_order(symbol, 'LIMIT', quantity, 'SELL', price)
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        if self.engine:
            return self.engine.get_current_price(symbol)
        return None
    
    def get_position(self, symbol: str):
        """获取持仓"""
        if self.engine:
            return self.engine.get_position(symbol)
        return None
    
    def get_portfolio_value(self) -> float:
        """获取投资组合价值"""
        if self.engine:
            return self.engine.get_portfolio_value()
        return 0.0
    
    def get_cash(self) -> float:
        """获取现金（直接从投资组合获取实时数据）"""
        if self.engine:
            return self.engine.portfolio.cash
        return 0.0

    def get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        if symbol in self.market_data:
            return self.market_data[symbol].close
        return 0.0


    
    def generate_signal(self, symbol: str, signal_type: str, strength: float, 
                       metadata: Optional[Dict[str, Any]] = None):
        """生成交易信号"""
        if not self.is_active:
            return
        
        signal = SignalEvent(
            timestamp=datetime.now(),
            symbol=symbol,
            signal_type=signal_type,
            strength=strength,
            metadata=metadata or {}
        )
        
        self.signals[symbol] = signal
        self.total_signals += 1
        
        # 处理信号
        self.on_signal(signal)
        
        self.logger.info(f"生成信号: {symbol} {signal_type} (强度: {strength:.2f})")
    
    def update_market_data(self, event: MarketDataEvent):
        """更新市场数据"""
        self.market_data[event.symbol] = event
    
    def get_latest_signal(self, symbol: str) -> Optional[SignalEvent]:
        """获取最新信号"""
        return self.signals.get(symbol)
    
    def get_latest_market_data(self, symbol: str) -> Optional[MarketDataEvent]:
        """获取最新市场数据"""
        return self.market_data.get(symbol)
    
    def activate(self):
        """激活策略"""
        self.is_active = True
        self.logger.info(f"策略 {self.name} 已激活")
    
    def deactivate(self):
        """停用策略"""
        self.is_active = False
        self.logger.info(f"策略 {self.name} 已停用")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        return {
            'name': self.name,
            'is_active': self.is_active,
            'symbols': self.symbols,
            'total_signals': self.total_signals,
            'total_trades': self.total_trades,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'parameters': self.parameters.copy()
        }
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def log_warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
    
    def log_error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)


class SimpleStrategy(BaseStrategy):
    """简单策略示例"""
    
    def __init__(self, name: str = "SimpleStrategy"):
        super().__init__(name)
        self.price_history = {}
        self.last_trade_time = {}  # 记录最后交易时间，避免重复交易
        self.min_trade_interval = 5  # 最小交易间隔（数据点）
    
    def on_start(self):
        """策略开始"""
        self.start_time = datetime.now()
        self.log_info("简单策略开始运行")
        
        for symbol in self.symbols:
            self.price_history[symbol] = []
            self.last_trade_time[symbol] = -1
    
    def on_market_data(self, event: MarketDataEvent):
        """处理市场数据"""
        self.update_market_data(event)

        # 调试：记录策略调用
        if not hasattr(self, 'call_count'):
            self.call_count = 0
        self.call_count += 1

        # 记录价格历史
        symbol = event.symbol
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append(event.close)
        
        # 保持最近100个价格
        if len(self.price_history[symbol]) > 100:
            self.price_history[symbol] = self.price_history[symbol][-100:]
        
        # 简单的移动平均策略（使用更短的周期和更敏感的阈值）
        if len(self.price_history[symbol]) >= 10:
            recent_prices = self.price_history[symbol][-10:]  # 使用10日均线
            ma_10 = sum(recent_prices) / len(recent_prices)
            current_price = event.close

            position = self.get_position(symbol)
            current_quantity = position.quantity if position else 0

            # 买入信号：价格突破10日均线（更敏感的阈值）
            if current_price > ma_10 * 1.002 and current_quantity <= 0:
                # 检查交易间隔，避免重复交易
                current_time = len(self.price_history[symbol])
                if current_time - self.last_trade_time[symbol] < self.min_trade_interval:
                    return  # 跳过，交易间隔太短

                # 检查可用现金
                cash = self.get_cash()
                portfolio_cash = self.engine.portfolio.cash if self.engine else 0
                self.log_info(f"现金检查 (调用#{self.call_count}): get_cash()=${cash:.2f}, portfolio.cash=${portfolio_cash:.2f}")

                if cash > 5000:  # 至少需要5000美元现金（提高门槛）
                    self.generate_signal(symbol, 'BUY', 0.7, {'ma_10': ma_10, 'price': current_price})

                    # 计算买入数量（使用10%的可用现金，降低仓位）
                    quantity = (cash * 0.10) / current_price
                    if quantity > 0:
                        self.buy_market(symbol, quantity)
                        self.last_trade_time[symbol] = current_time  # 记录交易时间
                        self.log_info(f"买入信号: 价格 {current_price:.2f} > MA10 {ma_10:.2f} (+{((current_price/ma_10-1)*100):.3f}%), 数量: {quantity:.6f}, 现金: ${cash:.2f}")
                else:
                    self.log_info(f"现金不足，跳过买入信号: 现金 ${cash:.2f}")

            # 卖出信号：价格跌破10日均线（更敏感的阈值）
            elif current_price < ma_10 * 0.998 and current_quantity > 0:
                # 检查交易间隔
                current_time = len(self.price_history[symbol])
                if current_time - self.last_trade_time[symbol] >= self.min_trade_interval:
                    self.generate_signal(symbol, 'SELL', 0.7, {'ma_10': ma_10, 'price': current_price})
                    self.sell_market(symbol, abs(current_quantity))
                    self.last_trade_time[symbol] = current_time  # 记录交易时间
                    self.log_info(f"卖出信号: 价格 {current_price:.2f} < MA10 {ma_10:.2f} ({((current_price/ma_10-1)*100):.3f}%), 数量: {abs(current_quantity):.6f}")

            # 记录价格和均线信息（每50个数据点记录一次）
            if len(self.price_history[symbol]) % 50 == 0:
                self.log_info(f"价格监控: {symbol} 当前价格 {current_price:.2f}, MA10 {ma_10:.2f}, 持仓 {current_quantity:.6f}")
    
    def on_end(self):
        """策略结束"""
        self.end_time = datetime.now()
        self.log_info("简单策略运行结束")
        
        # 平仓所有持仓
        for symbol in self.symbols:
            position = self.get_position(symbol)
            if position and position.quantity != 0:
                self.sell_market(symbol, abs(position.quantity))
                self.log_info(f"平仓 {symbol}: {position.quantity}")
