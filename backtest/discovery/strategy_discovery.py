"""
策略发现系统

自动发现和评估有效的交易策略，帮助用户找到最优的策略组合。
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from dataclasses import dataclass
from enum import Enum
import itertools
import random

from ..core.engine import BacktestEngine
from ..strategies.parametric_strategy import ParametricStrategy, ParametricStrategyConfig
from ..strategies.strategy_templates import StrategyTemplates
from ..strategies.advanced_strategy_builder import AdvancedStrategyBuilder
from ..optimization import ParameterOptimizer, OptimizationMethod, ParameterRange, OptimizationConfig


class DiscoveryMethod(Enum):
    """发现方法"""
    TEMPLATE_COMBINATION = "template_combination"  # 模板组合
    INDICATOR_EXPLORATION = "indicator_exploration"  # 指标探索
    PARAMETER_SWEEP = "parameter_sweep"  # 参数扫描
    GENETIC_EVOLUTION = "genetic_evolution"  # 遗传进化


@dataclass
class StrategyCandidate:
    """策略候选"""
    name: str
    config: ParametricStrategyConfig
    score: float
    metrics: Dict[str, float]
    discovery_method: str
    generation: int = 0


@dataclass
class DiscoveryConfig:
    """发现配置"""
    methods: List[DiscoveryMethod]
    symbols: List[str]
    start_date: datetime
    end_date: datetime
    validation_start_date: Optional[datetime] = None
    validation_end_date: Optional[datetime] = None
    max_candidates: int = 100
    min_score_threshold: float = 0.0
    parallel_workers: int = 4
    objective_function: str = "sharpe_ratio"


class StrategyDiscovery:
    """策略发现器"""
    
    def __init__(self, initial_capital: float = 100000.0):
        self.initial_capital = initial_capital
        self.logger = logging.getLogger(__name__)
        self.strategy_builder = AdvancedStrategyBuilder()
        
        # 发现历史
        self.discovery_history = []
        self.best_strategies = []
        
    def discover_strategies(self, config: DiscoveryConfig) -> List[StrategyCandidate]:
        """发现策略"""
        self.logger.info("开始策略发现...")
        
        all_candidates = []
        
        for method in config.methods:
            self.logger.info(f"使用方法: {method.value}")
            
            if method == DiscoveryMethod.TEMPLATE_COMBINATION:
                candidates = self._discover_by_template_combination(config)
            elif method == DiscoveryMethod.INDICATOR_EXPLORATION:
                candidates = self._discover_by_indicator_exploration(config)
            elif method == DiscoveryMethod.PARAMETER_SWEEP:
                candidates = self._discover_by_parameter_sweep(config)
            elif method == DiscoveryMethod.GENETIC_EVOLUTION:
                candidates = self._discover_by_genetic_evolution(config)
            else:
                continue
            
            all_candidates.extend(candidates)
        
        # 去重和排序
        unique_candidates = self._deduplicate_candidates(all_candidates)
        sorted_candidates = sorted(unique_candidates, key=lambda x: x.score, reverse=True)
        
        # 限制数量
        final_candidates = sorted_candidates[:config.max_candidates]
        
        # 验证最佳策略
        if config.validation_start_date and config.validation_end_date:
            final_candidates = self._validate_strategies(final_candidates, config)
        
        # 保存发现历史
        self.discovery_history.append({
            'timestamp': datetime.now(),
            'config': config,
            'candidates_found': len(final_candidates),
            'best_score': final_candidates[0].score if final_candidates else 0
        })
        
        self.best_strategies = final_candidates
        return final_candidates
    
    def _discover_by_template_combination(self, config: DiscoveryConfig) -> List[StrategyCandidate]:
        """通过模板组合发现策略"""
        candidates = []
        templates = StrategyTemplates.get_all_templates()
        
        # 测试每个模板的不同参数组合
        for template_name, template_config in templates.items():
            # 生成参数变体
            variants = self._generate_template_variants(template_config, max_variants=10)
            
            for i, variant in enumerate(variants):
                try:
                    strategy_config = ParametricStrategyConfig.from_dict(variant)
                    score, metrics = self._evaluate_strategy(strategy_config, config)
                    
                    if score >= config.min_score_threshold:
                        candidate = StrategyCandidate(
                            name=f"{template_name}_variant_{i}",
                            config=strategy_config,
                            score=score,
                            metrics=metrics,
                            discovery_method="template_combination"
                        )
                        candidates.append(candidate)
                        
                except Exception as e:
                    self.logger.warning(f"评估模板变体失败 {template_name}_{i}: {e}")
        
        return candidates
    
    def _discover_by_indicator_exploration(self, config: DiscoveryConfig) -> List[StrategyCandidate]:
        """通过指标探索发现策略"""
        candidates = []
        indicator_library = self.strategy_builder.get_indicator_library()
        
        # 生成指标组合
        indicator_names = list(indicator_library.keys())
        
        # 测试不同的指标组合
        for combo_size in range(1, min(4, len(indicator_names) + 1)):
            for indicator_combo in itertools.combinations(indicator_names, combo_size):
                try:
                    # 创建基于指标组合的策略
                    blueprint = self.strategy_builder._create_combination_blueprint(list(indicator_combo))
                    strategy_config = self.strategy_builder.build_strategy_from_blueprint(blueprint)
                    
                    score, metrics = self._evaluate_strategy(strategy_config, config)
                    
                    if score >= config.min_score_threshold:
                        candidate = StrategyCandidate(
                            name=f"indicator_combo_{'_'.join(indicator_combo)}",
                            config=strategy_config,
                            score=score,
                            metrics=metrics,
                            discovery_method="indicator_exploration"
                        )
                        candidates.append(candidate)
                        
                except Exception as e:
                    self.logger.warning(f"评估指标组合失败 {indicator_combo}: {e}")
        
        return candidates
    
    def _discover_by_parameter_sweep(self, config: DiscoveryConfig) -> List[StrategyCandidate]:
        """通过参数扫描发现策略"""
        candidates = []
        
        # 选择一个基础策略进行参数扫描
        base_template = StrategyTemplates.get_dual_moving_average_template()
        base_config = ParametricStrategyConfig.from_dict(base_template)
        
        # 定义参数范围
        parameter_ranges = [
            ParameterRange("fast_period", 5, 30, step=5, param_type="int"),
            ParameterRange("slow_period", 15, 60, step=5, param_type="int"),
            ParameterRange("stop_loss_pct", 2.0, 10.0, step=1.0, param_type="float"),
            ParameterRange("take_profit_pct", 5.0, 20.0, step=2.5, param_type="float")
        ]
        
        # 创建优化器
        optimizer = ParameterOptimizer(
            base_strategy_config=base_config,
            symbols=config.symbols,
            start_date=config.start_date,
            end_date=config.end_date,
            initial_capital=self.initial_capital
        )
        
        # 执行参数优化
        optimization_config = OptimizationConfig(
            method=OptimizationMethod.GRID_SEARCH,
            parameter_ranges=parameter_ranges,
            objective_function=config.objective_function,
            max_iterations=50,
            parallel_workers=config.parallel_workers
        )
        
        try:
            result = optimizer.optimize(optimization_config)
            
            # 将优化结果转换为候选策略
            for i, result_item in enumerate(result.all_results[:20]):  # 取前20个结果
                if result_item['score'] >= config.min_score_threshold:
                    # 应用参数到配置
                    modified_config = self._apply_parameters_to_template(
                        base_template, result_item['parameters']
                    )
                    strategy_config = ParametricStrategyConfig.from_dict(modified_config)
                    
                    candidate = StrategyCandidate(
                        name=f"param_sweep_{i}",
                        config=strategy_config,
                        score=result_item['score'],
                        metrics={'optimized_score': result_item['score']},
                        discovery_method="parameter_sweep"
                    )
                    candidates.append(candidate)
                    
        except Exception as e:
            self.logger.error(f"参数扫描失败: {e}")
        
        return candidates
    
    def _discover_by_genetic_evolution(self, config: DiscoveryConfig) -> List[StrategyCandidate]:
        """通过遗传进化发现策略"""
        candidates = []
        
        # 初始种群
        population_size = 20
        generations = 5
        
        # 创建初始种群
        population = self._create_initial_population(population_size)
        
        for generation in range(generations):
            self.logger.info(f"遗传进化第 {generation + 1} 代")
            
            # 评估种群
            evaluated_population = []
            for individual in population:
                try:
                    score, metrics = self._evaluate_strategy(individual, config)
                    evaluated_population.append((individual, score, metrics))
                except Exception as e:
                    self.logger.warning(f"评估个体失败: {e}")
            
            # 选择最佳个体
            evaluated_population.sort(key=lambda x: x[1], reverse=True)
            best_individuals = evaluated_population[:population_size // 2]
            
            # 记录最佳候选
            for i, (individual, score, metrics) in enumerate(best_individuals[:5]):
                if score >= config.min_score_threshold:
                    candidate = StrategyCandidate(
                        name=f"genetic_gen{generation}_rank{i}",
                        config=individual,
                        score=score,
                        metrics=metrics,
                        discovery_method="genetic_evolution",
                        generation=generation
                    )
                    candidates.append(candidate)
            
            # 生成下一代
            if generation < generations - 1:
                population = self._evolve_population(best_individuals, population_size)
        
        return candidates
    
    def _generate_template_variants(self, template: Dict[str, Any], max_variants: int = 10) -> List[Dict[str, Any]]:
        """生成模板变体"""
        variants = []
        base_template = template.copy()
        
        for _ in range(max_variants):
            variant = base_template.copy()
            
            # 随机调整参数
            if 'indicators' in variant:
                for indicator in variant['indicators']:
                    if 'params' in indicator:
                        for param_name, param_value in indicator['params'].items():
                            if isinstance(param_value, (int, float)):
                                # 在原值的50%-150%范围内随机调整
                                factor = random.uniform(0.5, 1.5)
                                new_value = param_value * factor
                                if isinstance(param_value, int):
                                    new_value = max(1, int(new_value))
                                indicator['params'][param_name] = new_value
            
            # 随机调整风险管理参数
            if 'risk_management' in variant:
                risk_params = variant['risk_management']
                if 'stop_loss_pct' in risk_params:
                    risk_params['stop_loss_pct'] = random.uniform(1.0, 15.0)
                if 'take_profit_pct' in risk_params:
                    risk_params['take_profit_pct'] = random.uniform(3.0, 30.0)
                if 'max_position_size' in risk_params:
                    risk_params['max_position_size'] = random.uniform(0.05, 0.5)
            
            variants.append(variant)
        
        return variants
    
    def _evaluate_strategy(self, strategy_config: ParametricStrategyConfig, 
                          config: DiscoveryConfig) -> Tuple[float, Dict[str, float]]:
        """评估策略"""
        try:
            # 创建回测引擎
            engine = BacktestEngine(initial_capital=self.initial_capital)
            engine.set_symbols(config.symbols)
            engine.set_timeframe(config.start_date, config.end_date)
            
            # 创建策略
            strategy = ParametricStrategy(strategy_config)
            strategy.set_symbols(config.symbols)
            engine.add_strategy(strategy)
            
            # 运行回测
            result = engine.run_backtest()
            
            # 提取评估指标
            portfolio_metrics = result['portfolio']
            
            if config.objective_function == "sharpe_ratio":
                score = portfolio_metrics.get('sharpe_ratio', 0)
            elif config.objective_function == "total_return":
                score = portfolio_metrics.get('total_return', 0)
            elif config.objective_function == "max_drawdown":
                score = -portfolio_metrics.get('max_drawdown', 100)  # 负值，因为要最小化回撤
            else:
                score = portfolio_metrics.get('total_return', 0)
            
            metrics = {
                'total_return': portfolio_metrics.get('total_return', 0),
                'sharpe_ratio': portfolio_metrics.get('sharpe_ratio', 0),
                'max_drawdown': portfolio_metrics.get('max_drawdown', 0),
                'win_rate': portfolio_metrics.get('win_rate', 0),
                'total_trades': portfolio_metrics.get('total_trades', 0)
            }
            
            return score, metrics
            
        except Exception as e:
            self.logger.error(f"策略评估失败: {e}")
            return float('-inf'), {}
    
    def _deduplicate_candidates(self, candidates: List[StrategyCandidate]) -> List[StrategyCandidate]:
        """去重候选策略"""
        # 简化的去重逻辑：基于策略名称和得分
        seen = set()
        unique_candidates = []
        
        for candidate in candidates:
            key = (candidate.name, round(candidate.score, 4))
            if key not in seen:
                seen.add(key)
                unique_candidates.append(candidate)
        
        return unique_candidates
    
    def _validate_strategies(self, candidates: List[StrategyCandidate], 
                           config: DiscoveryConfig) -> List[StrategyCandidate]:
        """验证策略在样本外数据上的表现"""
        validated_candidates = []
        
        for candidate in candidates:
            try:
                # 在验证期间评估策略
                validation_config = DiscoveryConfig(
                    methods=[],
                    symbols=config.symbols,
                    start_date=config.validation_start_date,
                    end_date=config.validation_end_date,
                    objective_function=config.objective_function
                )
                
                validation_score, validation_metrics = self._evaluate_strategy(
                    candidate.config, validation_config
                )
                
                # 更新候选策略的验证指标
                candidate.metrics['validation_score'] = validation_score
                candidate.metrics['validation_metrics'] = validation_metrics
                
                # 只保留验证表现良好的策略
                if validation_score >= config.min_score_threshold * 0.7:  # 允许一定的性能下降
                    validated_candidates.append(candidate)
                    
            except Exception as e:
                self.logger.warning(f"验证策略失败 {candidate.name}: {e}")
        
        return validated_candidates

    def _apply_parameters_to_template(self, template: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """将参数应用到策略模板"""
        import copy

        # 深拷贝模板以避免修改原始模板
        modified_template = copy.deepcopy(template)

        # 更新指标参数
        if 'indicators' in modified_template:
            for indicator in modified_template['indicators']:
                if 'params' in indicator:
                    for param_name, param_value in parameters.items():
                        # 直接参数名匹配
                        if param_name in indicator['params']:
                            indicator['params'][param_name] = param_value
                        # 带指标名前缀的参数
                        elif param_name.startswith(f"{indicator['name']}_"):
                            actual_param_name = param_name.replace(f"{indicator['name']}_", "")
                            if actual_param_name in indicator['params']:
                                indicator['params'][actual_param_name] = param_value

        # 更新风险管理参数
        if 'risk_management' in modified_template:
            risk_params = ['stop_loss_pct', 'take_profit_pct', 'max_position_size', 'position_sizing_method']
            for param_name in risk_params:
                if param_name in parameters:
                    modified_template['risk_management'][param_name] = parameters[param_name]

        # 更新交易规则中的阈值参数
        for rule_type in ['buy_rules', 'sell_rules']:
            if rule_type in modified_template:
                for rule in modified_template[rule_type]:
                    if 'conditions' in rule:
                        for condition in rule['conditions']:
                            # 更新阈值参数
                            for param_name, param_value in parameters.items():
                                if param_name.endswith('_threshold'):
                                    threshold_type = param_name.replace('_threshold', '')
                                    if threshold_type in str(condition.get('left_operand', '')):
                                        condition['right_operand'] = param_value
                                elif param_name in ['oversold_threshold', 'overbought_threshold']:
                                    if 'rsi' in str(condition.get('left_operand', '')):
                                        if param_name == 'oversold_threshold' and condition.get('operator') == '<':
                                            condition['right_operand'] = param_value
                                        elif param_name == 'overbought_threshold' and condition.get('operator') == '>':
                                            condition['right_operand'] = param_value

        return modified_template

    def _create_initial_population(self, population_size: int) -> List[ParametricStrategyConfig]:
        """创建初始种群"""
        population = []
        templates = StrategyTemplates.get_all_templates()

        for _ in range(population_size):
            # 随机选择一个模板
            template_name = random.choice(list(templates.keys()))
            template = templates[template_name]

            # 生成随机变体
            variant = self._generate_template_variants(template, max_variants=1)[0]

            try:
                strategy_config = ParametricStrategyConfig.from_dict(variant)
                population.append(strategy_config)
            except Exception as e:
                self.logger.warning(f"创建初始个体失败: {e}")
                # 使用原始模板作为备选
                try:
                    strategy_config = ParametricStrategyConfig.from_dict(template)
                    population.append(strategy_config)
                except Exception:
                    continue

        return population

    def _evolve_population(self, best_individuals: List[Tuple], population_size: int) -> List[ParametricStrategyConfig]:
        """进化种群"""
        new_population = []

        # 保留最佳个体
        for individual, score, metrics in best_individuals[:population_size // 4]:
            new_population.append(individual)

        # 生成新个体
        while len(new_population) < population_size:
            # 选择两个父代
            parent1 = random.choice(best_individuals)[0]
            parent2 = random.choice(best_individuals)[0]

            # 简化的交叉操作：随机选择父代的特征
            try:
                child_config = self._crossover_strategies(parent1, parent2)

                # 变异
                if random.random() < 0.1:  # 10%变异率
                    child_config = self._mutate_strategy(child_config)

                new_population.append(child_config)

            except Exception as e:
                self.logger.warning(f"进化操作失败: {e}")
                # 使用父代之一作为备选
                new_population.append(random.choice([parent1, parent2]))

        return new_population

    def _crossover_strategies(self, parent1: ParametricStrategyConfig, parent2: ParametricStrategyConfig) -> ParametricStrategyConfig:
        """策略交叉"""
        import copy

        # 简化的交叉：随机选择父代的指标和规则
        child = copy.deepcopy(parent1)

        # 随机选择父代2的一些指标
        if random.random() < 0.5 and parent2.indicators:
            child.indicators = random.choice([parent1.indicators, parent2.indicators])

        # 随机选择父代2的风险管理设置
        if random.random() < 0.5:
            child.risk_management = copy.deepcopy(parent2.risk_management)

        return child

    def _mutate_strategy(self, strategy: ParametricStrategyConfig) -> ParametricStrategyConfig:
        """策略变异"""
        import copy

        mutated = copy.deepcopy(strategy)

        # 随机调整指标参数
        for indicator in mutated.indicators:
            for param_name, param_value in indicator.params.items():
                if isinstance(param_value, (int, float)) and random.random() < 0.3:
                    # 30%概率变异参数
                    factor = random.uniform(0.8, 1.2)
                    new_value = param_value * factor
                    if isinstance(param_value, int):
                        new_value = max(1, int(new_value))
                    indicator.params[param_name] = new_value

        # 随机调整风险管理参数
        if random.random() < 0.2:  # 20%概率变异风险参数
            if mutated.risk_management.stop_loss_pct:
                mutated.risk_management.stop_loss_pct = random.uniform(1.0, 15.0)
            if mutated.risk_management.take_profit_pct:
                mutated.risk_management.take_profit_pct = random.uniform(3.0, 30.0)

        return mutated
