"""
参数优化器

提供多种参数优化算法，帮助用户找到最优的策略参数组合。
"""

from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime
import numpy as np
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from dataclasses import dataclass
from enum import Enum
import itertools
import random

from ..core.engine import BacktestEngine
from ..strategies.parametric_strategy import ParametricStrategy, ParametricStrategyConfig


class OptimizationMethod(Enum):
    """优化方法"""
    GRID_SEARCH = "grid_search"  # 网格搜索
    RANDOM_SEARCH = "random_search"  # 随机搜索
    GENETIC_ALGORITHM = "genetic_algorithm"  # 遗传算法
    BAYESIAN_OPTIMIZATION = "bayesian_optimization"  # 贝叶斯优化


@dataclass
class ParameterRange:
    """参数范围定义"""
    name: str
    min_value: float
    max_value: float
    step: Optional[float] = None
    values: Optional[List[Any]] = None  # 离散值列表
    param_type: str = "float"  # int, float, str, bool


@dataclass
class OptimizationConfig:
    """优化配置"""
    method: OptimizationMethod
    parameter_ranges: List[ParameterRange]
    objective_function: str = "sharpe_ratio"  # 优化目标
    max_iterations: int = 100
    population_size: int = 50  # 遗传算法种群大小
    mutation_rate: float = 0.1  # 遗传算法变异率
    crossover_rate: float = 0.8  # 遗传算法交叉率
    parallel_workers: int = 4  # 并行工作线程数


@dataclass
class OptimizationResult:
    """优化结果"""
    best_parameters: Dict[str, Any]
    best_score: float
    all_results: List[Dict[str, Any]]
    optimization_time: float
    iterations_completed: int


class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self, base_strategy_config: ParametricStrategyConfig,
                 symbols: List[str], start_date: datetime, end_date: datetime,
                 initial_capital: float = 100000.0):
        self.base_strategy_config = base_strategy_config
        self.symbols = symbols
        self.start_date = start_date
        self.end_date = end_date
        self.initial_capital = initial_capital
        self.logger = logging.getLogger(__name__)
        
        # 优化历史
        self.optimization_history = []
    
    def optimize(self, config: OptimizationConfig) -> OptimizationResult:
        """执行参数优化"""
        start_time = datetime.now()
        
        if config.method == OptimizationMethod.GRID_SEARCH:
            result = self._grid_search_optimization(config)
        elif config.method == OptimizationMethod.RANDOM_SEARCH:
            result = self._random_search_optimization(config)
        elif config.method == OptimizationMethod.GENETIC_ALGORITHM:
            result = self._genetic_algorithm_optimization(config)
        else:
            raise ValueError(f"不支持的优化方法: {config.method}")
        
        end_time = datetime.now()
        result.optimization_time = (end_time - start_time).total_seconds()
        
        # 保存优化历史
        self.optimization_history.append({
            'timestamp': start_time,
            'method': config.method.value,
            'result': result
        })
        
        return result
    
    def _grid_search_optimization(self, config: OptimizationConfig) -> OptimizationResult:
        """网格搜索优化"""
        self.logger.info("开始网格搜索优化...")
        
        # 生成参数组合
        parameter_combinations = self._generate_parameter_combinations(config.parameter_ranges)
        
        # 限制组合数量
        if len(parameter_combinations) > config.max_iterations:
            self.logger.warning(f"参数组合数量 {len(parameter_combinations)} 超过最大迭代次数 {config.max_iterations}，将随机采样")
            parameter_combinations = random.sample(parameter_combinations, config.max_iterations)
        
        # 并行评估
        results = self._evaluate_parameters_parallel(parameter_combinations, config)
        
        # 找到最佳结果
        best_result = max(results, key=lambda x: x['score'])
        
        return OptimizationResult(
            best_parameters=best_result['parameters'],
            best_score=best_result['score'],
            all_results=results,
            optimization_time=0,  # 将在外部设置
            iterations_completed=len(results)
        )
    
    def _random_search_optimization(self, config: OptimizationConfig) -> OptimizationResult:
        """随机搜索优化"""
        self.logger.info("开始随机搜索优化...")
        
        # 生成随机参数组合
        parameter_combinations = []
        for _ in range(config.max_iterations):
            params = self._generate_random_parameters(config.parameter_ranges)
            parameter_combinations.append(params)
        
        # 并行评估
        results = self._evaluate_parameters_parallel(parameter_combinations, config)
        
        # 找到最佳结果
        best_result = max(results, key=lambda x: x['score'])
        
        return OptimizationResult(
            best_parameters=best_result['parameters'],
            best_score=best_result['score'],
            all_results=results,
            optimization_time=0,
            iterations_completed=len(results)
        )
    
    def _genetic_algorithm_optimization(self, config: OptimizationConfig) -> OptimizationResult:
        """遗传算法优化"""
        self.logger.info("开始遗传算法优化...")
        
        # 初始化种群
        population = []
        for _ in range(config.population_size):
            individual = self._generate_random_parameters(config.parameter_ranges)
            population.append(individual)
        
        all_results = []
        best_score = float('-inf')
        best_parameters = None
        
        for generation in range(config.max_iterations // config.population_size):
            self.logger.info(f"遗传算法第 {generation + 1} 代")
            
            # 评估当前种群
            generation_results = self._evaluate_parameters_parallel(population, config)
            all_results.extend(generation_results)
            
            # 更新最佳结果
            generation_best = max(generation_results, key=lambda x: x['score'])
            if generation_best['score'] > best_score:
                best_score = generation_best['score']
                best_parameters = generation_best['parameters']
            
            # 选择、交叉、变异
            population = self._evolve_population(population, generation_results, config)
        
        return OptimizationResult(
            best_parameters=best_parameters,
            best_score=best_score,
            all_results=all_results,
            optimization_time=0,
            iterations_completed=len(all_results)
        )
    
    def _generate_parameter_combinations(self, parameter_ranges: List[ParameterRange]) -> List[Dict[str, Any]]:
        """生成所有参数组合"""
        param_lists = []
        param_names = []
        
        for param_range in parameter_ranges:
            param_names.append(param_range.name)
            
            if param_range.values is not None:
                # 使用预定义的值列表
                param_lists.append(param_range.values)
            else:
                # 根据范围生成值列表
                if param_range.param_type == "int":
                    step = param_range.step or 1
                    values = list(range(int(param_range.min_value), int(param_range.max_value) + 1, int(step)))
                elif param_range.param_type == "float":
                    step = param_range.step or (param_range.max_value - param_range.min_value) / 10
                    values = []
                    current = param_range.min_value
                    while current <= param_range.max_value:
                        values.append(current)
                        current += step
                else:
                    values = [param_range.min_value, param_range.max_value]
                
                param_lists.append(values)
        
        # 生成所有组合
        combinations = []
        for combination in itertools.product(*param_lists):
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _generate_random_parameters(self, parameter_ranges: List[ParameterRange]) -> Dict[str, Any]:
        """生成随机参数组合"""
        parameters = {}
        
        for param_range in parameter_ranges:
            if param_range.values is not None:
                # 从预定义值中随机选择
                parameters[param_range.name] = random.choice(param_range.values)
            else:
                # 在范围内随机生成
                if param_range.param_type == "int":
                    value = random.randint(int(param_range.min_value), int(param_range.max_value))
                elif param_range.param_type == "float":
                    value = random.uniform(param_range.min_value, param_range.max_value)
                elif param_range.param_type == "bool":
                    value = random.choice([True, False])
                else:
                    value = param_range.min_value
                
                parameters[param_range.name] = value
        
        return parameters
    
    def _evaluate_parameters_parallel(self, parameter_combinations: List[Dict[str, Any]], 
                                    config: OptimizationConfig) -> List[Dict[str, Any]]:
        """并行评估参数组合"""
        results = []
        
        with ThreadPoolExecutor(max_workers=config.parallel_workers) as executor:
            # 提交所有任务
            future_to_params = {
                executor.submit(self._evaluate_single_parameter_set, params, config.objective_function): params
                for params in parameter_combinations
            }
            
            # 收集结果
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                try:
                    score = future.result()
                    results.append({
                        'parameters': params,
                        'score': score
                    })
                except Exception as e:
                    self.logger.error(f"评估参数 {params} 时出错: {e}")
                    results.append({
                        'parameters': params,
                        'score': float('-inf')
                    })
        
        return results
    
    def _evaluate_single_parameter_set(self, parameters: Dict[str, Any], objective_function: str) -> float:
        """评估单个参数集"""
        try:
            # 创建策略配置副本并应用参数
            strategy_config = self._apply_parameters_to_config(parameters)
            
            # 创建回测引擎
            engine = BacktestEngine(initial_capital=self.initial_capital)
            engine.set_symbols(self.symbols)
            engine.set_timeframe(self.start_date, self.end_date)
            
            # 创建策略
            strategy = ParametricStrategy(strategy_config)
            strategy.set_symbols(self.symbols)
            engine.add_strategy(strategy)
            
            # 运行回测
            result = engine.run_backtest()
            
            # 计算目标函数值
            if objective_function == "sharpe_ratio":
                return result['portfolio']['sharpe_ratio']
            elif objective_function == "total_return":
                return result['portfolio']['total_return']
            elif objective_function == "max_drawdown":
                return -result['portfolio']['max_drawdown']  # 负值，因为我们要最小化回撤
            elif objective_function == "profit_factor":
                return result['portfolio'].get('profit_factor', 0)
            else:
                return result['portfolio']['total_return']
                
        except Exception as e:
            self.logger.error(f"回测执行失败: {e}")
            return float('-inf')
    
    def _apply_parameters_to_config(self, parameters: Dict[str, Any]) -> ParametricStrategyConfig:
        """将参数应用到策略配置"""
        import copy

        # 深拷贝配置以避免修改原始配置
        config_copy = copy.deepcopy(self.base_strategy_config)

        # 更新指标参数
        for indicator in config_copy.indicators:
            for param_name, param_value in parameters.items():
                if param_name.startswith(f"{indicator.name}_"):
                    actual_param_name = param_name.replace(f"{indicator.name}_", "")
                    indicator.params[actual_param_name] = param_value
                elif param_name in indicator.params:
                    # 直接参数名匹配
                    indicator.params[param_name] = param_value

        # 更新风险管理参数
        risk_params = {
            'stop_loss_pct': 'stop_loss_pct',
            'take_profit_pct': 'take_profit_pct',
            'max_position_size': 'max_position_size',
            'position_sizing_method': 'position_sizing_method'
        }

        for param_name, param_value in parameters.items():
            if param_name in risk_params:
                setattr(config_copy.risk_management, param_name, param_value)

        # 更新交易规则参数
        for param_name, param_value in parameters.items():
            if param_name.endswith('_threshold'):
                # 更新阈值参数
                for rule in config_copy.buy_rules + config_copy.sell_rules:
                    for condition in rule.conditions:
                        if param_name.replace('_threshold', '') in condition.left_operand:
                            condition.right_operand = param_value

        return config_copy
