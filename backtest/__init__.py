"""
加密货币回测系统

一个完整的加密货币交易策略回测平台，集成AI预测模型，
支持使用真实历史数据进行准确的策略回测和性能评估。

主要模块:
- core: 核心回测引擎
- strategies: 交易策略
- data: 数据管理
- analysis: 性能分析
- visualization: 可视化
- api: Web API接口
"""

__version__ = "1.0.0"
__author__ = "Crypto Backtest System"

# 导入核心类
from .core.engine import BacktestEngine
from .core.portfolio import Portfolio
from .core.order_manager import OrderManager
from .core.risk_manager import RiskManager

# 导入策略基类
from .strategies.base_strategy import BaseStrategy
from .strategies.ai_strategy import AIStrategy
# from .strategies.technical_strategy import TechnicalStrategy

# 导入数据管理
from .data.data_manager import DataManager

# 暂时注释掉未实现的模块
# from .data.historical_data import HistoricalDataManager
# from .analysis.performance_analyzer import PerformanceAnalyzer
# from .analysis.risk_analyzer import RiskAnalyzer

__all__ = [
    'BacktestEngine',
    'Portfolio', 
    'OrderManager',
    'RiskManager',
    'BaseStrategy',
    'AIStrategy',
    # 'TechnicalStrategy',
    'DataManager'
    # 'HistoricalDataManager',
    # 'PerformanceAnalyzer',
    # 'RiskAnalyzer'
]
