# 加密货币预测模型依赖包
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
matplotlib>=3.4.0
seaborn>=0.11.0
scipy>=1.7.0
joblib>=1.0.0
shap>=0.40.0

# 深度学习框架
torch>=1.9.0
# tensorflow>=2.6.0

# 数据处理和分析
tqdm>=4.62.0
jupyter>=1.0.0
ipython>=7.25.0

# 时间序列分析
statsmodels>=0.12.0

# 绘图增强
plotly>=5.0.0

# Web框架和API
flask>=2.0.0
flask-cors>=3.0.0
flask-socketio>=5.0.0
fastapi>=0.68.0
uvicorn>=0.15.0
websockets>=10.0

# 数据库和缓存
redis>=4.0.0

# 网络请求
requests>=2.25.0
aiohttp>=3.8.0

# 配置和环境
python-dotenv>=0.19.0
pydantic>=1.8.0
pydantic-settings>=2.0.0

# 日志和监控
loguru>=0.6.0

# 安全和认证
cryptography>=3.4.0
bcrypt>=3.2.0
passlib>=1.7.4
python-jose[cryptography]>=3.3.0
python-multipart>=0.0.5

# 数据库ORM
sqlalchemy>=1.4.0
alembic>=1.7.0

# 支付SDK
python-alipay-sdk>=3.0.0
wechatpayv3>=2.0.0
xmltodict>=0.13.0

# 工具
click>=8.0.0
