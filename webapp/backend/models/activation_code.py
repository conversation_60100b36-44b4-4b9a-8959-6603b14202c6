"""
激活码数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Enum, ForeignKey, Text, Numeric
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
import enum
import secrets
import string

from .database import Base
from .user import UserRole

class ActivationCodeStatus(enum.Enum):
    """激活码状态"""
    UNUSED = "unused"      # 未使用
    USED = "used"          # 已使用
    EXPIRED = "expired"    # 已过期
    INVALID = "invalid"    # 已失效

class ActivationCodeType(enum.Enum):
    """激活码类型"""
    MONTHLY = "monthly"    # 月卡
    QUARTERLY = "quarterly" # 季卡
    YEARLY = "yearly"      # 年卡

class PaymentStatus(enum.Enum):
    """支付状态"""
    PENDING = "pending"      # 待支付
    SUBMITTED = "submitted"  # 已提交付款（用户声明已付款）
    PAID = "paid"           # 已支付（管理员确认）
    REJECTED = "rejected"   # 已拒绝（管理员拒绝）
    FAILED = "failed"       # 支付失败
    REFUNDED = "refunded"   # 已退款
    CANCELLED = "cancelled" # 已取消
    EXPIRED = "expired"     # 已过期

class PaymentMethod(enum.Enum):
    """支付方式"""
    WECHAT = "wechat"      # 微信支付
    ALIPAY = "alipay"      # 支付宝
    DEMO = "demo"          # 演示支付

class PaymentOrder(Base):
    """支付订单表"""
    __tablename__ = "payment_orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_no = Column(String(32), unique=True, index=True, nullable=False, comment="订单号")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    
    # 订单信息
    product_type = Column(Enum(ActivationCodeType), nullable=False, comment="产品类型")
    amount = Column(Numeric(10, 2), nullable=False, comment="支付金额")
    currency = Column(String(3), default="CNY", comment="货币类型")
    
    # 支付信息
    payment_method = Column(Enum(PaymentMethod), nullable=False, comment="支付方式")
    payment_status = Column(Enum(PaymentStatus), default=PaymentStatus.PENDING, comment="支付状态")
    
    # 第三方支付信息
    third_party_order_no = Column(String(64), comment="第三方订单号")
    qr_code_url = Column(Text, comment="支付二维码URL")
    payment_url = Column(Text, comment="支付链接")
    
    # 个人收款码支付相关字段
    user_note = Column(Text, comment="用户备注")
    admin_note = Column(Text, comment="管理员备注")
    submitted_at = Column(DateTime, comment="用户提交付款时间")
    rejected_at = Column(DateTime, comment="管理员拒绝时间")
    confirmed_by = Column(Integer, ForeignKey("users.id"), comment="确认支付的管理员ID")

    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    paid_at = Column(DateTime, comment="支付时间")
    expires_at = Column(DateTime, comment="订单过期时间")
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id], back_populates="payment_orders")
    activation_codes = relationship("ActivationCode", back_populates="payment_order")
    confirmed_by_user = relationship("User", foreign_keys=[confirmed_by])
    
    def __repr__(self):
        return f"<PaymentOrder(order_no='{self.order_no}', status='{self.payment_status.value}')>"
    
    @property
    def is_expired(self):
        """检查订单是否过期"""
        return self.expires_at and datetime.utcnow() > self.expires_at
    
    @property
    def is_paid(self):
        """检查订单是否已支付"""
        return self.payment_status == PaymentStatus.PAID

class ActivationCode(Base):
    """激活码表"""
    __tablename__ = "activation_codes"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(32), unique=True, index=True, nullable=False, comment="激活码")
    
    # 激活码信息
    code_type = Column(Enum(ActivationCodeType), nullable=False, comment="激活码类型")
    status = Column(Enum(ActivationCodeStatus), default=ActivationCodeStatus.UNUSED, comment="激活码状态")
    
    # 关联信息
    payment_order_id = Column(Integer, ForeignKey("payment_orders.id"), comment="支付订单ID")
    created_by_user_id = Column(Integer, ForeignKey("users.id"), comment="创建者用户ID")
    used_by_user_id = Column(Integer, ForeignKey("users.id"), comment="使用者用户ID")
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    used_at = Column(DateTime, comment="使用时间")
    expires_at = Column(DateTime, comment="过期时间")
    
    # 关联关系
    payment_order = relationship("PaymentOrder", back_populates="activation_codes")
    created_by = relationship("User", foreign_keys=[created_by_user_id])
    used_by = relationship("User", foreign_keys=[used_by_user_id])
    
    def __repr__(self):
        return f"<ActivationCode(code='{self.code}', type='{self.code_type.value}', status='{self.status.value}')>"
    
    @property
    def is_expired(self):
        """检查激活码是否过期"""
        return self.expires_at and datetime.utcnow() > self.expires_at
    
    @property
    def is_usable(self):
        """检查激活码是否可用"""
        return (self.status == ActivationCodeStatus.UNUSED and 
                not self.is_expired)
    
    @classmethod
    def generate_code(cls, length=16):
        """生成激活码"""
        # 使用字母和数字，排除容易混淆的字符
        chars = string.ascii_uppercase + string.digits
        chars = chars.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
        
        # 生成随机码
        code = ''.join(secrets.choice(chars) for _ in range(length))
        
        # 格式化为 XXXX-XXXX-XXXX-XXXX 格式
        if length == 16:
            code = f"{code[:4]}-{code[4:8]}-{code[8:12]}-{code[12:16]}"
        
        return code
    
    @classmethod
    def create_activation_code(cls, code_type: ActivationCodeType, payment_order_id: int = None, 
                             created_by_user_id: int = None, expires_days: int = 365):
        """创建激活码"""
        code = cls.generate_code()
        expires_at = datetime.utcnow() + timedelta(days=expires_days)
        
        activation_code = cls(
            code=code,
            code_type=code_type,
            payment_order_id=payment_order_id,
            created_by_user_id=created_by_user_id,
            expires_at=expires_at
        )
        
        return activation_code

    def use_code(self, user_id: int) -> bool:
        """使用激活码"""
        if not self.is_usable:
            return False

        self.status = ActivationCodeStatus.USED
        self.used_by_user_id = user_id
        self.used_at = datetime.utcnow()

        return True

# 更新用户模型，添加支付订单关系
def update_user_model():
    """更新用户模型以添加支付相关关系"""
    from .user import User
    
    # 添加支付订单关系
    if not hasattr(User, 'payment_orders'):
        User.payment_orders = relationship("PaymentOrder", back_populates="user")
