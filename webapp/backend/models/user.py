"""
用户相关数据库模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, timedelta
import enum

from .database import Base

class UserRole(enum.Enum):
    """用户角色枚举"""
    ADMIN = "admin"           # 管理员
    FREE = "free"             # 普通用户
    MONTHLY = "monthly"       # 月卡用户
    QUARTERLY = "quarterly"   # 季卡用户
    YEARLY = "yearly"         # 年卡用户

class SubscriptionStatus(enum.Enum):
    """订阅状态枚举"""
    ACTIVE = "active"         # 激活
    EXPIRED = "expired"       # 过期
    CANCELLED = "cancelled"   # 取消

class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    
    # 用户状态
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    role = Column(Enum(UserRole), default=UserRole.FREE)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # 用户配置
    avatar_url = Column(String(255))
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="zh-CN")
    
    # 关联关系
    subscriptions = relationship("UserSubscription", back_populates="user", cascade="all, delete-orphan")
    payment_orders = relationship("PaymentOrder", foreign_keys="PaymentOrder.user_id", back_populates="user")
    
    @property
    def current_subscription(self):
        """获取当前有效订阅"""
        active_subs = [sub for sub in self.subscriptions if sub.is_active]
        if not active_subs:
            return None
        return max(active_subs, key=lambda x: x.expires_at)
    
    @property
    def has_premium_access(self):
        """是否有高级功能访问权限"""
        if self.role == UserRole.ADMIN:
            return True
        
        current_sub = self.current_subscription
        if current_sub and current_sub.is_active:
            return current_sub.subscription_type in [UserRole.MONTHLY, UserRole.QUARTERLY, UserRole.YEARLY]
        
        return False
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role.value}')>"

class UserSubscription(Base):
    """用户订阅模型"""
    __tablename__ = "user_subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 订阅信息
    subscription_type = Column(Enum(UserRole), nullable=False)
    status = Column(Enum(SubscriptionStatus), default=SubscriptionStatus.ACTIVE)
    
    # 时间信息
    starts_at = Column(DateTime(timezone=True), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 支付信息
    payment_method = Column(String(50))
    payment_id = Column(String(100))
    amount = Column(String(20))  # 存储为字符串避免精度问题
    currency = Column(String(10), default="CNY")
    
    # 关联关系
    user = relationship("User", back_populates="subscriptions")
    
    @property
    def is_active(self):
        """检查订阅是否有效"""
        now = datetime.utcnow()
        return (
            self.status == SubscriptionStatus.ACTIVE and
            self.starts_at <= now <= self.expires_at
        )
    
    @property
    def days_remaining(self):
        """剩余天数"""
        if not self.is_active:
            return 0
        
        now = datetime.utcnow()
        if now > self.expires_at:
            return 0
        
        return (self.expires_at - now).days
    
    def __repr__(self):
        return f"<UserSubscription(id={self.id}, user_id={self.user_id}, type='{self.subscription_type.value}', status='{self.status.value}')>"
