"""
应用配置设置
"""

import os
from typing import List
from pathlib import Path

try:
    # Pydantic v2
    from pydantic_settings import BaseSettings
    from pydantic import field_validator
    PYDANTIC_V2 = True
except ImportError:
    # Pydantic v1
    from pydantic import BaseSettings, validator
    PYDANTIC_V2 = False

class Settings(BaseSettings):
    """应用设置"""
    
    # 基础配置
    APP_NAME: str = "Coin-Analyze"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    ENVIRONMENT: str = "development"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://localhost:56190",
        "http://127.0.0.1:56190",
        "http://************:3000",
        "http://************:5173",
        "http://************:56190",
        # FRP服务器配置
        "http://**************:3000",
        "https://**************:3000",
        "http://**************:8000",
        "https://**************:8000"
    ]
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./coin_analyze.db"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    
    # API密钥配置
    BINANCE_API_KEY: str = ""
    BINANCE_SECRET_KEY: str = ""
    
    # 第三方API配置
    COINGECKO_API_KEY: str = ""
    NEWSAPI_KEY: str = ""
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440  # 24小时

    # 默认管理员配置
    DEFAULT_ADMIN_USERNAME: str = "admin"
    DEFAULT_ADMIN_EMAIL: str = "<EMAIL>"
    DEFAULT_ADMIN_PASSWORD: str = "admin123456"  # 生产环境中应该修改
    
    # 文件路径配置
    PROJECT_ROOT: Path = Path(__file__).parent.parent.parent.parent
    DATA_PATH: Path = PROJECT_ROOT / "data"
    MODELS_PATH: Path = PROJECT_ROOT / "models"
    LOGS_PATH: Path = PROJECT_ROOT / "logs"
    
    # 预测模型配置
    MODEL_UPDATE_INTERVAL: int = 3600  # 模型更新间隔(秒)
    PREDICTION_CACHE_TTL: int = 300    # 预测结果缓存时间(秒)
    
    # 市场数据配置
    MARKET_DATA_UPDATE_INTERVAL: int = 5  # 市场数据更新间隔(秒)
    SUPPORTED_SYMBOLS: List[str] = [
        "BTCUSDT", "ETHUSDT", "BNBUSDT", "XRPUSDT", 
        "ADAUSDT", "SOLUSDT", "DOGEUSDT", "AVAXUSDT",
        "TRXUSDT", "TONUSDT"
    ]
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = 30
    WS_MAX_CONNECTIONS: int = 1000
    
    # 个人收款码支付配置
    PERSONAL_PAYMENT_WECHAT_QR_CODE_URLS: str = ""
    PERSONAL_PAYMENT_WECHAT_ACCOUNT_NAMES: str = ""
    PERSONAL_PAYMENT_WECHAT_ACCOUNT_INFOS: str = ""
    PERSONAL_PAYMENT_ALIPAY_QR_CODE_URLS: str = ""
    PERSONAL_PAYMENT_ALIPAY_ACCOUNT_NAMES: str = ""
    PERSONAL_PAYMENT_ALIPAY_ACCOUNT_INFOS: str = ""
    PERSONAL_PAYMENT_MONTHLY_PRICE: float = 29.99
    PERSONAL_PAYMENT_QUARTERLY_PRICE: float = 79.99
    PERSONAL_PAYMENT_YEARLY_PRICE: float = 299.99

    # 支付配置
    PAYMENT_PAYMENT_MODE: str = "sandbox"
    PAYMENT_BASE_URL: str = "http://localhost:8000"

    # API配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Coin-Analyze"

    # CORS配置
    BACKEND_CORS_ORIGINS: str = '["http://localhost:3000", "https://yourdomain.com"]'

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 缓存配置
    CACHE_DEFAULT_TTL: int = 300
    CACHE_MARKET_DATA_TTL: int = 60
    CACHE_PREDICTION_TTL: int = 300
    
    # 限流配置
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 10
    
    # 安全配置
    ENABLE_HTTPS: bool = False
    SSL_CERT_PATH: str = ""
    SSL_KEY_PATH: str = ""
    
    if PYDANTIC_V2:
        @field_validator("ALLOWED_ORIGINS", mode="before")
        @classmethod
        def parse_cors_origins(cls, v):
            if isinstance(v, str):
                return [origin.strip() for origin in v.split(",")]
            return v

        @field_validator("SUPPORTED_SYMBOLS", mode="before")
        @classmethod
        def parse_symbols(cls, v):
            if isinstance(v, str):
                return [symbol.strip().upper() for symbol in v.split(",")]
            return v
    else:
        @validator("ALLOWED_ORIGINS", pre=True)
        def parse_cors_origins(cls, v):
            if isinstance(v, str):
                return [origin.strip() for origin in v.split(",")]
            return v

        @validator("SUPPORTED_SYMBOLS", pre=True)
        def parse_symbols(cls, v):
            if isinstance(v, str):
                return [symbol.strip().upper() for symbol in v.split(",")]
            return v
    
    @property
    def database_url_sync(self) -> str:
        """同步数据库URL"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+psycopg2://")
    
    @property
    def database_url_async(self) -> str:
        """异步数据库URL"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局设置实例
settings = Settings()

# 确保必要的目录存在
for path in [settings.DATA_PATH, settings.MODELS_PATH, settings.LOGS_PATH]:
    path.mkdir(parents=True, exist_ok=True)
