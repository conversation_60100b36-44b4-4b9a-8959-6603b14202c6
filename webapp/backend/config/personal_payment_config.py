"""
个人收款码支付配置
"""

import os
from typing import Dict, Any, List
from pydantic_settings import BaseSettings

class PersonalPaymentConfig(BaseSettings):
    """个人收款码支付配置类"""

    # 微信收款码配置 - 支持多个收款码
    WECHAT_QR_CODE_URLS: str = ""  # 用逗号分隔多个URL
    WECHAT_ACCOUNT_NAMES: str = ""  # 用逗号分隔多个账户名
    WECHAT_ACCOUNT_INFOS: str = ""  # 用逗号分隔多个账户信息

    # 支付宝收款码配置 - 支持多个收款码
    ALIPAY_QR_CODE_URLS: str = ""  # 用逗号分隔多个URL
    ALIPAY_ACCOUNT_NAMES: str = ""  # 用逗号分隔多个账户名
    ALIPAY_ACCOUNT_INFOS: str = ""  # 用逗号分隔多个账户信息
    
    # 产品价格配置
    MONTHLY_PRICE: float = 29.99
    QUARTERLY_PRICE: float = 79.99
    YEARLY_PRICE: float = 299.99
    
    class Config:
        env_file = ".env"
        env_prefix = "PERSONAL_PAYMENT_"
        extra = "ignore"  # 忽略额外字段

# 全局个人支付配置实例
personal_payment_config = PersonalPaymentConfig()

def get_payment_qr_codes() -> Dict[str, List[Dict[str, Any]]]:
    """获取收款码配置 - 支持多个收款码"""
    import random

    # 解析微信收款码配置
    wechat_urls = [url.strip() for url in personal_payment_config.WECHAT_QR_CODE_URLS.split(',') if url.strip()]
    wechat_names = [name.strip() for name in personal_payment_config.WECHAT_ACCOUNT_NAMES.split(',') if name.strip()]
    wechat_infos = [info.strip() for info in personal_payment_config.WECHAT_ACCOUNT_INFOS.split(',') if info.strip()]

    # 解析支付宝收款码配置
    alipay_urls = [url.strip() for url in personal_payment_config.ALIPAY_QR_CODE_URLS.split(',') if url.strip()]
    alipay_names = [name.strip() for name in personal_payment_config.ALIPAY_ACCOUNT_NAMES.split(',') if name.strip()]
    alipay_infos = [info.strip() for info in personal_payment_config.ALIPAY_ACCOUNT_INFOS.split(',') if info.strip()]

    # 构建微信收款码列表
    wechat_codes = []
    if wechat_urls:
        for i, url in enumerate(wechat_urls):
            wechat_codes.append({
                "qr_code_url": url,
                "account_name": wechat_names[i] if i < len(wechat_names) else f"微信收款码{i+1}",
                "account_info": wechat_infos[i] if i < len(wechat_infos) else f"微信账号{i+1}"
            })
    else:
        wechat_codes.append({
            "qr_code_url": "https://via.placeholder.com/300x300?text=微信收款码",
            "account_name": "请配置收款人姓名",
            "account_info": "请配置微信号"
        })

    # 构建支付宝收款码列表
    alipay_codes = []
    if alipay_urls:
        for i, url in enumerate(alipay_urls):
            alipay_codes.append({
                "qr_code_url": url,
                "account_name": alipay_names[i] if i < len(alipay_names) else f"支付宝收款码{i+1}",
                "account_info": alipay_infos[i] if i < len(alipay_infos) else f"支付宝账号{i+1}"
            })
    else:
        alipay_codes.append({
            "qr_code_url": "https://via.placeholder.com/300x300?text=支付宝收款码",
            "account_name": "请配置收款人姓名",
            "account_info": "请配置支付宝账号"
        })

    return {
        "wechat": wechat_codes,
        "alipay": alipay_codes
    }

def get_random_payment_qr_code(payment_method: str) -> Dict[str, Any]:
    """随机获取一个收款码"""
    import random

    qr_codes = get_payment_qr_codes()
    if payment_method in qr_codes and qr_codes[payment_method]:
        return random.choice(qr_codes[payment_method])

    # 默认返回占位符
    return {
        "qr_code_url": f"https://via.placeholder.com/300x300?text={payment_method}收款码",
        "account_name": "请配置收款人姓名",
        "account_info": f"请配置{payment_method}账号"
    }

def get_product_prices() -> Dict[str, float]:
    """获取产品价格配置"""
    return {
        "monthly": personal_payment_config.MONTHLY_PRICE,
        "quarterly": personal_payment_config.QUARTERLY_PRICE,
        "yearly": personal_payment_config.YEARLY_PRICE
    }

def is_personal_payment_configured() -> bool:
    """检查个人收款码是否已配置"""
    wechat_configured = bool(
        personal_payment_config.WECHAT_QR_CODE_URLS and
        personal_payment_config.WECHAT_ACCOUNT_NAMES
    )

    alipay_configured = bool(
        personal_payment_config.ALIPAY_QR_CODE_URLS and
        personal_payment_config.ALIPAY_ACCOUNT_NAMES
    )

    return wechat_configured or alipay_configured

# 个人收款码配置示例
PERSONAL_PAYMENT_CONFIG_EXAMPLE = """
# 个人收款码支付配置示例 - 请添加到 .env 文件中
# 支持多个收款码，用逗号分隔

# 微信收款码配置（支持多个，用逗号分隔）
PERSONAL_PAYMENT_WECHAT_QR_CODE_URLS=http://localhost:3000/images/wechat_qr1.jpg,http://localhost:3000/images/wechat_qr2.jpg
PERSONAL_PAYMENT_WECHAT_ACCOUNT_NAMES=张三,李四
PERSONAL_PAYMENT_WECHAT_ACCOUNT_INFOS=wxid_zhangsan,wxid_lisi

# 支付宝收款码配置（支持多个，用逗号分隔）
PERSONAL_PAYMENT_ALIPAY_QR_CODE_URLS=http://localhost:3000/images/alipay_qr1.jpg,http://localhost:3000/images/alipay_qr2.jpg
PERSONAL_PAYMENT_ALIPAY_ACCOUNT_NAMES=张三,李四
PERSONAL_PAYMENT_ALIPAY_ACCOUNT_INFOS=138****1111,138****2222

# 产品价格配置（可选，有默认值）
PERSONAL_PAYMENT_MONTHLY_PRICE=29.99
PERSONAL_PAYMENT_QUARTERLY_PRICE=79.99
PERSONAL_PAYMENT_YEARLY_PRICE=299.99
"""
