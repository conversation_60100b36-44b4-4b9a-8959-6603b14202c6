"""
支付配置文件
包含微信支付和支付宝的配置信息
"""

import os
from typing import Dict, Any
from pydantic_settings import BaseSettings

class PaymentConfig(BaseSettings):
    """支付配置类"""
    
    # 基础配置
    BASE_URL: str = "http://localhost:8000"
    PAYMENT_MODE: str = "sandbox"  # sandbox 或 production
    
    # 微信支付配置
    WECHAT_APP_ID: str = ""
    WECHAT_MCH_ID: str = ""
    WECHAT_API_KEY: str = ""
    WECHAT_CERT_PATH: str = ""
    WECHAT_KEY_PATH: str = ""
    WECHAT_SERIAL_NO: str = ""
    
    # 支付宝配置
    ALIPAY_APP_ID: str = ""
    ALIPAY_PRIVATE_KEY: str = ""
    ALIPAY_PUBLIC_KEY: str = ""
    ALIPAY_GATEWAY: str = "https://openapi.alipay.com/gateway.do"  # 正式环境
    ALIPAY_SANDBOX_GATEWAY: str = "https://openapi.alipaydev.com/gateway.do"  # 沙箱环境
    
    class Config:
        env_file = ".env"
        env_prefix = "PAYMENT_"
        extra = "ignore"  # 忽略额外字段

# 全局支付配置实例
payment_config = PaymentConfig()

def get_wechat_config() -> Dict[str, Any]:
    """获取微信支付配置"""
    return {
        "app_id": payment_config.WECHAT_APP_ID,
        "mch_id": payment_config.WECHAT_MCH_ID,
        "api_key": payment_config.WECHAT_API_KEY,
        "cert_path": payment_config.WECHAT_CERT_PATH,
        "key_path": payment_config.WECHAT_KEY_PATH,
        "serial_no": payment_config.WECHAT_SERIAL_NO,
        "notify_url": f"{payment_config.BASE_URL}/api/v1/payment/wechat/notify",
        "is_sandbox": payment_config.PAYMENT_MODE == "sandbox"
    }

def get_alipay_config() -> Dict[str, Any]:
    """获取支付宝配置"""
    gateway = (payment_config.ALIPAY_SANDBOX_GATEWAY 
              if payment_config.PAYMENT_MODE == "sandbox" 
              else payment_config.ALIPAY_GATEWAY)
    
    return {
        "app_id": payment_config.ALIPAY_APP_ID,
        "private_key": payment_config.ALIPAY_PRIVATE_KEY,
        "public_key": payment_config.ALIPAY_PUBLIC_KEY,
        "gateway": gateway,
        "notify_url": f"{payment_config.BASE_URL}/api/v1/payment/alipay/notify",
        "return_url": f"{payment_config.BASE_URL}/payment/success",
        "is_sandbox": payment_config.PAYMENT_MODE == "sandbox"
    }

def is_payment_configured() -> bool:
    """检查支付是否已配置"""
    wechat_configured = bool(
        payment_config.WECHAT_APP_ID and 
        payment_config.WECHAT_MCH_ID and 
        payment_config.WECHAT_API_KEY
    )
    
    alipay_configured = bool(
        payment_config.ALIPAY_APP_ID and 
        payment_config.ALIPAY_PRIVATE_KEY and 
        payment_config.ALIPAY_PUBLIC_KEY
    )
    
    return wechat_configured or alipay_configured

# 支付配置示例（用于文档）
PAYMENT_CONFIG_EXAMPLE = """
# 支付配置示例 - 请添加到 .env 文件中

# 支付模式: sandbox(沙箱) 或 production(生产)
PAYMENT_PAYMENT_MODE=sandbox

# 微信支付配置
PAYMENT_WECHAT_APP_ID=your_wechat_app_id
PAYMENT_WECHAT_MCH_ID=your_merchant_id
PAYMENT_WECHAT_API_KEY=your_api_key
PAYMENT_WECHAT_CERT_PATH=path/to/apiclient_cert.pem
PAYMENT_WECHAT_KEY_PATH=path/to/apiclient_key.pem
PAYMENT_WECHAT_SERIAL_NO=your_cert_serial_no

# 支付宝配置
PAYMENT_ALIPAY_APP_ID=your_alipay_app_id
PAYMENT_ALIPAY_PRIVATE_KEY=your_private_key
PAYMENT_ALIPAY_PUBLIC_KEY=alipay_public_key

# 服务器地址（用于回调）
PAYMENT_BASE_URL=https://yourdomain.com
"""
