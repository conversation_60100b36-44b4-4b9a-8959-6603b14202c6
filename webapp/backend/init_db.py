#!/usr/bin/env python3
"""
数据库初始化脚本
创建数据库表并初始化默认管理员账户
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from sqlalchemy.orm import Session
from models.database import engine, SessionLocal, init_db
from models.user import User, UserRole, UserSubscription, SubscriptionStatus
from auth.security import get_password_hash
from config.settings import settings

def create_default_admin():
    """创建默认管理员账户"""
    db = SessionLocal()
    try:
        # 检查是否已存在管理员账户
        existing_admin = db.query(User).filter(
            User.username == settings.DEFAULT_ADMIN_USERNAME
        ).first()
        
        if existing_admin:
            print(f"⚠️  管理员账户 '{settings.DEFAULT_ADMIN_USERNAME}' 已存在")
            return existing_admin
        
        # 创建管理员账户
        admin_user = User(
            username=settings.DEFAULT_ADMIN_USERNAME,
            email=settings.DEFAULT_ADMIN_EMAIL,
            full_name="系统管理员",
            hashed_password=get_password_hash(settings.DEFAULT_ADMIN_PASSWORD),
            role=UserRole.ADMIN,
            is_active=True,
            is_verified=True,
            created_at=datetime.utcnow()
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print(f"✅ 创建默认管理员账户成功:")
        print(f"   用户名: {settings.DEFAULT_ADMIN_USERNAME}")
        print(f"   邮箱: {settings.DEFAULT_ADMIN_EMAIL}")
        print(f"   密码: {settings.DEFAULT_ADMIN_PASSWORD}")
        print(f"   ⚠️  请在生产环境中修改默认密码!")
        
        return admin_user
        
    except Exception as e:
        print(f"❌ 创建管理员账户失败: {e}")
        db.rollback()
        return None
    finally:
        db.close()

def create_demo_users():
    """创建演示用户账户"""
    db = SessionLocal()
    try:
        demo_users = [
            {
                "username": "demo_free",
                "email": "<EMAIL>",
                "full_name": "免费用户演示",
                "role": UserRole.FREE
            },
            {
                "username": "demo_monthly",
                "email": "<EMAIL>", 
                "full_name": "月卡用户演示",
                "role": UserRole.MONTHLY
            },
            {
                "username": "demo_yearly",
                "email": "<EMAIL>",
                "full_name": "年卡用户演示", 
                "role": UserRole.YEARLY
            }
        ]
        
        created_users = []
        for user_data in demo_users:
            # 检查用户是否已存在
            existing_user = db.query(User).filter(
                User.username == user_data["username"]
            ).first()
            
            if existing_user:
                print(f"⚠️  演示用户 '{user_data['username']}' 已存在")
                continue
            
            # 创建用户
            demo_user = User(
                username=user_data["username"],
                email=user_data["email"],
                full_name=user_data["full_name"],
                hashed_password=get_password_hash("demo123456"),
                role=user_data["role"],
                is_active=True,
                is_verified=True,
                created_at=datetime.utcnow()
            )
            
            db.add(demo_user)
            db.commit()
            db.refresh(demo_user)
            
            # 为付费用户创建订阅记录
            if demo_user.role in [UserRole.MONTHLY, UserRole.YEARLY]:
                from datetime import timedelta
                
                months = 1 if demo_user.role == UserRole.MONTHLY else 12
                subscription = UserSubscription(
                    user_id=demo_user.id,
                    subscription_type=demo_user.role,
                    status=SubscriptionStatus.ACTIVE,
                    starts_at=datetime.utcnow(),
                    expires_at=datetime.utcnow() + timedelta(days=months * 30),
                    payment_method="demo",
                    amount="29.99" if demo_user.role == UserRole.MONTHLY else "299.99",
                    currency="CNY"
                )
                
                db.add(subscription)
                db.commit()
            
            created_users.append(demo_user)
            print(f"✅ 创建演示用户: {user_data['username']} ({user_data['role'].value})")
        
        if created_users:
            print(f"✅ 共创建 {len(created_users)} 个演示用户")
            print("   默认密码: demo123456")
        
        return created_users
        
    except Exception as e:
        print(f"❌ 创建演示用户失败: {e}")
        db.rollback()
        return []
    finally:
        db.close()

def main():
    """主函数"""
    print("🚀 开始初始化数据库...")
    
    try:
        # 初始化数据库表
        print("📊 创建数据库表...")
        init_db()
        
        # 创建默认管理员
        print("👤 创建默认管理员账户...")
        admin_user = create_default_admin()
        
        # 创建演示用户
        print("🎭 创建演示用户账户...")
        demo_users = create_demo_users()
        
        print("\n" + "="*60)
        print("🎉 数据库初始化完成!")
        print("="*60)
        print("📋 账户信息:")
        print(f"   管理员: {settings.DEFAULT_ADMIN_USERNAME} / {settings.DEFAULT_ADMIN_PASSWORD}")
        print("   演示用户: demo_free, demo_monthly, demo_yearly / demo123456")
        print("="*60)
        print("⚠️  请在生产环境中修改默认密码!")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
