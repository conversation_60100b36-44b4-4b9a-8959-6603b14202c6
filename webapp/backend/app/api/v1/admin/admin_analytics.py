"""
管理员分析统计API
包含访问统计、收款码统计等功能
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, text
from pydantic import BaseModel

from models.database import get_db
from auth.security import require_admin_access
from models.user import User
from models.activation_code import PaymentOrder

router = APIRouter()

# 响应模型
class VisitorStats(BaseModel):
    """访问统计数据"""
    total_visitors: int
    unique_visitors: int
    registered_users: int
    premium_users: int
    today_visitors: int
    this_week_visitors: int
    this_month_visitors: int

class VisitorRecord(BaseModel):
    """访问记录"""
    id: int
    user_id: Optional[int]
    username: Optional[str]
    email: Optional[str]
    ip_address: str
    user_agent: str
    page_path: str
    visit_time: datetime
    session_duration: Optional[int]
    is_registered: bool
    is_premium: bool

class QRCodeStats(BaseModel):
    """收款码统计"""
    qr_code_id: str
    qr_code_name: str
    total_payments: int
    total_amount: float
    success_rate: float
    last_used: Optional[datetime]
    daily_stats: List[Dict[str, Any]]

class QRCodePaymentRecord(BaseModel):
    """收款码付费记录"""
    id: int
    order_no: str
    qr_code_id: str
    qr_code_name: str
    user_id: int
    username: str
    amount: float
    payment_status: str
    created_at: datetime
    paid_at: Optional[datetime]
    user_note: Optional[str]

class AnalyticsResponse(BaseModel):
    """分析统计响应"""
    visitor_stats: VisitorStats
    recent_visitors: List[VisitorRecord]
    qr_code_stats: List[QRCodeStats]
    total_pages: int
    current_page: int

@require_admin_access
@router.get("/analytics/visitors", response_model=AnalyticsResponse)
async def get_visitor_analytics(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取访问统计数据"""
    
    try:
        # 解析日期范围
        date_filter = []
        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            date_filter.append(User.created_at >= start_dt)
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            date_filter.append(User.created_at <= end_dt)

        # 计算访问统计
        now = datetime.utcnow()
        today = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        # 总用户统计
        try:
            total_users = db.query(func.count(User.id)).scalar() or 0
            registered_users = db.query(func.count(User.id)).filter(User.is_active == True).scalar() or 0
            premium_users = db.query(func.count(User.id)).filter(User.is_premium == True).scalar() or 0

            # 时间段统计
            today_users = db.query(func.count(User.id)).filter(User.created_at >= today).scalar() or 0
            week_users = db.query(func.count(User.id)).filter(User.created_at >= week_ago).scalar() or 0
            month_users = db.query(func.count(User.id)).filter(User.created_at >= month_ago).scalar() or 0
        except Exception as e:
            print(f"获取用户统计失败: {e}")
            # 数据库查询失败时返回0
            total_users = 0
            registered_users = 0
            premium_users = 0
            today_users = 0
            week_users = 0
            month_users = 0

        visitor_stats = VisitorStats(
            total_visitors=total_users,
            unique_visitors=registered_users,
            registered_users=registered_users,
            premium_users=premium_users,
            today_visitors=today_users,
            this_week_visitors=week_users,
            this_month_visitors=month_users
        )

        # 获取最近访问用户（模拟数据，实际应该从访问日志表获取）
        try:
            recent_users_query = db.query(User).order_by(desc(User.last_login_at))
            if date_filter:
                recent_users_query = recent_users_query.filter(and_(*date_filter))

            total_records = recent_users_query.count()
            recent_users = recent_users_query.offset((page - 1) * page_size).limit(page_size).all()

            recent_visitors = []
            for user in recent_users:
                recent_visitors.append(VisitorRecord(
                    id=user.id,
                    user_id=user.id,
                    username=user.username,
                    email=user.email,
                    ip_address="***********",  # 模拟IP
                    user_agent="Mozilla/5.0...",  # 模拟User Agent
                    page_path="/",
                    visit_time=user.last_login_at or user.created_at,
                    session_duration=1800,  # 模拟会话时长
                    is_registered=True,
                    is_premium=user.is_premium
                ))
        except Exception as e:
            print(f"获取用户数据失败: {e}")
            # 数据库查询失败时返回空列表
            total_records = 0
            recent_visitors = []

        # 收款码统计（从付费记录中统计）
        qr_code_stats = await get_qr_code_statistics(db)

        total_pages = (total_records + page_size - 1) // page_size

        return AnalyticsResponse(
            visitor_stats=visitor_stats,
            recent_visitors=recent_visitors,
            qr_code_stats=qr_code_stats,
            total_pages=total_pages,
            current_page=page
        )

    except Exception as e:
        print(f"获取访问统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取访问统计失败")

async def get_qr_code_statistics(db: Session) -> List[QRCodeStats]:
    """获取收款码统计数据"""

    try:
        # 从付费记录中统计收款码使用情况
        qr_stats_query = db.query(
            PaymentOrder.user_note.label('qr_info'),
            func.count(PaymentOrder.id).label('total_payments'),
            func.sum(PaymentOrder.amount).label('total_amount'),
            func.count(func.nullif(PaymentOrder.payment_status != 'paid', True)).label('success_count'),
            func.max(PaymentOrder.created_at).label('last_used')
        ).filter(
            PaymentOrder.user_note.isnot(None),
            PaymentOrder.user_note.like('%收款码%')
        ).group_by(PaymentOrder.user_note).all()

        qr_code_stats = []
        for stat in qr_stats_query:
            # 解析收款码信息
            qr_info = stat.qr_info or ""
            if "收款码A" in qr_info:
                qr_id, qr_name = "QR_A", "收款码A"
            elif "收款码B" in qr_info:
                qr_id, qr_name = "QR_B", "收款码B"
            elif "收款码C" in qr_info:
                qr_id, qr_name = "QR_C", "收款码C"
            else:
                qr_id, qr_name = "QR_OTHER", "其他收款码"

            success_rate = (stat.success_count / stat.total_payments * 100) if stat.total_payments > 0 else 0

            qr_code_stats.append(QRCodeStats(
                qr_code_id=qr_id,
                qr_code_name=qr_name,
                total_payments=stat.total_payments,
                total_amount=float(stat.total_amount or 0),
                success_rate=success_rate,
                last_used=stat.last_used,
                daily_stats=[]  # 可以后续添加每日统计
            ))

        return qr_code_stats
    except Exception as e:
        print(f"获取收款码统计失败: {e}")
        # 数据库查询失败时返回空列表
        return []

@require_admin_access
@router.get("/analytics/qr-codes/{qr_code_id}/payments")
async def get_qr_code_payments(
    qr_code_id: str,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取特定收款码的付费记录"""
    
    try:
        # 根据收款码ID确定搜索关键词
        qr_keywords = {
            "QR_A": "收款码A",
            "QR_B": "收款码B", 
            "QR_C": "收款码C"
        }
        
        keyword = qr_keywords.get(qr_code_id, "收款码")
        
        try:
            # 构建查询
            query = db.query(PaymentOrder).join(User).filter(
                PaymentOrder.user_note.like(f'%{keyword}%')
            )

            # 日期筛选
            if start_date:
                start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                query = query.filter(PaymentOrder.created_at >= start_dt)
            if end_date:
                end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                query = query.filter(PaymentOrder.created_at <= end_dt)

            total_records = query.count()
            records = query.order_by(desc(PaymentOrder.created_at)).offset((page - 1) * page_size).limit(page_size).all()

            payment_records = []
            for record in records:
                payment_records.append(QRCodePaymentRecord(
                    id=record.id,
                    order_no=record.order_no,
                    qr_code_id=qr_code_id,
                    qr_code_name=keyword,
                    user_id=record.user_id,
                    username=record.user.username,
                    amount=record.amount,
                    payment_status=record.payment_status,
                    created_at=record.created_at,
                    paid_at=record.paid_at,
                    user_note=record.user_note
                ))
        except Exception as e:
            print(f"获取收款码付费记录失败: {e}")
            # 数据库查询失败时返回空列表
            total_records = 0
            payment_records = []
        
        total_pages = (total_records + page_size - 1) // page_size
        
        return {
            "records": payment_records,
            "total_records": total_records,
            "total_pages": total_pages,
            "current_page": page
        }
        
    except Exception as e:
        print(f"获取收款码付费记录失败: {e}")
        raise HTTPException(status_code=500, detail="获取收款码付费记录失败")

@require_admin_access
@router.get("/analytics/qr-codes/summary")
async def get_qr_codes_summary(
    db: Session = Depends(get_db)
):
    """获取所有收款码汇总统计"""
    
    try:
        qr_code_stats = await get_qr_code_statistics(db)
        
        # 计算总体统计
        total_payments = sum(stat.total_payments for stat in qr_code_stats)
        total_amount = sum(stat.total_amount for stat in qr_code_stats)
        avg_success_rate = sum(stat.success_rate for stat in qr_code_stats) / len(qr_code_stats) if qr_code_stats else 0
        
        return {
            "summary": {
                "total_qr_codes": len(qr_code_stats),
                "total_payments": total_payments,
                "total_amount": total_amount,
                "average_success_rate": avg_success_rate
            },
            "qr_code_stats": qr_code_stats
        }
        
    except Exception as e:
        print(f"获取收款码汇总统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取收款码汇总统计失败")
