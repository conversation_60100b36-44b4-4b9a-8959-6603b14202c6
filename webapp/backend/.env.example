# 数据库配置
DATABASE_URL=sqlite:///./data/coin_analyze.db

# JWT配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 支付配置
# 支付模式: sandbox(沙箱) 或 production(生产)
PAYMENT_PAYMENT_MODE=sandbox

# 微信支付配置
PAYMENT_WECHAT_APP_ID=your_wechat_app_id
PAYMENT_WECHAT_MCH_ID=your_merchant_id
PAYMENT_WECHAT_API_KEY=your_api_key_v3
PAYMENT_WECHAT_CERT_PATH=certs/apiclient_cert.pem
PAYMENT_WECHAT_KEY_PATH=certs/apiclient_key.pem
PAYMENT_WECHAT_SERIAL_NO=your_cert_serial_no

# 支付宝配置
PAYMENT_ALIPAY_APP_ID=your_alipay_app_id
PAYMENT_ALIPAY_PRIVATE_KEY=your_rsa_private_key
PAYMENT_ALIPAY_PUBLIC_KEY=alipay_rsa_public_key

# 服务器地址（用于回调）
PAYMENT_BASE_URL=https://yourdomain.com

# API配置
API_V1_STR=/api/v1
PROJECT_NAME=Coin-Analyze

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

# 个人收款码支付配置 - 支持多个收款码
# 微信收款码配置（多个收款码用逗号分隔）
PERSONAL_PAYMENT_WECHAT_QR_CODE_URLS=http://localhost:3000/images/jiazi-weixin.jpg,http://localhost:3000/images/xueqian-weixin.jpg
PERSONAL_PAYMENT_WECHAT_ACCOUNT_NAMES=甲子,雪千
PERSONAL_PAYMENT_WECHAT_ACCOUNT_INFOS=甲子微信,雪千微信

# 支付宝收款码配置（多个收款码用逗号分隔）
PERSONAL_PAYMENT_ALIPAY_QR_CODE_URLS=http://localhost:3000/images/jiazi-zhifubao.jpg,http://localhost:3000/images/xueqian-zhifubao.jpg
PERSONAL_PAYMENT_ALIPAY_ACCOUNT_NAMES=甲子,雪千
PERSONAL_PAYMENT_ALIPAY_ACCOUNT_INFOS=甲子支付宝,雪千支付宝

# 产品价格配置（可选，有默认值）
PERSONAL_PAYMENT_MONTHLY_PRICE=29.99
PERSONAL_PAYMENT_QUARTERLY_PRICE=79.99
PERSONAL_PAYMENT_YEARLY_PRICE=299.99

# 日志配置
LOG_LEVEL=INFO
