"""
支付服务
集成微信支付和支付宝支付
"""

import hashlib
import json
import time
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Optional, Tuple
import requests
from sqlalchemy.orm import Session

from models.activation_code import (
    PaymentOrder, ActivationCode, PaymentStatus, PaymentMethod, 
    ActivationCodeType, ActivationCodeStatus
)
from models.user import User, UserRole
from config.settings import settings

class PaymentService:
    """支付服务类"""
    
    def __init__(self):
        # 支付配置 - 在生产环境中应该从环境变量或配置文件读取
        self.wechat_config = {
            'app_id': settings.WECHAT_APP_ID if hasattr(settings, 'WECHAT_APP_ID') else 'demo_app_id',
            'mch_id': settings.WECHAT_MCH_ID if hasattr(settings, 'WECHAT_MCH_ID') else 'demo_mch_id',
            'api_key': settings.WECHAT_API_KEY if hasattr(settings, 'WECHAT_API_KEY') else 'demo_api_key',
            'notify_url': f"{settings.BASE_URL}/api/v1/payment/wechat/notify" if hasattr(settings, 'BASE_URL') else 'http://localhost:8000/api/v1/payment/wechat/notify'
        }
        
        self.alipay_config = {
            'app_id': settings.ALIPAY_APP_ID if hasattr(settings, 'ALIPAY_APP_ID') else 'demo_app_id',
            'private_key': settings.ALIPAY_PRIVATE_KEY if hasattr(settings, 'ALIPAY_PRIVATE_KEY') else 'demo_private_key',
            'public_key': settings.ALIPAY_PUBLIC_KEY if hasattr(settings, 'ALIPAY_PUBLIC_KEY') else 'demo_public_key',
            'notify_url': f"{settings.BASE_URL}/api/v1/payment/alipay/notify" if hasattr(settings, 'BASE_URL') else 'http://localhost:8000/api/v1/payment/alipay/notify'
        }
        
        # 产品价格配置
        self.product_prices = {
            ActivationCodeType.MONTHLY: Decimal('29.99'),
            ActivationCodeType.QUARTERLY: Decimal('79.99'),
            ActivationCodeType.YEARLY: Decimal('299.99')
        }
    
    def generate_order_no(self) -> str:
        """生成订单号"""
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4()).replace('-', '')[:8]
        return f"PAY{timestamp}{random_str}".upper()
    
    def create_payment_order(self, db: Session, user_id: int, product_type: ActivationCodeType, 
                           payment_method: PaymentMethod) -> PaymentOrder:
        """创建支付订单"""
        order_no = self.generate_order_no()
        amount = self.product_prices[product_type]
        expires_at = datetime.utcnow() + timedelta(hours=2)  # 订单2小时后过期
        
        order = PaymentOrder(
            order_no=order_no,
            user_id=user_id,
            product_type=product_type,
            amount=amount,
            payment_method=payment_method,
            expires_at=expires_at
        )
        
        db.add(order)
        db.commit()
        db.refresh(order)
        
        return order
    
    def create_wechat_payment(self, order: PaymentOrder) -> Dict:
        """创建微信支付订单"""
        # 在实际环境中，这里会调用微信支付API
        # 这里提供演示实现
        
        if self.wechat_config['app_id'] == 'demo_app_id':
            # 演示模式
            return self._create_demo_payment(order, PaymentMethod.WECHAT)
        
        # 实际微信支付实现
        try:
            # 构建支付参数
            params = {
                'appid': self.wechat_config['app_id'],
                'mch_id': self.wechat_config['mch_id'],
                'nonce_str': str(uuid.uuid4()).replace('-', ''),
                'body': f"会员服务-{order.product_type.value}",
                'out_trade_no': order.order_no,
                'total_fee': int(order.amount * 100),  # 微信支付金额单位为分
                'spbill_create_ip': '127.0.0.1',
                'notify_url': self.wechat_config['notify_url'],
                'trade_type': 'NATIVE'  # 扫码支付
            }
            
            # 生成签名
            sign = self._generate_wechat_sign(params)
            params['sign'] = sign
            
            # 调用微信统一下单API
            xml_data = self._dict_to_xml(params)
            response = requests.post(
                'https://api.mch.weixin.qq.com/pay/unifiedorder',
                data=xml_data,
                headers={'Content-Type': 'application/xml'},
                timeout=10
            )
            
            # 解析响应
            result = self._xml_to_dict(response.text)
            
            if result.get('return_code') == 'SUCCESS' and result.get('result_code') == 'SUCCESS':
                return {
                    'success': True,
                    'qr_code_url': result.get('code_url'),
                    'prepay_id': result.get('prepay_id'),
                    'order_no': order.order_no
                }
            else:
                return {
                    'success': False,
                    'error': result.get('err_code_des', '支付创建失败')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'微信支付创建失败: {str(e)}'
            }
    
    def create_alipay_payment(self, order: PaymentOrder) -> Dict:
        """创建支付宝支付订单"""
        # 在实际环境中，这里会调用支付宝API
        # 这里提供演示实现
        
        if self.alipay_config['app_id'] == 'demo_app_id':
            # 演示模式
            return self._create_demo_payment(order, PaymentMethod.ALIPAY)
        
        # 实际支付宝支付实现
        try:
            from alipay import AliPay
            
            alipay = AliPay(
                appid=self.alipay_config['app_id'],
                app_notify_url=self.alipay_config['notify_url'],
                app_private_key_string=self.alipay_config['private_key'],
                alipay_public_key_string=self.alipay_config['public_key'],
                sign_type="RSA2",
                debug=settings.DEBUG if hasattr(settings, 'DEBUG') else True
            )
            
            # 创建支付订单
            order_string = alipay.api_alipay_trade_precreate(
                out_trade_no=order.order_no,
                total_amount=str(order.amount),
                subject=f"会员服务-{order.product_type.value}",
                notify_url=self.alipay_config['notify_url']
            )
            
            return {
                'success': True,
                'qr_code_url': order_string.get('qr_code'),
                'order_no': order.order_no
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'支付宝支付创建失败: {str(e)}'
            }
    
    def _create_demo_payment(self, order: PaymentOrder, payment_method: PaymentMethod) -> Dict:
        """创建演示支付订单"""
        # 生成演示二维码URL
        demo_qr_url = f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=demo_payment_{order.order_no}"
        
        return {
            'success': True,
            'qr_code_url': demo_qr_url,
            'order_no': order.order_no,
            'demo_mode': True,
            'message': '演示模式：请使用演示支付功能完成支付'
        }
    
    def process_payment_success(self, db: Session, order_no: str, third_party_order_no: str = None) -> bool:
        """处理支付成功"""
        try:
            # 查找订单
            order = db.query(PaymentOrder).filter(PaymentOrder.order_no == order_no).first()
            if not order:
                return False
            
            # 检查订单状态
            if order.payment_status != PaymentStatus.PENDING:
                return False
            
            # 更新订单状态
            order.payment_status = PaymentStatus.PAID
            order.paid_at = datetime.utcnow()
            if third_party_order_no:
                order.third_party_order_no = third_party_order_no
            
            # 生成激活码
            activation_code = ActivationCode.create_activation_code(
                code_type=order.product_type,
                payment_order_id=order.id,
                created_by_user_id=order.user_id
            )
            
            db.add(activation_code)
            db.commit()
            
            return True
            
        except Exception as e:
            db.rollback()
            print(f"处理支付成功失败: {e}")
            return False
    
    def use_activation_code(self, db: Session, user_id: int, code: str) -> Tuple[bool, str]:
        """使用激活码"""
        try:
            # 查找激活码
            activation_code = db.query(ActivationCode).filter(
                ActivationCode.code == code.upper().replace(' ', '').replace('-', '')
            ).first()
            
            if not activation_code:
                return False, "激活码不存在"
            
            # 检查激活码状态
            if not activation_code.is_usable:
                if activation_code.status == ActivationCodeStatus.USED:
                    return False, "激活码已被使用"
                elif activation_code.is_expired:
                    return False, "激活码已过期"
                else:
                    return False, "激活码无效"
            
            # 获取用户
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return False, "用户不存在"
            
            # 使用激活码
            activation_code.status = ActivationCodeStatus.USED
            activation_code.used_by_user_id = user_id
            activation_code.used_at = datetime.utcnow()
            
            # 更新用户角色
            role_mapping = {
                ActivationCodeType.MONTHLY: UserRole.MONTHLY,
                ActivationCodeType.QUARTERLY: UserRole.QUARTERLY,
                ActivationCodeType.YEARLY: UserRole.YEARLY
            }
            
            user.role = role_mapping[activation_code.code_type]
            
            # 创建或更新订阅记录
            from models.user import UserSubscription, SubscriptionStatus
            
            # 计算订阅时长
            duration_map = {
                ActivationCodeType.MONTHLY: 1,
                ActivationCodeType.QUARTERLY: 3,
                ActivationCodeType.YEARLY: 12
            }
            
            months = duration_map[activation_code.code_type]
            starts_at = datetime.utcnow()
            expires_at = starts_at + timedelta(days=months * 30)
            
            subscription = UserSubscription(
                user_id=user_id,
                subscription_type=user.role,
                status=SubscriptionStatus.ACTIVE,
                starts_at=starts_at,
                expires_at=expires_at,
                payment_method="activation_code",
                amount=str(self.product_prices[activation_code.code_type]),
                currency="CNY"
            )
            
            db.add(subscription)
            db.commit()
            
            return True, f"激活码使用成功，您已升级为{activation_code.code_type.value}会员"
            
        except Exception as e:
            db.rollback()
            return False, f"激活码使用失败: {str(e)}"
    
    def _generate_wechat_sign(self, params: Dict) -> str:
        """生成微信支付签名"""
        # 排序参数
        sorted_params = sorted(params.items())
        query_string = '&'.join([f'{k}={v}' for k, v in sorted_params if v])
        query_string += f'&key={self.wechat_config["api_key"]}'
        
        # MD5签名
        return hashlib.md5(query_string.encode('utf-8')).hexdigest().upper()
    
    def _dict_to_xml(self, data: Dict) -> str:
        """字典转XML"""
        xml = '<xml>'
        for k, v in data.items():
            xml += f'<{k}>{v}</{k}>'
        xml += '</xml>'
        return xml
    
    def _xml_to_dict(self, xml_str: str) -> Dict:
        """XML转字典"""
        import xml.etree.ElementTree as ET
        root = ET.fromstring(xml_str)
        return {child.tag: child.text for child in root}

# 全局支付服务实例
payment_service = PaymentService()
