"""
真实支付服务
集成微信支付和支付宝真实支付接口
"""

import hashlib
import json
import time
import uuid
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Optional, Tuple
import requests
from sqlalchemy.orm import Session
import logging

from models.activation_code import (
    PaymentOrder, ActivationCode, PaymentStatus, PaymentMethod, 
    ActivationCodeType, ActivationCodeStatus
)
from models.user import User, UserRole
from config.payment_config import get_wechat_config, get_alipay_config, is_payment_configured

logger = logging.getLogger(__name__)

class RealPaymentService:
    """真实支付服务类"""
    
    def __init__(self):
        self.wechat_config = get_wechat_config()
        self.alipay_config = get_alipay_config()
        
        # 产品价格配置
        self.product_prices = {
            ActivationCodeType.MONTHLY: Decimal('29.99'),
            ActivationCodeType.QUARTERLY: Decimal('79.99'),
            ActivationCodeType.YEARLY: Decimal('299.99')
        }
        
        # 检查支付配置
        if not is_payment_configured():
            logger.warning("⚠️ 支付未配置，将使用演示模式")
    
    def generate_order_no(self) -> str:
        """生成订单号"""
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4()).replace('-', '')[:8]
        return f"PAY{timestamp}{random_str}".upper()
    
    def create_payment_order(self, db: Session, user_id: int, product_type: ActivationCodeType, 
                           payment_method: PaymentMethod) -> PaymentOrder:
        """创建支付订单"""
        order_no = self.generate_order_no()
        amount = self.product_prices[product_type]
        expires_at = datetime.utcnow() + timedelta(hours=2)  # 订单2小时后过期
        
        order = PaymentOrder(
            order_no=order_no,
            user_id=user_id,
            product_type=product_type,
            amount=amount,
            payment_method=payment_method,
            expires_at=expires_at
        )
        
        db.add(order)
        db.commit()
        db.refresh(order)
        
        return order
    
    def create_wechat_payment(self, order: PaymentOrder) -> Dict:
        """创建微信支付订单"""
        try:
            # 检查配置
            if not self.wechat_config['app_id'] or self.wechat_config['app_id'] == "":
                return self._create_demo_payment(order, PaymentMethod.WECHAT)
            
            # 使用微信支付V3 API
            from wechatpayv3 import WeChatPay, WeChatPayType

            wxpay = WeChatPay(
                wechatpay_type=WeChatPayType.NATIVE,
                mchid=self.wechat_config['mch_id'],
                private_key=self._load_private_key(self.wechat_config['key_path']),
                cert_serial_no=self.wechat_config['serial_no'],
                appid=self.wechat_config['app_id'],
                apiv3_key=self.wechat_config['api_key'],
                notify_url=self.wechat_config['notify_url']
            )

            # 构建支付参数
            code, message = wxpay.pay(
                description=f"会员服务-{order.product_type.value}",
                out_trade_no=order.order_no,
                amount={
                    'total': int(order.amount * 100),  # 微信支付金额单位为分
                    'currency': 'CNY'
                },
                notify_url=self.wechat_config['notify_url']
            )
            
            if code == 200:
                result = json.loads(message)
                return {
                    'success': True,
                    'qr_code_url': result.get('code_url'),
                    'prepay_id': result.get('prepay_id'),
                    'order_no': order.order_no
                }
            else:
                logger.error(f"微信支付创建失败: {code} - {message}")
                return {
                    'success': False,
                    'error': f'微信支付创建失败: {message}'
                }
                
        except ImportError:
            logger.warning("微信支付SDK未安装，使用演示模式")
            return self._create_demo_payment(order, PaymentMethod.WECHAT)
        except Exception as e:
            logger.error(f"微信支付创建失败: {e}")
            return {
                'success': False,
                'error': f'微信支付创建失败: {str(e)}'
            }
    
    def create_alipay_payment(self, order: PaymentOrder) -> Dict:
        """创建支付宝支付订单"""
        try:
            # 检查配置
            if not self.alipay_config['app_id'] or self.alipay_config['app_id'] == "":
                return self._create_demo_payment(order, PaymentMethod.ALIPAY)
            
            from alipay import AliPay
            
            alipay = AliPay(
                appid=self.alipay_config['app_id'],
                app_notify_url=self.alipay_config['notify_url'],
                app_private_key_string=self.alipay_config['private_key'],
                alipay_public_key_string=self.alipay_config['public_key'],
                sign_type="RSA2",
                debug=self.alipay_config['is_sandbox']
            )
            
            # 创建支付订单 - 使用扫码支付
            result = alipay.api_alipay_trade_precreate(
                out_trade_no=order.order_no,
                total_amount=str(order.amount),
                subject=f"会员服务-{order.product_type.value}",
                notify_url=self.alipay_config['notify_url']
            )
            
            if result.get('code') == '10000':
                return {
                    'success': True,
                    'qr_code_url': result.get('qr_code'),
                    'order_no': order.order_no
                }
            else:
                logger.error(f"支付宝支付创建失败: {result}")
                return {
                    'success': False,
                    'error': f"支付宝支付创建失败: {result.get('msg', '未知错误')}"
                }
                
        except ImportError:
            logger.warning("支付宝SDK未安装，使用演示模式")
            return self._create_demo_payment(order, PaymentMethod.ALIPAY)
        except Exception as e:
            logger.error(f"支付宝支付创建失败: {e}")
            return {
                'success': False,
                'error': f'支付宝支付创建失败: {str(e)}'
            }
    
    def _create_demo_payment(self, order: PaymentOrder, payment_method: PaymentMethod) -> Dict:
        """创建演示支付订单"""
        # 生成演示二维码URL
        demo_qr_url = f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=demo_payment_{order.order_no}"
        
        return {
            'success': True,
            'qr_code_url': demo_qr_url,
            'order_no': order.order_no,
            'demo_mode': True,
            'message': '演示模式：请使用演示支付功能完成支付'
        }
    
    def _load_private_key(self, key_path: str) -> str:
        """加载私钥文件"""
        try:
            with open(key_path, 'r') as f:
                return f.read()
        except FileNotFoundError:
            logger.error(f"私钥文件未找到: {key_path}")
            raise
    
    def verify_wechat_callback(self, headers: Dict, body: str) -> bool:
        """验证微信支付回调签名"""
        try:
            from wechatpayv3 import WeChatPay

            # 这里需要实现微信支付回调验证逻辑
            # 具体实现需要根据微信支付V3 API文档
            return True
        except Exception as e:
            logger.error(f"微信支付回调验证失败: {e}")
            return False
    
    def verify_alipay_callback(self, params: Dict) -> bool:
        """验证支付宝回调签名"""
        try:
            from alipay import AliPay
            
            alipay = AliPay(
                appid=self.alipay_config['app_id'],
                app_notify_url=self.alipay_config['notify_url'],
                app_private_key_string=self.alipay_config['private_key'],
                alipay_public_key_string=self.alipay_config['public_key'],
                sign_type="RSA2",
                debug=self.alipay_config['is_sandbox']
            )
            
            # 验证签名
            sign = params.pop('sign', None)
            return alipay.verify(params, sign) if sign else False
            
        except Exception as e:
            logger.error(f"支付宝回调验证失败: {e}")
            return False
    
    def process_payment_success(self, db: Session, order_no: str, third_party_order_no: str = None) -> bool:
        """处理支付成功"""
        try:
            # 查找订单
            order = db.query(PaymentOrder).filter(PaymentOrder.order_no == order_no).first()
            if not order:
                logger.error(f"订单不存在: {order_no}")
                return False
            
            # 检查订单状态
            if order.payment_status != PaymentStatus.PENDING:
                logger.warning(f"订单状态不正确: {order.payment_status}")
                return False
            
            # 更新订单状态
            order.payment_status = PaymentStatus.PAID
            order.paid_at = datetime.utcnow()
            if third_party_order_no:
                order.third_party_order_no = third_party_order_no
            
            # 生成激活码
            activation_code = ActivationCode.create_activation_code(
                code_type=order.product_type,
                payment_order_id=order.id,
                created_by_user_id=order.user_id
            )
            
            db.add(activation_code)
            db.commit()
            
            logger.info(f"支付成功处理完成: {order_no}, 激活码: {activation_code.code}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"处理支付成功失败: {e}")
            return False

# 全局真实支付服务实例
real_payment_service = RealPaymentService()
