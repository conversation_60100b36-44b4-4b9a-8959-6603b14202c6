"""
个人收款码支付服务
支持微信、支付宝个人收款码支付，手动确认付款
"""

import hashlib
import json
import time
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Optional, List
from sqlalchemy.orm import Session
import logging

from models.activation_code import (
    PaymentOrder, ActivationCode, PaymentStatus, PaymentMethod,
    ActivationCodeType, ActivationCodeStatus
)
from models.user import User, UserRole
from config.personal_payment_config import (
    get_payment_qr_codes, get_product_prices, get_random_payment_qr_code, personal_payment_config
)

logger = logging.getLogger(__name__)

class PersonalPaymentService:
    """个人收款码支付服务类"""
    
    def __init__(self):
        # 从配置获取产品价格
        prices = get_product_prices()
        self.product_prices = {
            ActivationCodeType.MONTHLY: Decimal(str(prices['monthly'])),
            ActivationCodeType.QUARTERLY: Decimal(str(prices['quarterly'])),
            ActivationCodeType.YEARLY: Decimal(str(prices['yearly']))
        }

        # 个人收款码将在创建支付时随机选择
    
    def generate_order_no(self) -> str:
        """生成订单号"""
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4()).replace('-', '')[:8]
        return f"PAY{timestamp}{random_str}".upper()
    
    def generate_payment_reference(self, order_no: str) -> str:
        """生成付款备注（用于识别付款）"""
        # 使用订单号后8位作为付款备注
        return f"CZ{order_no[-8:]}"
    
    def create_payment_order(self, db: Session, user_id: int, product_type: ActivationCodeType, 
                           payment_method: PaymentMethod) -> PaymentOrder:
        """创建支付订单"""
        order_no = self.generate_order_no()
        amount = self.product_prices[product_type]
        expires_at = datetime.utcnow() + timedelta(hours=24)  # 订单24小时后过期
        
        order = PaymentOrder(
            order_no=order_no,
            user_id=user_id,
            product_type=product_type,
            amount=amount,
            payment_method=payment_method,
            expires_at=expires_at
        )
        
        db.add(order)
        db.commit()
        db.refresh(order)
        
        return order
    
    def create_personal_payment(self, order: PaymentOrder, qr_code_index: int = None) -> Dict:
        """创建个人收款码支付"""
        try:
            # 获取收款码配置
            payment_method_str = order.payment_method.value
            all_qr_codes = get_payment_qr_codes()

            if payment_method_str not in all_qr_codes or not all_qr_codes[payment_method_str]:
                raise ValueError(f"未配置{payment_method_str}收款码")

            # 选择收款码：如果指定了索引则使用指定的，否则随机选择
            qr_codes_list = all_qr_codes[payment_method_str]
            if qr_code_index is not None and 0 <= qr_code_index < len(qr_codes_list):
                payment_config = qr_codes_list[qr_code_index]
            else:
                payment_config = get_random_payment_qr_code(payment_method_str)

            # 生成付款备注
            payment_reference = self.generate_payment_reference(order.order_no)

            return {
                'success': True,
                'order_no': order.order_no,
                'amount': float(order.amount),
                'payment_method': order.payment_method.value,
                'qr_code_url': payment_config['qr_code_url'],
                'account_name': payment_config['account_name'],
                'account_info': payment_config['account_info'],
                'payment_reference': payment_reference,
                'expires_at': order.expires_at.isoformat(),
                'instructions': self._get_payment_instructions(order.payment_method, payment_reference, order.amount)
            }
            
        except Exception as e:
            logger.error(f"创建个人收款码支付失败: {e}")
            return {
                'success': False,
                'error': f'创建支付失败: {str(e)}'
            }
    
    def _get_payment_instructions(self, payment_method: PaymentMethod, reference: str, amount: Decimal) -> List[str]:
        """获取支付说明"""
        method_name = "微信" if payment_method == PaymentMethod.WECHAT else "支付宝"
        
        return [
            f"1. 打开{method_name}扫描上方二维码",
            f"2. 确认支付金额为 ¥{amount}",
            f"3. 在付款备注中填写：{reference}",
            f"4. 完成付款后点击下方「我已付款」按钮",
            f"5. 等待管理员确认，确认后将自动发放激活码"
        ]
    
    def mark_payment_submitted(self, db: Session, order_no: str, user_note: str = None) -> bool:
        """标记用户已提交付款"""
        try:
            order = db.query(PaymentOrder).filter(PaymentOrder.order_no == order_no).first()
            if not order:
                return False
            
            if order.payment_status != PaymentStatus.PENDING:
                return False
            
            # 更新订单状态为已提交付款
            order.payment_status = PaymentStatus.SUBMITTED
            order.user_note = user_note
            order.submitted_at = datetime.utcnow()
            
            db.commit()
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"标记付款提交失败: {e}")
            return False
    
    def confirm_payment(self, db: Session, order_no: str, admin_user_id: int, admin_note: str = None) -> bool:
        """管理员确认付款"""
        try:
            order = db.query(PaymentOrder).filter(PaymentOrder.order_no == order_no).first()
            if not order:
                return False
            
            if order.payment_status != PaymentStatus.SUBMITTED:
                return False
            
            # 更新订单状态为已支付
            order.payment_status = PaymentStatus.PAID
            order.paid_at = datetime.utcnow()
            order.confirmed_by = admin_user_id
            order.admin_note = admin_note
            
            # 生成激活码
            activation_code = ActivationCode.create_activation_code(
                code_type=order.product_type,
                payment_order_id=order.id,
                created_by_user_id=admin_user_id
            )
            
            db.add(activation_code)
            db.commit()
            
            logger.info(f"付款确认成功: {order_no}, 激活码: {activation_code.code}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"确认付款失败: {e}")
            return False
    
    def reject_payment(self, db: Session, order_no: str, admin_user_id: int, reason: str) -> bool:
        """管理员拒绝付款"""
        try:
            order = db.query(PaymentOrder).filter(PaymentOrder.order_no == order_no).first()
            if not order:
                return False
            
            if order.payment_status != PaymentStatus.SUBMITTED:
                return False
            
            # 更新订单状态为已拒绝
            order.payment_status = PaymentStatus.REJECTED
            order.rejected_at = datetime.utcnow()
            order.confirmed_by = admin_user_id
            order.admin_note = reason
            
            db.commit()
            
            logger.info(f"付款已拒绝: {order_no}, 原因: {reason}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"拒绝付款失败: {e}")
            return False
    
    def get_pending_payments(self, db: Session, limit: int = 50) -> List[Dict]:
        """获取待确认的付款订单"""
        try:
            orders = db.query(PaymentOrder).filter(
                PaymentOrder.payment_status == PaymentStatus.SUBMITTED
            ).order_by(PaymentOrder.submitted_at.desc()).limit(limit).all()
            
            result = []
            for order in orders:
                user = db.query(User).filter(User.id == order.user_id).first()
                result.append({
                    'order_no': order.order_no,
                    'user_id': order.user_id,
                    'username': user.username if user else 'Unknown',
                    'product_type': order.product_type.value,
                    'amount': float(order.amount),
                    'payment_method': order.payment_method.value,
                    'payment_reference': self.generate_payment_reference(order.order_no),
                    'submitted_at': order.submitted_at.isoformat() if order.submitted_at else None,
                    'user_note': order.user_note,
                    'expires_at': order.expires_at.isoformat()
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取待确认付款失败: {e}")
            return []
    
    def get_payment_history(self, db: Session, user_id: int = None, limit: int = 50) -> List[Dict]:
        """获取付款历史"""
        try:
            query = db.query(PaymentOrder)
            if user_id:
                query = query.filter(PaymentOrder.user_id == user_id)
            
            orders = query.order_by(PaymentOrder.created_at.desc()).limit(limit).all()
            
            result = []
            for order in orders:
                user = db.query(User).filter(User.id == order.user_id).first()
                result.append({
                    'order_no': order.order_no,
                    'user_id': order.user_id,
                    'username': user.username if user else 'Unknown',
                    'product_type': order.product_type.value,
                    'amount': float(order.amount),
                    'payment_method': order.payment_method.value,
                    'payment_status': order.payment_status.value,
                    'payment_reference': self.generate_payment_reference(order.order_no),
                    'created_at': order.created_at.isoformat(),
                    'submitted_at': order.submitted_at.isoformat() if order.submitted_at else None,
                    'paid_at': order.paid_at.isoformat() if order.paid_at else None,
                    'user_note': order.user_note,
                    'admin_note': order.admin_note
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取付款历史失败: {e}")
            return []
    
    def use_activation_code(self, db: Session, user_id: int, code: str) -> tuple[bool, str]:
        """使用激活码"""
        try:
            # 查找激活码
            activation_code = db.query(ActivationCode).filter(
                ActivationCode.code == code,
                ActivationCode.status == ActivationCodeStatus.UNUSED
            ).first()
            
            if not activation_code:
                return False, "激活码不存在或已使用"
            
            # 检查激活码是否过期
            if activation_code.expires_at and activation_code.expires_at < datetime.utcnow():
                return False, "激活码已过期"
            
            # 使用激活码
            success = activation_code.use_code(user_id)
            if not success:
                return False, "激活码使用失败"
            
            db.commit()
            
            return True, f"激活码使用成功，已激活{activation_code.code_type.value}会员"
            
        except Exception as e:
            db.rollback()
            logger.error(f"使用激活码失败: {e}")
            return False, f"激活码使用失败: {str(e)}"

# 全局个人支付服务实例
personal_payment_service = PersonalPaymentService()
