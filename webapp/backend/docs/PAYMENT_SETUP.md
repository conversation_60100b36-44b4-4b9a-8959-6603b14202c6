# 真实支付配置指南

本文档将指导您如何配置真实的微信支付和支付宝支付。

## 📋 前置条件

### 微信支付
1. 已注册微信商户平台账号
2. 已完成商户认证
3. 已开通Native支付（扫码支付）功能
4. 已获取API证书

### 支付宝
1. 已注册支付宝开放平台账号
2. 已创建应用并通过审核
3. 已开通当面付功能
4. 已配置应用公钥和获取支付宝公钥

## 🔧 配置步骤

### 1. 安装支付SDK

```bash
pip install python-alipay-sdk wechatpay-python xmltodict
```

### 2. 配置环境变量

在项目根目录的 `.env` 文件中添加以下配置：

```env
# 支付模式: sandbox(沙箱) 或 production(生产)
PAYMENT_PAYMENT_MODE=sandbox

# 微信支付配置
PAYMENT_WECHAT_APP_ID=your_wechat_app_id
PAYMENT_WECHAT_MCH_ID=your_merchant_id
PAYMENT_WECHAT_API_KEY=your_api_key_v3
PAYMENT_WECHAT_CERT_PATH=certs/apiclient_cert.pem
PAYMENT_WECHAT_KEY_PATH=certs/apiclient_key.pem
PAYMENT_WECHAT_SERIAL_NO=your_cert_serial_no

# 支付宝配置
PAYMENT_ALIPAY_APP_ID=your_alipay_app_id
PAYMENT_ALIPAY_PRIVATE_KEY=your_rsa_private_key
PAYMENT_ALIPAY_PUBLIC_KEY=alipay_rsa_public_key

# 服务器地址（用于回调）
PAYMENT_BASE_URL=https://yourdomain.com
```

### 3. 微信支付配置详解

#### 3.1 获取配置信息
1. 登录[微信商户平台](https://pay.weixin.qq.com/)
2. 在"账户中心 > API安全"中下载API证书
3. 记录以下信息：
   - `WECHAT_APP_ID`: 应用ID
   - `WECHAT_MCH_ID`: 商户号
   - `WECHAT_API_KEY`: APIv3密钥
   - `WECHAT_SERIAL_NO`: 证书序列号

#### 3.2 证书文件配置
1. 将下载的证书文件放在 `webapp/backend/certs/` 目录下
2. 确保文件权限正确（600）

```bash
mkdir -p webapp/backend/certs
chmod 600 webapp/backend/certs/*
```

#### 3.3 配置回调地址
在微信商户平台配置支付回调地址：
```
https://yourdomain.com/api/v1/payment/wechat/notify
```

### 4. 支付宝配置详解

#### 4.1 获取配置信息
1. 登录[支付宝开放平台](https://open.alipay.com/)
2. 创建应用并获取APPID
3. 配置应用公钥，获取支付宝公钥
4. 记录以下信息：
   - `ALIPAY_APP_ID`: 应用APPID
   - `ALIPAY_PRIVATE_KEY`: 应用私钥
   - `ALIPAY_PUBLIC_KEY`: 支付宝公钥

#### 4.2 生成RSA密钥对
```bash
# 生成私钥
openssl genrsa -out rsa_private_key.pem 2048

# 生成公钥
openssl rsa -in rsa_private_key.pem -pubout -out rsa_public_key.pem
```

#### 4.3 配置回调地址
在支付宝开放平台配置支付回调地址：
```
https://yourdomain.com/api/v1/payment/alipay/notify
```

## 🧪 测试配置

### 1. 检查配置状态
访问API端点检查配置状态：
```
GET /api/v1/payment/config/status
```

### 2. 沙箱测试
- 微信支付：使用微信支付沙箱环境
- 支付宝：使用支付宝沙箱环境

### 3. 生产环境
将 `PAYMENT_PAYMENT_MODE` 设置为 `production`

## 🔒 安全注意事项

1. **证书安全**：
   - 证书文件权限设置为600
   - 不要将证书文件提交到版本控制系统

2. **密钥安全**：
   - 使用环境变量存储敏感信息
   - 定期更换API密钥

3. **回调验证**：
   - 必须验证支付回调的签名
   - 检查订单状态，避免重复处理

4. **HTTPS**：
   - 生产环境必须使用HTTPS
   - 确保回调地址可以被支付平台访问

## 🚀 部署清单

部署到生产环境前，请确认：

- [ ] 已配置所有必要的环境变量
- [ ] 证书文件已正确放置且权限正确
- [ ] 回调地址已在支付平台配置
- [ ] 已测试支付流程
- [ ] 已配置HTTPS
- [ ] 已设置防火墙规则

## 📞 技术支持

如果遇到配置问题，请参考：
- [微信支付开发文档](https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml)
- [支付宝开放平台文档](https://opendocs.alipay.com/)

## 🔄 从演示模式切换到真实支付

1. 完成上述配置
2. 重启后端服务
3. 系统将自动检测配置并切换到真实支付模式
4. 演示支付功能将被禁用
