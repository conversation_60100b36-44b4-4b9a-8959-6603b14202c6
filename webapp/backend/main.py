"""
Coin-Analyze Backend API Server
专业加密货币数据分析平台后端服务
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "src"))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
from contextlib import asynccontextmanager
import asyncio
import logging
from datetime import datetime

# 导入API路由
from api.market import router as market_router
from api.prediction import router as prediction_router
from api.analysis import router as analysis_router
from api.websocket import router as websocket_router
from api.backtest import router as backtest_router
from api.news import router as news_router
from api.auth import router as auth_router
from api.payment import router as payment_router
from api.personal_payment import router as personal_payment_router
from api.admin_stats import router as admin_stats_router
from api.admin_payments import router as admin_payments_router
from app.api.v1.admin.admin_analytics import router as admin_analytics_router

# 导入核心服务
from core.prediction_service import PredictionService
from core.market_service import MarketService
from config.simple_settings import settings
from models.database import init_db

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局服务实例
prediction_service = None
market_service = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global prediction_service, market_service
    
    logger.info("🚀 启动 Coin-Analyze 后端服务...")

    try:
        # 初始化数据库
        logger.info("📊 初始化数据库...")
        init_db()

        # 初始化核心服务
        logger.info("📊 初始化预测服务...")
        prediction_service = PredictionService()
        
        logger.info("📈 初始化市场数据服务...")
        market_service = MarketService()
        
        # 启动后台任务
        logger.info("⚡ 启动实时数据更新任务...")
        asyncio.create_task(market_service.start_real_time_updates())
        
        logger.info("✅ 所有服务初始化完成")
        
    except Exception as e:
        logger.error(f"❌ 服务初始化失败: {e}")
        raise
    
    yield
    
    # 清理资源
    logger.info("🔄 正在关闭服务...")
    if market_service:
        await market_service.stop_real_time_updates()
    logger.info("👋 服务已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="Coin-Analyze API",
    description="专业加密货币数据分析平台API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS + ["*"] if settings.DEBUG else settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(auth_router, prefix="/api/v1/auth", tags=["用户认证"])
app.include_router(payment_router, prefix="/api/v1/payment", tags=["支付管理"])
app.include_router(personal_payment_router, prefix="/api/v1", tags=["个人收款码支付"])
app.include_router(admin_stats_router, prefix="/api/v1", tags=["管理员统计"])
app.include_router(admin_payments_router, prefix="/api/v1", tags=["管理员付费管理"])
app.include_router(admin_analytics_router, prefix="/api/v1/admin", tags=["管理员分析统计"])
app.include_router(market_router, prefix="/api/v1/market", tags=["市场数据"])
app.include_router(prediction_router, prefix="/api/v1/prediction", tags=["AI预测"])
app.include_router(analysis_router, prefix="/api/v1/analysis", tags=["技术分析"])
app.include_router(websocket_router, prefix="/ws", tags=["WebSocket"])
app.include_router(backtest_router, tags=["回测分析"])
app.include_router(news_router, tags=["新闻研究"])

# 静态文件服务
# 创建静态文件目录（如果不存在）
static_dir = Path("static")
static_dir.mkdir(exist_ok=True)
images_dir = static_dir / "images"
images_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径 - 返回API信息"""
    return f"""
    <html>
        <head>
            <title>Coin-Analyze API</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: #fff; }}
                .container {{ max-width: 800px; margin: 0 auto; }}
                .header {{ text-align: center; margin-bottom: 40px; }}
                .status {{ background: #2d3748; padding: 20px; border-radius: 8px; margin: 20px 0; }}
                .links {{ display: flex; gap: 20px; justify-content: center; margin: 30px 0; }}
                .link {{ background: #4299e1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }}
                .link:hover {{ background: #3182ce; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🪙 Coin-Analyze API</h1>
                    <p>专业加密货币数据分析平台</p>
                </div>
                
                <div class="status">
                    <h3>📊 服务状态</h3>
                    <p>✅ API服务运行正常</p>
                    <p>🕐 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p>🔧 版本: v1.0.0</p>
                </div>
                
                <div class="links">
                    <a href="/docs" class="link">📚 API文档</a>
                    <a href="/redoc" class="link">📖 ReDoc文档</a>
                    <a href="/api/v1/market/overview" class="link">📈 市场概览</a>
                </div>
                
                <div class="status">
                    <h3>🚀 核心功能</h3>
                    <ul>
                        <li>实时市场数据获取</li>
                        <li>AI智能预测分析</li>
                        <li>技术指标计算</li>
                        <li>WebSocket实时推送</li>
                        <li>多币种支持</li>
                    </ul>
                </div>
            </div>
        </body>
    </html>
    """

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "services": {
            "prediction": prediction_service is not None,
            "market": market_service is not None
        }
    }

@app.get("/api/v1/info")
async def api_info():
    """API信息端点"""
    return {
        "name": "Coin-Analyze API",
        "version": "1.0.0",
        "description": "专业加密货币数据分析平台API",
        "endpoints": {
            "market": "/api/v1/market/*",
            "prediction": "/api/v1/prediction/*", 
            "analysis": "/api/v1/analysis/*",
            "websocket": "/ws/*"
        },
        "documentation": {
            "swagger": "/docs",
            "redoc": "/redoc"
        }
    }

if __name__ == "__main__":
    # 开发环境启动
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
