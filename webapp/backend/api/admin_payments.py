"""
管理员付费管理API路由
提供付费记录查询、备注管理、收款码统计等功能
"""

from datetime import datetime, timedelta, date
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from models.database import get_db
from models.user import User, UserRole, UserSubscription, SubscriptionStatus
from models.activation_code import PaymentOrder, PaymentStatus, PaymentMethod, ActivationCodeType, ActivationCode
from auth.security import require_admin_access

router = APIRouter(prefix="/admin/payments", tags=["管理员付费管理"])

# 请求模型
class UpdatePaymentNoteRequest(BaseModel):
    order_no: str
    admin_note: str

class PaymentRecordFilter(BaseModel):
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    payment_status: Optional[str] = None
    payment_method: Optional[str] = None
    product_type: Optional[str] = None
    user_id: Optional[int] = None

# 响应模型
class PaymentRecordDetail(BaseModel):
    id: int
    order_no: str
    user_id: int
    username: str
    user_email: str
    product_type: str
    amount: float
    currency: str
    payment_method: str
    payment_status: str
    user_note: Optional[str]
    admin_note: Optional[str]
    created_at: str
    submitted_at: Optional[str]
    paid_at: Optional[str]
    expires_at: Optional[str]
    confirmed_by_username: Optional[str]
    activation_codes: List[str]

class PaymentSummary(BaseModel):
    total_records: int
    total_amount: float
    status_breakdown: Dict[str, int]
    method_breakdown: Dict[str, int]
    product_breakdown: Dict[str, int]

class PaymentRecordsResponse(BaseModel):
    records: List[PaymentRecordDetail]
    summary: PaymentSummary
    total_pages: int
    current_page: int

@router.get("/records", response_model=PaymentRecordsResponse)
async def get_payment_records(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页记录数"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    payment_status: Optional[str] = Query(None, description="支付状态"),
    payment_method: Optional[str] = Query(None, description="支付方式"),
    product_type: Optional[str] = Query(None, description="产品类型"),
    user_id: Optional[int] = Query(None, description="用户ID"),
    search: Optional[str] = Query(None, description="搜索关键词（用户名、邮箱、订单号）"),
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """获取付费记录列表"""
    # 构建查询
    query = db.query(PaymentOrder).join(User, PaymentOrder.user_id == User.id)
    
    # 日期过滤
    if start_date:
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            query = query.filter(PaymentOrder.created_at >= start_dt)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="开始日期格式错误，请使用 YYYY-MM-DD 格式"
            )
    
    if end_date:
        try:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            query = query.filter(PaymentOrder.created_at < end_dt)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="结束日期格式错误，请使用 YYYY-MM-DD 格式"
            )
    
    # 状态过滤
    if payment_status:
        try:
            status_enum = PaymentStatus(payment_status)
            query = query.filter(PaymentOrder.payment_status == status_enum)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的支付状态: {payment_status}"
            )
    
    # 支付方式过滤
    if payment_method:
        try:
            method_enum = PaymentMethod(payment_method)
            query = query.filter(PaymentOrder.payment_method == method_enum)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的支付方式: {payment_method}"
            )
    
    # 产品类型过滤
    if product_type:
        try:
            product_enum = ActivationCodeType(product_type)
            query = query.filter(PaymentOrder.product_type == product_enum)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的产品类型: {product_type}"
            )
    
    # 用户ID过滤
    if user_id:
        query = query.filter(PaymentOrder.user_id == user_id)
    
    # 搜索过滤
    if search:
        search_filter = or_(
            User.username.ilike(f"%{search}%"),
            User.email.ilike(f"%{search}%"),
            PaymentOrder.order_no.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    # 获取总数
    total_count = query.count()
    
    # 分页
    offset = (page - 1) * page_size
    orders = query.order_by(desc(PaymentOrder.created_at)).offset(offset).limit(page_size).all()
    
    # 构建详细记录
    records = []
    for order in orders:
        # 获取确认者用户名
        confirmed_by_username = None
        if order.confirmed_by:
            confirmed_user = db.query(User).filter(User.id == order.confirmed_by).first()
            if confirmed_user:
                confirmed_by_username = confirmed_user.username
        
        # 获取激活码
        activation_codes = [code.code for code in order.activation_codes]
        
        records.append(PaymentRecordDetail(
            id=order.id,
            order_no=order.order_no,
            user_id=order.user_id,
            username=order.user.username,
            user_email=order.user.email,
            product_type=order.product_type.value,
            amount=float(order.amount),
            currency=order.currency,
            payment_method=order.payment_method.value,
            payment_status=order.payment_status.value,
            user_note=order.user_note,
            admin_note=order.admin_note,
            created_at=order.created_at.isoformat(),
            submitted_at=order.submitted_at.isoformat() if order.submitted_at else None,
            paid_at=order.paid_at.isoformat() if order.paid_at else None,
            expires_at=order.expires_at.isoformat() if order.expires_at else None,
            confirmed_by_username=confirmed_by_username,
            activation_codes=activation_codes
        ))
    
    # 计算统计信息
    all_orders = query.all()
    total_amount = sum(float(order.amount) for order in all_orders)
    
    status_breakdown = {}
    method_breakdown = {}
    product_breakdown = {}
    
    for order in all_orders:
        # 状态统计
        status = order.payment_status.value
        status_breakdown[status] = status_breakdown.get(status, 0) + 1
        
        # 支付方式统计
        method = order.payment_method.value
        method_breakdown[method] = method_breakdown.get(method, 0) + 1
        
        # 产品类型统计
        product = order.product_type.value
        product_breakdown[product] = product_breakdown.get(product, 0) + 1
    
    summary = PaymentSummary(
        total_records=total_count,
        total_amount=total_amount,
        status_breakdown=status_breakdown,
        method_breakdown=method_breakdown,
        product_breakdown=product_breakdown
    )
    
    total_pages = (total_count + page_size - 1) // page_size
    
    return PaymentRecordsResponse(
        records=records,
        summary=summary,
        total_pages=total_pages,
        current_page=page
    )

@router.put("/notes")
async def update_payment_note(
    request: UpdatePaymentNoteRequest,
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """更新付费记录的管理员备注"""
    order = db.query(PaymentOrder).filter(PaymentOrder.order_no == request.order_no).first()
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    order.admin_note = request.admin_note
    db.commit()
    
    return {
        "success": True,
        "message": "备注更新成功",
        "order_no": request.order_no,
        "admin_note": request.admin_note
    }

@router.get("/by-qr-code")
async def get_payments_by_qr_code(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """按收款码统计付费记录"""
    end_date = date.today()
    start_date = end_date - timedelta(days=days-1)
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    # 查询付费订单
    paid_orders = db.query(PaymentOrder).filter(
        PaymentOrder.payment_status == PaymentStatus.PAID,
        PaymentOrder.paid_at >= start_datetime,
        PaymentOrder.paid_at <= end_datetime
    ).all()
    
    # 按收款码分组统计
    qr_stats = {}
    
    for order in paid_orders:
        # 构建收款码标识
        qr_key = f"{order.payment_method.value}"
        if order.user_note:
            qr_key += f"_{order.user_note[:20]}"
        
        if qr_key not in qr_stats:
            qr_stats[qr_key] = {
                'payment_method': order.payment_method.value,
                'identifier': order.user_note or "默认收款码",
                'count': 0,
                'total_amount': 0.0,
                'orders': []
            }
        
        qr_stats[qr_key]['count'] += 1
        qr_stats[qr_key]['total_amount'] += float(order.amount)
        qr_stats[qr_key]['orders'].append({
            'order_no': order.order_no,
            'amount': float(order.amount),
            'paid_at': order.paid_at.isoformat(),
            'username': order.user.username
        })
    
    return {
        'period': f"{start_date.isoformat()} 至 {end_date.isoformat()}",
        'qr_code_stats': list(qr_stats.values())
    }
