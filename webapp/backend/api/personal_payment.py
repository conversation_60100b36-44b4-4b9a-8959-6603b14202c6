"""
个人收款码支付API
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from pydantic import BaseModel

from models.database import get_db
from auth.security import get_current_active_user, get_current_admin_user
from models.user import User
from models.activation_code import ActivationCodeType, PaymentMethod, PaymentStatus
from services.personal_payment_service import personal_payment_service
from config.personal_payment_config import get_payment_qr_codes

router = APIRouter(prefix="/personal-payment", tags=["个人收款码支付"])

# 请求模型
class PersonalPaymentCreateRequest(BaseModel):
    product_type: ActivationCodeType
    payment_method: PaymentMethod
    qr_code_index: int = None  # 可选：指定使用哪个收款码（索引）

class PaymentSubmitRequest(BaseModel):
    order_no: str
    user_note: str = None

class PaymentConfirmRequest(BaseModel):
    order_no: str
    admin_note: str = None

class PaymentRejectRequest(BaseModel):
    order_no: str
    reason: str

class ActivationCodeUseRequest(BaseModel):
    code: str

# 响应模型
class PersonalPaymentCreateResponse(BaseModel):
    success: bool
    order_no: str = None
    amount: float = None
    payment_method: str = None
    qr_code_url: str = None
    account_name: str = None
    account_info: str = None
    payment_reference: str = None
    expires_at: str = None
    instructions: List[str] = None
    error: str = None

class PaymentOperationResponse(BaseModel):
    success: bool
    message: str

class PendingPaymentItem(BaseModel):
    order_no: str
    user_id: int
    username: str
    product_type: str
    amount: float
    payment_method: str
    payment_reference: str
    submitted_at: str = None
    user_note: str = None
    expires_at: str

class PaymentHistoryItem(BaseModel):
    order_no: str
    user_id: int
    username: str
    product_type: str
    amount: float
    payment_method: str
    payment_status: str
    payment_reference: str
    created_at: str
    submitted_at: str = None
    paid_at: str = None
    user_note: str = None
    admin_note: str = None

@router.get("/qr-codes")
def get_qr_codes():
    """获取所有可用的收款码选项"""
    try:
        qr_codes = get_payment_qr_codes()
        return {
            "success": True,
            "qr_codes": qr_codes
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取收款码失败: {str(e)}"
        )

@router.post("/create", response_model=PersonalPaymentCreateResponse)
async def create_personal_payment(
    request: PersonalPaymentCreateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建个人收款码支付订单"""
    try:
        # 创建支付订单
        order = personal_payment_service.create_payment_order(
            db=db,
            user_id=current_user.id,
            product_type=request.product_type,
            payment_method=request.payment_method
        )
        
        # 创建个人收款码支付
        payment_result = personal_payment_service.create_personal_payment(order, request.qr_code_index)
        
        if payment_result['success']:
            return PersonalPaymentCreateResponse(**payment_result)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=payment_result['error']
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建支付订单失败: {str(e)}"
        )

@router.post("/submit", response_model=PaymentOperationResponse)
async def submit_payment(
    request: PaymentSubmitRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """用户提交付款确认"""
    try:
        success = personal_payment_service.mark_payment_submitted(
            db=db,
            order_no=request.order_no,
            user_note=request.user_note
        )
        
        if success:
            return PaymentOperationResponse(
                success=True,
                message="付款提交成功，请等待管理员确认"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="付款提交失败，订单不存在或状态不正确"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交付款失败: {str(e)}"
        )

@router.post("/confirm", response_model=PaymentOperationResponse)
async def confirm_payment(
    request: PaymentConfirmRequest,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """管理员确认付款"""
    try:
        success = personal_payment_service.confirm_payment(
            db=db,
            order_no=request.order_no,
            admin_user_id=current_admin.id,
            admin_note=request.admin_note
        )
        
        if success:
            return PaymentOperationResponse(
                success=True,
                message="付款确认成功，激活码已生成"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="付款确认失败，订单不存在或状态不正确"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认付款失败: {str(e)}"
        )

@router.post("/reject", response_model=PaymentOperationResponse)
async def reject_payment(
    request: PaymentRejectRequest,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """管理员拒绝付款"""
    try:
        success = personal_payment_service.reject_payment(
            db=db,
            order_no=request.order_no,
            admin_user_id=current_admin.id,
            reason=request.reason
        )
        
        if success:
            return PaymentOperationResponse(
                success=True,
                message="付款已拒绝"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="付款拒绝失败，订单不存在或状态不正确"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"拒绝付款失败: {str(e)}"
        )

@router.get("/pending", response_model=List[PendingPaymentItem])
async def get_pending_payments(
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """获取待确认的付款订单（管理员）"""
    try:
        payments = personal_payment_service.get_pending_payments(db=db)
        return [PendingPaymentItem(**payment) for payment in payments]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取待确认付款失败: {str(e)}"
        )

@router.get("/history", response_model=List[PaymentHistoryItem])
async def get_payment_history(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户付款历史"""
    try:
        payments = personal_payment_service.get_payment_history(
            db=db, 
            user_id=current_user.id
        )
        return [PaymentHistoryItem(**payment) for payment in payments]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取付款历史失败: {str(e)}"
        )

@router.get("/history/all", response_model=List[PaymentHistoryItem])
async def get_all_payment_history(
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """获取所有付款历史（管理员）"""
    try:
        payments = personal_payment_service.get_payment_history(db=db)
        return [PaymentHistoryItem(**payment) for payment in payments]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取付款历史失败: {str(e)}"
        )

@router.post("/use-code", response_model=PaymentOperationResponse)
async def use_activation_code(
    request: ActivationCodeUseRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """使用激活码"""
    try:
        success, message = personal_payment_service.use_activation_code(
            db=db,
            user_id=current_user.id,
            code=request.code
        )
        
        return PaymentOperationResponse(
            success=success,
            message=message
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"使用激活码失败: {str(e)}"
        )
