"""
新闻与研究API
提供加密货币相关新闻和研究报告的接口
"""

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from typing import List, Optional
from datetime import datetime, timedelta
import asyncio
import logging
from pydantic import BaseModel
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from core.news_service import news_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/news", tags=["news"])

# 数据模型
class NewsItem(BaseModel):
    id: int
    title: str
    summary: str
    content: Optional[str] = None
    category: str
    source: str
    publishTime: str
    readTime: str
    image: Optional[str] = None
    trending: bool = False
    views: int = 0
    tags: List[str] = []
    url: Optional[str] = None

class ResearchReport(BaseModel):
    id: int
    title: str
    description: str
    author: str
    publishDate: str
    pages: int
    downloads: int
    rating: float
    category: str
    featured: bool = False
    fileUrl: Optional[str] = None
    previewUrl: Optional[str] = None

class NewsResponse(BaseModel):
    success: bool
    data: List[NewsItem]
    total: int
    page: int
    limit: int

class ResearchResponse(BaseModel):
    success: bool
    data: List[ResearchReport]
    total: int
    page: int
    limit: int

class SubscribeRequest(BaseModel):
    email: str
    categories: List[str] = []

# 模拟数据
MOCK_NEWS = [
    NewsItem(
        id=1,
        title="比特币突破新高，机构投资者大量涌入",
        summary="比特币价格再次创下历史新高，主要由机构投资者的大量买入推动。分析师认为这一趋势将持续到2024年底，预计价格可能达到15万美元。",
        content="比特币在过去24小时内上涨了8.5%，突破了此前的历史高点。这一轮上涨主要由机构投资者推动，包括多家知名对冲基金和企业财务部门的大量买入...",
        category="市场动态",
        source="CoinDesk",
        publishTime="2小时前",
        readTime="3分钟",
        trending=True,
        views=1234,
        tags=["比特币", "机构投资", "价格分析", "市场趋势"],
        url="https://example.com/news/1"
    ),
    NewsItem(
        id=2,
        title="以太坊2.0升级完成，网络性能大幅提升",
        summary="以太坊网络完成重大升级，交易速度提升300%，手续费降低80%。这一升级标志着以太坊生态的重要里程碑。",
        content="以太坊网络的最新升级已经成功部署，带来了显著的性能改进。网络的交易处理能力从每秒15笔提升到45笔，同时平均交易费用从50美元降低到10美元...",
        category="技术更新",
        source="Ethereum Foundation",
        publishTime="4小时前",
        readTime="5分钟",
        trending=False,
        views=892,
        tags=["以太坊", "技术升级", "性能优化", "网络升级"],
        url="https://example.com/news/2"
    ),
    NewsItem(
        id=3,
        title="DeFi协议总锁仓量突破1000亿美元",
        summary="去中心化金融(DeFi)生态系统继续快速增长，总锁仓量(TVL)首次突破1000亿美元大关。",
        content="DeFi生态系统在2024年展现出强劲的增长势头，总锁仓量(TVL)达到了1000亿美元的里程碑。这一增长主要由新兴协议的创新和传统金融机构的参与推动...",
        category="DeFi",
        source="DeFi Pulse",
        publishTime="6小时前",
        readTime="4分钟",
        trending=True,
        views=756,
        tags=["DeFi", "TVL", "生态发展", "去中心化金融"],
        url="https://example.com/news/3"
    ),
    NewsItem(
        id=4,
        title="央行数字货币(CBDC)试点项目扩大规模",
        summary="多个国家的央行数字货币试点项目进入新阶段，预计将在未来两年内正式推出。",
        content="全球多个央行正在加速推进数字货币项目。中国的数字人民币试点范围进一步扩大，欧洲央行的数字欧元项目也进入了技术测试阶段...",
        category="政策法规",
        source="央行公告",
        publishTime="8小时前",
        readTime="6分钟",
        trending=False,
        views=543,
        tags=["CBDC", "央行", "数字货币", "政策"],
        url="https://example.com/news/4"
    ),
    NewsItem(
        id=5,
        title="NFT市场回暖，艺术品交易量激增",
        summary="NFT市场在经历低迷期后开始回暖，艺术品类NFT交易量环比增长150%。",
        content="NFT市场在2024年第一季度显示出复苏迹象。艺术品类NFT的交易量大幅增长，主要由高质量项目和知名艺术家的参与推动...",
        category="NFT",
        source="OpenSea",
        publishTime="12小时前",
        readTime="3分钟",
        trending=False,
        views=432,
        tags=["NFT", "艺术品", "市场回暖", "数字收藏品"],
        url="https://example.com/news/5"
    )
]

MOCK_RESEARCH = [
    ResearchReport(
        id=1,
        title="2024年加密货币市场深度分析报告",
        description="全面分析2024年加密货币市场趋势、技术发展和投资机会，包含详细的数据分析和专业预测。",
        author="区块链研究院",
        publishDate="2024-01-15",
        pages=45,
        downloads=2341,
        rating=4.8,
        category="市场分析",
        featured=True,
        fileUrl="/downloads/crypto-market-analysis-2024.pdf",
        previewUrl="/preview/crypto-market-analysis-2024"
    ),
    ResearchReport(
        id=2,
        title="DeFi生态系统发展白皮书",
        description="深入探讨去中心化金融的发展现状、挑战和未来前景，分析主要协议和创新趋势。",
        author="DeFi联盟",
        publishDate="2024-01-10",
        pages=32,
        downloads=1876,
        rating=4.6,
        category="技术研究",
        featured=False,
        fileUrl="/downloads/defi-ecosystem-whitepaper.pdf",
        previewUrl="/preview/defi-ecosystem-whitepaper"
    ),
    ResearchReport(
        id=3,
        title="区块链技术在供应链中的应用研究",
        description="分析区块链技术如何革新传统供应链管理模式，提供实际案例和实施建议。",
        author="供应链创新实验室",
        publishDate="2024-01-08",
        pages=28,
        downloads=1543,
        rating=4.5,
        category="应用研究",
        featured=True,
        fileUrl="/downloads/blockchain-supply-chain.pdf",
        previewUrl="/preview/blockchain-supply-chain"
    ),
    ResearchReport(
        id=4,
        title="加密货币监管政策全球对比分析",
        description="比较分析全球主要国家和地区的加密货币监管政策，为投资者和企业提供合规指导。",
        author="金融政策研究中心",
        publishDate="2024-01-05",
        pages=38,
        downloads=1234,
        rating=4.7,
        category="政策研究",
        featured=False,
        fileUrl="/downloads/crypto-regulation-analysis.pdf",
        previewUrl="/preview/crypto-regulation-analysis"
    )
]

@router.get("/list", response_model=NewsResponse)
async def get_news_list(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(10, ge=1, le=50, description="每页数量"),
    category: Optional[str] = Query(None, description="分类筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    trending: Optional[bool] = Query(None, description="是否只显示热门新闻"),
    force_update: bool = Query(False, description="强制更新新闻数据")
):
    """获取新闻列表"""
    try:
        # 获取最新新闻数据
        articles = await news_service.search_news(search or "", category)

        # 转换为API格式
        news_data = news_service.convert_to_api_format(articles)

        # 应用trending过滤
        if trending is not None:
            news_data = [news for news in news_data if news.get('trending') == trending]

        # 分页
        total = len(news_data)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_news = news_data[start_idx:end_idx]

        # 转换为NewsItem对象
        news_items = [NewsItem(**item) for item in paginated_news]

        return NewsResponse(
            success=True,
            data=news_items,
            total=total,
            page=page,
            limit=limit
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取新闻列表失败: {str(e)}")

@router.get("/status")
async def get_news_status():
    """获取新闻服务状态"""
    try:
        # 获取价格信息
        prices = await news_service.get_current_prices()

        return {
            "success": True,
            "data": {
                "last_update": news_service.last_update.isoformat() if news_service.last_update else None,
                "cached_articles": len(news_service.news_cache),
                "update_interval_hours": news_service.update_interval.total_seconds() / 3600,
                "current_prices": prices
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/{news_id}")
async def get_news_detail(news_id: int):
    """获取新闻详情"""
    try:
        news = next((n for n in MOCK_NEWS if n.id == news_id), None)
        if not news:
            raise HTTPException(status_code=404, detail="新闻不存在")
        
        # 增加浏览量
        news.views += 1
        
        return {
            "success": True,
            "data": news
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取新闻详情失败: {str(e)}")

@router.get("/research/list", response_model=ResearchResponse)
async def get_research_list(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(10, ge=1, le=50, description="每页数量"),
    category: Optional[str] = Query(None, description="分类筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    featured: Optional[bool] = Query(None, description="是否只显示精选报告")
):
    """获取研究报告列表"""
    try:
        # 过滤数据
        filtered_research = MOCK_RESEARCH.copy()
        
        if category and category != "全部":
            filtered_research = [r for r in filtered_research if r.category == category]
        
        if search:
            search_lower = search.lower()
            filtered_research = [
                r for r in filtered_research 
                if search_lower in r.title.lower() 
                or search_lower in r.description.lower()
            ]
        
        if featured is not None:
            filtered_research = [r for r in filtered_research if r.featured == featured]
        
        # 分页
        total = len(filtered_research)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_research = filtered_research[start_idx:end_idx]
        
        return ResearchResponse(
            success=True,
            data=paginated_research,
            total=total,
            page=page,
            limit=limit
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取研究报告列表失败: {str(e)}")

@router.get("/research/{research_id}")
async def get_research_detail(research_id: int):
    """获取研究报告详情"""
    try:
        research = next((r for r in MOCK_RESEARCH if r.id == research_id), None)
        if not research:
            raise HTTPException(status_code=404, detail="研究报告不存在")
        
        # 增加下载量
        research.downloads += 1
        
        return {
            "success": True,
            "data": research
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取研究报告详情失败: {str(e)}")

@router.get("/trending-tags")
async def get_trending_tags():
    """获取热门标签"""
    try:
        # 从所有新闻中提取标签并统计频率
        all_tags = []
        for news in MOCK_NEWS:
            all_tags.extend(news.tags)
        
        # 简单的标签排序（实际应用中可以根据使用频率排序）
        trending_tags = list(set(all_tags))[:8]
        
        return {
            "success": True,
            "data": trending_tags
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门标签失败: {str(e)}")

@router.post("/subscribe")
async def subscribe_news(request: SubscribeRequest):
    """订阅新闻推送"""
    try:
        # 这里应该将邮箱保存到数据库并设置推送任务
        # 目前只是模拟成功响应
        
        return {
            "success": True,
            "message": f"订阅成功！我们将向 {request.email} 发送最新的加密货币新闻和研究报告。"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"订阅失败: {str(e)}")

@router.post("/research/{research_id}/download")
async def download_research(research_id: int):
    """下载研究报告"""
    try:
        research = next((r for r in MOCK_RESEARCH if r.id == research_id), None)
        if not research:
            raise HTTPException(status_code=404, detail="研究报告不存在")

        # 增加下载计数
        research.downloads += 1

        # 返回下载链接（实际应用中应该生成临时下载链接）
        download_url = research.fileUrl or f"/downloads/research_{research_id}.pdf"

        return {
            "success": True,
            "downloadUrl": download_url,
            "message": "下载链接已生成"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")

@router.post("/refresh")
async def refresh_news(background_tasks: BackgroundTasks):
    """手动刷新新闻数据"""
    try:
        # 在后台任务中更新新闻
        background_tasks.add_task(news_service.get_latest_news, True)

        return {
            "success": True,
            "message": "新闻数据正在后台更新中..."
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新新闻失败: {str(e)}")

@router.get("/prices")
async def get_crypto_prices():
    """获取加密货币实时价格"""
    try:
        prices = await news_service.get_current_prices()
        return {
            "success": True,
            "data": prices,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取价格失败: {e}")
        raise HTTPException(status_code=500, detail="获取价格失败")


