"""
管理员统计API路由
提供详细的付费用户统计、收款码使用统计、付费趋势分析等功能
"""

from datetime import datetime, timedelta, date
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from models.database import get_db
from models.user import User, UserRole, UserSubscription, SubscriptionStatus
from models.activation_code import PaymentOrder, PaymentStatus, PaymentMethod, ActivationCodeType
from auth.security import require_admin_access

router = APIRouter(prefix="/admin/stats", tags=["管理员统计"])

# 响应模型
class DailyPaymentStats(BaseModel):
    date: str
    total_payments: int
    total_amount: float
    unique_users: int
    payment_methods: Dict[str, int]
    product_types: Dict[str, int]

class MonthlyPaymentStats(BaseModel):
    month: str
    total_payments: int
    total_amount: float
    unique_users: int
    daily_breakdown: List[DailyPaymentStats]

class PaymentTrendData(BaseModel):
    date: str
    amount: float
    count: int

class QRCodeUsageStats(BaseModel):
    payment_method: str
    qr_code_info: str
    usage_count: int
    total_amount: float
    last_used: Optional[str]

class PaymentOverviewStats(BaseModel):
    today: DailyPaymentStats
    this_month: MonthlyPaymentStats
    payment_trends: List[PaymentTrendData]
    qr_code_usage: List[QRCodeUsageStats]
    top_products: Dict[str, int]

@router.get("/daily-payments", response_model=DailyPaymentStats)
async def get_daily_payment_stats(
    target_date: Optional[str] = Query(None, description="目标日期 (YYYY-MM-DD)，默认为今天"),
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """获取指定日期的付费统计"""
    if target_date:
        try:
            query_date = datetime.strptime(target_date, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="日期格式错误，请使用 YYYY-MM-DD 格式"
            )
    else:
        query_date = date.today()
    
    # 查询当日付费订单
    start_datetime = datetime.combine(query_date, datetime.min.time())
    end_datetime = datetime.combine(query_date, datetime.max.time())
    
    paid_orders = db.query(PaymentOrder).filter(
        PaymentOrder.payment_status == PaymentStatus.PAID,
        PaymentOrder.paid_at >= start_datetime,
        PaymentOrder.paid_at <= end_datetime
    ).all()
    
    # 统计数据
    total_payments = len(paid_orders)
    total_amount = sum(float(order.amount) for order in paid_orders)
    unique_users = len(set(order.user_id for order in paid_orders))
    
    # 按支付方式统计
    payment_methods = {}
    for order in paid_orders:
        method = order.payment_method.value
        payment_methods[method] = payment_methods.get(method, 0) + 1
    
    # 按产品类型统计
    product_types = {}
    for order in paid_orders:
        product = order.product_type.value
        product_types[product] = product_types.get(product, 0) + 1
    
    return DailyPaymentStats(
        date=query_date.isoformat(),
        total_payments=total_payments,
        total_amount=total_amount,
        unique_users=unique_users,
        payment_methods=payment_methods,
        product_types=product_types
    )

@router.get("/monthly-payments", response_model=MonthlyPaymentStats)
async def get_monthly_payment_stats(
    year: Optional[int] = Query(None, description="年份，默认为当前年份"),
    month: Optional[int] = Query(None, description="月份 (1-12)，默认为当前月份"),
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """获取指定月份的付费统计"""
    if year is None:
        year = date.today().year
    if month is None:
        month = date.today().month
    
    if not (1 <= month <= 12):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="月份必须在1-12之间"
        )
    
    # 计算月份的开始和结束日期
    start_date = date(year, month, 1)
    if month == 12:
        end_date = date(year + 1, 1, 1) - timedelta(days=1)
    else:
        end_date = date(year, month + 1, 1) - timedelta(days=1)
    
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    # 查询当月付费订单
    paid_orders = db.query(PaymentOrder).filter(
        PaymentOrder.payment_status == PaymentStatus.PAID,
        PaymentOrder.paid_at >= start_datetime,
        PaymentOrder.paid_at <= end_datetime
    ).all()
    
    # 月度统计
    total_payments = len(paid_orders)
    total_amount = sum(float(order.amount) for order in paid_orders)
    unique_users = len(set(order.user_id for order in paid_orders))
    
    # 按日统计
    daily_stats = {}
    current_date = start_date
    while current_date <= end_date:
        daily_stats[current_date.isoformat()] = {
            'date': current_date.isoformat(),
            'total_payments': 0,
            'total_amount': 0.0,
            'unique_users': set(),
            'payment_methods': {},
            'product_types': {}
        }
        current_date += timedelta(days=1)
    
    # 填充每日数据
    for order in paid_orders:
        order_date = order.paid_at.date().isoformat()
        if order_date in daily_stats:
            daily_stats[order_date]['total_payments'] += 1
            daily_stats[order_date]['total_amount'] += float(order.amount)
            daily_stats[order_date]['unique_users'].add(order.user_id)
            
            method = order.payment_method.value
            daily_stats[order_date]['payment_methods'][method] = \
                daily_stats[order_date]['payment_methods'].get(method, 0) + 1
            
            product = order.product_type.value
            daily_stats[order_date]['product_types'][product] = \
                daily_stats[order_date]['product_types'].get(product, 0) + 1
    
    # 转换为响应格式
    daily_breakdown = []
    for day_data in daily_stats.values():
        daily_breakdown.append(DailyPaymentStats(
            date=day_data['date'],
            total_payments=day_data['total_payments'],
            total_amount=day_data['total_amount'],
            unique_users=len(day_data['unique_users']),
            payment_methods=day_data['payment_methods'],
            product_types=day_data['product_types']
        ))
    
    return MonthlyPaymentStats(
        month=f"{year}-{month:02d}",
        total_payments=total_payments,
        total_amount=total_amount,
        unique_users=unique_users,
        daily_breakdown=daily_breakdown
    )

@router.get("/payment-trends", response_model=List[PaymentTrendData])
async def get_payment_trends(
    days: int = Query(30, description="统计天数，默认30天"),
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """获取付费趋势数据"""
    if days <= 0 or days > 365:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="天数必须在1-365之间"
        )

    end_date = date.today()
    start_date = end_date - timedelta(days=days-1)

    # 查询时间范围内的付费订单
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())

    paid_orders = db.query(PaymentOrder).filter(
        PaymentOrder.payment_status == PaymentStatus.PAID,
        PaymentOrder.paid_at >= start_datetime,
        PaymentOrder.paid_at <= end_datetime
    ).all()

    # 按日期分组统计
    daily_trends = {}
    current_date = start_date
    while current_date <= end_date:
        daily_trends[current_date.isoformat()] = {
            'date': current_date.isoformat(),
            'amount': 0.0,
            'count': 0
        }
        current_date += timedelta(days=1)

    # 填充数据
    for order in paid_orders:
        order_date = order.paid_at.date().isoformat()
        if order_date in daily_trends:
            daily_trends[order_date]['amount'] += float(order.amount)
            daily_trends[order_date]['count'] += 1

    # 转换为响应格式
    trends = [
        PaymentTrendData(
            date=data['date'],
            amount=data['amount'],
            count=data['count']
        )
        for data in daily_trends.values()
    ]

    return sorted(trends, key=lambda x: x.date)

@router.get("/qr-code-usage", response_model=List[QRCodeUsageStats])
async def get_qr_code_usage_stats(
    days: int = Query(30, description="统计天数，默认30天"),
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """获取收款码使用统计"""
    if days <= 0 or days > 365:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="天数必须在1-365之间"
        )

    end_date = date.today()
    start_date = end_date - timedelta(days=days-1)
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())

    # 查询付费订单，包含用户备注信息
    paid_orders = db.query(PaymentOrder).filter(
        PaymentOrder.payment_status == PaymentStatus.PAID,
        PaymentOrder.paid_at >= start_datetime,
        PaymentOrder.paid_at <= end_datetime
    ).all()

    # 按支付方式和收款码信息统计
    qr_usage = {}

    for order in paid_orders:
        payment_method = order.payment_method.value

        # 尝试从用户备注或管理员备注中提取收款码信息
        qr_info = "未知收款码"
        if order.user_note:
            qr_info = f"用户备注: {order.user_note[:50]}..."
        elif order.admin_note:
            qr_info = f"管理员备注: {order.admin_note[:50]}..."

        key = f"{payment_method}_{qr_info}"

        if key not in qr_usage:
            qr_usage[key] = {
                'payment_method': payment_method,
                'qr_code_info': qr_info,
                'usage_count': 0,
                'total_amount': 0.0,
                'last_used': None
            }

        qr_usage[key]['usage_count'] += 1
        qr_usage[key]['total_amount'] += float(order.amount)

        # 更新最后使用时间
        if (qr_usage[key]['last_used'] is None or
            order.paid_at > datetime.fromisoformat(qr_usage[key]['last_used'])):
            qr_usage[key]['last_used'] = order.paid_at.isoformat()

    # 转换为响应格式
    usage_stats = [
        QRCodeUsageStats(
            payment_method=data['payment_method'],
            qr_code_info=data['qr_code_info'],
            usage_count=data['usage_count'],
            total_amount=data['total_amount'],
            last_used=data['last_used']
        )
        for data in qr_usage.values()
    ]

    # 按使用次数排序
    return sorted(usage_stats, key=lambda x: x.usage_count, reverse=True)

@router.get("/overview", response_model=PaymentOverviewStats)
async def get_payment_overview(
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """获取付费概览统计"""
    # 获取今日统计
    today_stats = await get_daily_payment_stats(None, admin_user, db)

    # 获取本月统计
    current_date = date.today()
    monthly_stats = await get_monthly_payment_stats(
        current_date.year, current_date.month, admin_user, db
    )

    # 获取30天趋势
    trends = await get_payment_trends(30, admin_user, db)

    # 获取收款码使用统计
    qr_usage = await get_qr_code_usage_stats(30, admin_user, db)

    # 获取热门产品统计（最近30天）
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())

    paid_orders = db.query(PaymentOrder).filter(
        PaymentOrder.payment_status == PaymentStatus.PAID,
        PaymentOrder.paid_at >= start_datetime,
        PaymentOrder.paid_at <= end_datetime
    ).all()

    top_products = {}
    for order in paid_orders:
        product = order.product_type.value
        top_products[product] = top_products.get(product, 0) + 1

    return PaymentOverviewStats(
        today=today_stats,
        this_month=monthly_stats,
        payment_trends=trends,
        qr_code_usage=qr_usage,
        top_products=top_products
    )
