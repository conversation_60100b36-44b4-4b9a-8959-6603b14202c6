"""
用户认证API路由
"""

from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import List

from models.database import get_db
from models.user import User, UserRole, UserSubscription, SubscriptionStatus
from auth.security import (
    authenticate_user,
    create_access_token,
    get_password_hash,
    get_current_active_user,
    get_user_by_username,
    get_user_by_email,
    require_admin_access
)
from auth.schemas import (
    UserCreate,
    UserLogin,
    UserResponse,
    UserUpdate,
    Token,
    SubscriptionCreate,
    SubscriptionResponse,
    UserWithSubscription
)
from config.settings import settings

router = APIRouter()

@router.post("/register", response_model=UserResponse)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    """用户注册"""
    # 检查用户名是否已存在
    if get_user_by_username(db, user_data.username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    if get_user_by_email(db, user_data.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )
    
    # 创建新用户
    hashed_password = get_password_hash(user_data.password)
    db_user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        hashed_password=hashed_password,
        role=UserRole.FREE
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

@router.post("/login", response_model=Token)
async def login(user_data: UserLogin, db: Session = Depends(get_db)):
    """用户登录"""
    user = authenticate_user(db, user_data.username, user_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    # 更新最后登录时间
    user.last_login = datetime.utcnow()
    db.commit()
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": user
    }

@router.get("/me", response_model=UserWithSubscription)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """获取当前用户信息"""
    return current_user

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新当前用户信息"""
    # 检查邮箱是否被其他用户使用
    if user_update.email and user_update.email != current_user.email:
        existing_user = get_user_by_email(db, user_update.email)
        if existing_user and existing_user.id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被其他用户使用"
            )
    
    # 更新用户信息
    update_data = user_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(current_user, field, value)
    
    current_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(current_user)
    
    return current_user

@router.post("/subscribe", response_model=SubscriptionResponse)
async def create_subscription(
    subscription_data: SubscriptionCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建订阅（模拟支付成功）"""
    # 计算订阅时长
    duration_map = {
        UserRole.MONTHLY: 1,
        UserRole.QUARTERLY: 3,
        UserRole.YEARLY: 12
    }
    
    months = duration_map.get(subscription_data.subscription_type, subscription_data.duration_months)
    
    # 计算价格
    price_map = {
        UserRole.MONTHLY: "29.99",
        UserRole.QUARTERLY: "79.99", 
        UserRole.YEARLY: "299.99"
    }
    
    amount = price_map.get(subscription_data.subscription_type, "29.99")
    
    # 创建订阅记录
    starts_at = datetime.utcnow()
    expires_at = starts_at + timedelta(days=months * 30)
    
    subscription = UserSubscription(
        user_id=current_user.id,
        subscription_type=subscription_data.subscription_type,
        status=SubscriptionStatus.ACTIVE,
        starts_at=starts_at,
        expires_at=expires_at,
        payment_method=subscription_data.payment_method or "demo",
        amount=amount,
        currency="CNY"
    )
    
    # 更新用户角色
    current_user.role = subscription_data.subscription_type
    
    db.add(subscription)
    db.commit()
    db.refresh(subscription)
    
    return subscription

@router.get("/subscription", response_model=SubscriptionResponse)
async def get_current_subscription(current_user: User = Depends(get_current_active_user)):
    """获取当前订阅信息"""
    subscription = current_user.current_subscription
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到有效订阅"
        )
    return subscription

# 管理员API
@router.get("/admin/users", response_model=List[UserWithSubscription])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """获取用户列表（管理员）"""
    users = db.query(User).offset(skip).limit(limit).all()
    return users

@router.put("/admin/users/{user_id}/role")
async def update_user_role(
    user_id: int,
    new_role: UserRole,
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """更新用户角色（管理员）"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    user.role = new_role
    user.updated_at = datetime.utcnow()
    db.commit()
    
    return {"message": f"用户 {user.username} 的角色已更新为 {new_role.value}"}

@router.put("/admin/users/{user_id}/status")
async def update_user_status(
    user_id: int,
    is_active: bool,
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """更新用户状态（管理员）"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    user.is_active = is_active
    user.updated_at = datetime.utcnow()
    db.commit()
    
    status_text = "激活" if is_active else "禁用"
    return {"message": f"用户 {user.username} 已{status_text}"}

@router.get("/admin/stats")
async def get_user_stats(
    admin_user: User = Depends(require_admin_access),
    db: Session = Depends(get_db)
):
    """获取用户统计信息（管理员）"""
    total_users = db.query(User).count()
    active_users = db.query(User).filter(User.is_active == True).count()
    
    # 按角色统计
    role_stats = {}
    for role in UserRole:
        count = db.query(User).filter(User.role == role).count()
        role_stats[role.value] = count
    
    # 活跃订阅统计
    active_subscriptions = db.query(UserSubscription).filter(
        UserSubscription.status == SubscriptionStatus.ACTIVE,
        UserSubscription.expires_at > datetime.utcnow()
    ).count()
    
    return {
        "total_users": total_users,
        "active_users": active_users,
        "role_distribution": role_stats,
        "active_subscriptions": active_subscriptions
    }

@router.get("/debug/token")
async def debug_token_info(request: Request):
    """调试token信息"""
    auth_header = request.headers.get("authorization")

    debug_info = {
        "has_auth_header": bool(auth_header),
        "auth_header": auth_header,
        "headers": dict(request.headers),
        "timestamp": datetime.utcnow().isoformat()
    }

    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header.split(" ")[1]
        debug_info["token_length"] = len(token)
        debug_info["token_preview"] = f"{token[:10]}...{token[-10:]}" if len(token) > 20 else token

        # 尝试验证token
        try:
            from auth.security import verify_token
            token_data = verify_token(token)
            debug_info["token_valid"] = token_data is not None
            debug_info["username"] = token_data.username if token_data else None
        except Exception as e:
            debug_info["token_error"] = str(e)

    return debug_info
