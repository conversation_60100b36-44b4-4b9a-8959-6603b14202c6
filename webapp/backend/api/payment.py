"""
支付API路由
"""

from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel

from models.database import get_db
from models.user import User
from models.activation_code import (
    PaymentOrder, ActivationCode, PaymentStatus, PaymentMethod, 
    ActivationCodeType, ActivationCodeStatus
)
from auth.security import get_current_active_user
from services.payment_service import payment_service
from services.real_payment_service import real_payment_service
from config.payment_config import is_payment_configured

router = APIRouter()

# Pydantic模型
class PaymentCreateRequest(BaseModel):
    product_type: ActivationCodeType
    payment_method: PaymentMethod

class PaymentCreateResponse(BaseModel):
    success: bool
    order_no: str
    qr_code_url: Optional[str] = None
    payment_url: Optional[str] = None
    amount: str
    expires_at: datetime
    demo_mode: Optional[bool] = False
    message: Optional[str] = None

class PaymentOrderResponse(BaseModel):
    id: int
    order_no: str
    product_type: ActivationCodeType
    amount: str
    currency: str
    payment_method: PaymentMethod
    payment_status: PaymentStatus
    qr_code_url: Optional[str] = None
    created_at: datetime
    paid_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None

class ActivationCodeUseRequest(BaseModel):
    code: str

class ActivationCodeResponse(BaseModel):
    id: int
    code: str
    code_type: ActivationCodeType
    status: ActivationCodeStatus
    created_at: datetime
    used_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None

class DemoPaymentRequest(BaseModel):
    order_no: str

@router.post("/create", response_model=PaymentCreateResponse)
async def create_payment(
    request: PaymentCreateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建支付订单"""
    try:
        # 选择支付服务（真实支付或演示支付）
        service = real_payment_service if is_payment_configured() else payment_service

        # 创建支付订单
        order = service.create_payment_order(
            db=db,
            user_id=current_user.id,
            product_type=request.product_type,
            payment_method=request.payment_method
        )

        # 根据支付方式创建支付
        if request.payment_method == PaymentMethod.WECHAT:
            payment_result = service.create_wechat_payment(order)
        elif request.payment_method == PaymentMethod.ALIPAY:
            payment_result = service.create_alipay_payment(order)
        else:
            payment_result = service._create_demo_payment(order, request.payment_method)
        
        if not payment_result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=payment_result['error']
            )
        
        # 更新订单信息
        order.qr_code_url = payment_result.get('qr_code_url')
        order.payment_url = payment_result.get('payment_url')
        db.commit()
        
        return PaymentCreateResponse(
            success=True,
            order_no=order.order_no,
            qr_code_url=payment_result.get('qr_code_url'),
            payment_url=payment_result.get('payment_url'),
            amount=str(order.amount),
            expires_at=order.expires_at,
            demo_mode=payment_result.get('demo_mode', False),
            message=payment_result.get('message')
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建支付订单失败: {str(e)}"
        )

@router.get("/orders", response_model=List[PaymentOrderResponse])
async def get_payment_orders(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户的支付订单列表"""
    orders = db.query(PaymentOrder).filter(
        PaymentOrder.user_id == current_user.id
    ).order_by(PaymentOrder.created_at.desc()).all()
    
    return [
        PaymentOrderResponse(
            id=order.id,
            order_no=order.order_no,
            product_type=order.product_type,
            amount=str(order.amount),
            currency=order.currency,
            payment_method=order.payment_method,
            payment_status=order.payment_status,
            qr_code_url=order.qr_code_url,
            created_at=order.created_at,
            paid_at=order.paid_at,
            expires_at=order.expires_at
        )
        for order in orders
    ]

@router.get("/orders/{order_no}", response_model=PaymentOrderResponse)
async def get_payment_order(
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取指定支付订单详情"""
    order = db.query(PaymentOrder).filter(
        PaymentOrder.order_no == order_no,
        PaymentOrder.user_id == current_user.id
    ).first()
    
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    return PaymentOrderResponse(
        id=order.id,
        order_no=order.order_no,
        product_type=order.product_type,
        amount=str(order.amount),
        currency=order.currency,
        payment_method=order.payment_method,
        payment_status=order.payment_status,
        qr_code_url=order.qr_code_url,
        created_at=order.created_at,
        paid_at=order.paid_at,
        expires_at=order.expires_at
    )

@router.post("/demo/pay")
async def demo_payment(
    request: DemoPaymentRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """演示支付完成"""
    # 查找订单
    order = db.query(PaymentOrder).filter(
        PaymentOrder.order_no == request.order_no,
        PaymentOrder.user_id == current_user.id
    ).first()
    
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    if order.payment_status != PaymentStatus.PENDING:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="订单状态不正确"
        )
    
    # 处理支付成功
    service = real_payment_service if is_payment_configured() else payment_service
    success = service.process_payment_success(
        db=db,
        order_no=request.order_no,
        third_party_order_no=f"demo_{request.order_no}"
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="支付处理失败"
        )
    
    # 获取生成的激活码
    activation_code = db.query(ActivationCode).filter(
        ActivationCode.payment_order_id == order.id
    ).first()
    
    return {
        "success": True,
        "message": "支付成功",
        "activation_code": activation_code.code if activation_code else None
    }

@router.post("/activation-code/use")
async def use_activation_code(
    request: ActivationCodeUseRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """使用激活码"""
    service = real_payment_service if is_payment_configured() else payment_service
    success, message = service.use_activation_code(
        db=db,
        user_id=current_user.id,
        code=request.code
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message
        )
    
    return {
        "success": True,
        "message": message
    }

@router.get("/activation-codes", response_model=List[ActivationCodeResponse])
async def get_activation_codes(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户的激活码列表"""
    codes = db.query(ActivationCode).filter(
        ActivationCode.created_by_user_id == current_user.id
    ).order_by(ActivationCode.created_at.desc()).all()
    
    return [
        ActivationCodeResponse(
            id=code.id,
            code=code.code,
            code_type=code.code_type,
            status=code.status,
            created_at=code.created_at,
            used_at=code.used_at,
            expires_at=code.expires_at
        )
        for code in codes
    ]

# 支付回调接口
@router.post("/wechat/notify")
async def wechat_payment_notify(request: Request, db: Session = Depends(get_db)):
    """微信支付回调"""
    try:
        # 获取回调数据
        body = await request.body()
        headers = dict(request.headers)

        # 验证签名
        service = real_payment_service if is_payment_configured() else payment_service
        if hasattr(service, 'verify_wechat_callback'):
            if not service.verify_wechat_callback(headers, body.decode('utf-8')):
                return {"code": "FAIL", "message": "签名验证失败"}

        # 解析回调数据
        import json
        try:
            callback_data = json.loads(body.decode('utf-8'))
            out_trade_no = callback_data.get('out_trade_no')
            transaction_id = callback_data.get('transaction_id')

            if out_trade_no and callback_data.get('trade_state') == 'SUCCESS':
                service.process_payment_success(
                    db=db,
                    order_no=out_trade_no,
                    third_party_order_no=transaction_id
                )
        except json.JSONDecodeError:
            # 如果是XML格式，需要解析XML
            pass

        return {"code": "SUCCESS", "message": "OK"}

    except Exception as e:
        return {"code": "FAIL", "message": str(e)}

@router.post("/alipay/notify")
async def alipay_payment_notify(request: Request, db: Session = Depends(get_db)):
    """支付宝支付回调"""
    try:
        # 获取回调数据
        form_data = await request.form()
        params = dict(form_data)

        # 验证签名
        service = real_payment_service if is_payment_configured() else payment_service
        if hasattr(service, 'verify_alipay_callback'):
            if not service.verify_alipay_callback(params.copy()):
                return "fail"

        # 处理支付成功
        out_trade_no = params.get('out_trade_no')
        trade_no = params.get('trade_no')
        trade_status = params.get('trade_status')

        if out_trade_no and trade_no and trade_status == 'TRADE_SUCCESS':
            service.process_payment_success(
                db=db,
                order_no=out_trade_no,
                third_party_order_no=trade_no
            )

        return "success"

    except Exception as e:
        return "fail"

@router.get("/config/status")
async def get_payment_config_status():
    """获取支付配置状态"""
    from config.payment_config import payment_config, PAYMENT_CONFIG_EXAMPLE

    return {
        "is_configured": is_payment_configured(),
        "payment_mode": payment_config.PAYMENT_MODE,
        "wechat_configured": bool(payment_config.WECHAT_APP_ID and payment_config.WECHAT_MCH_ID),
        "alipay_configured": bool(payment_config.ALIPAY_APP_ID and payment_config.ALIPAY_PRIVATE_KEY),
        "config_example": PAYMENT_CONFIG_EXAMPLE if not is_payment_configured() else None
    }
