"""
技术分析API路由
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional, Dict, Any
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from core.market_service import MarketService
from config.simple_settings import settings
from auth.security import require_premium_access
from models.user import User

logger = logging.getLogger(__name__)
router = APIRouter()

# 全局市场服务实例
market_service = MarketService()

@router.get("/technical/{symbol}")
async def get_technical_analysis(
    symbol: str,
    interval: str = Query("1h", description="时间间隔"),
    indicators: str = Query("all", description="技术指标列表，逗号分隔或'all'"),
    current_user: User = Depends(require_premium_access)
):
    """
    获取技术分析指标
    
    Args:
        symbol: 交易对符号
        interval: K线时间间隔
        indicators: 要计算的指标列表
    
    Returns:
        技术分析结果
    """
    try:
        # 验证交易对
        if symbol.upper() not in settings.SUPPORTED_SYMBOLS:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的交易对: {symbol}"
            )
        
        # 获取市场数据
        symbol_data = await market_service.get_symbol_data(symbol.upper(), interval, 200)
        klines = symbol_data["klines"]
        
        if len(klines) < 50:
            raise HTTPException(
                status_code=400,
                detail="数据不足，无法计算技术指标"
            )
        
        # 转换为DataFrame
        df = pd.DataFrame(klines)
        
        # 解析要计算的指标
        if indicators == "all":
            requested_indicators = ["sma", "ema", "rsi", "macd", "bollinger", "stoch", "atr"]
        else:
            requested_indicators = [ind.strip().lower() for ind in indicators.split(",")]
        
        # 计算技术指标
        technical_data = {}
        
        if "sma" in requested_indicators:
            technical_data["sma"] = calculate_sma(df)
        
        if "ema" in requested_indicators:
            technical_data["ema"] = calculate_ema(df)
        
        if "rsi" in requested_indicators:
            technical_data["rsi"] = calculate_rsi(df)
        
        if "macd" in requested_indicators:
            technical_data["macd"] = calculate_macd(df)
        
        if "bollinger" in requested_indicators:
            technical_data["bollinger"] = calculate_bollinger_bands(df)
        
        if "stoch" in requested_indicators:
            technical_data["stochastic"] = calculate_stochastic(df)
        
        if "atr" in requested_indicators:
            technical_data["atr"] = calculate_atr(df)
        
        # 生成交易信号
        signals = generate_trading_signals(df, technical_data)
        
        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "interval": interval,
                "timestamp": datetime.now().isoformat(),
                "current_price": symbol_data["current_price"],
                "technical_indicators": technical_data,
                "trading_signals": signals,
                "market_summary": {
                    "trend": determine_trend(df),
                    "volatility": calculate_volatility(df),
                    "support_resistance": find_support_resistance(df)
                }
            },
            "message": f"{symbol} 技术分析完成"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"技术分析失败 {symbol}: {e}")
        raise HTTPException(status_code=500, detail=f"技术分析失败: {str(e)}")

def calculate_sma(df: pd.DataFrame) -> Dict[str, float]:
    """计算简单移动平均线"""
    closes = df['close']
    return {
        "sma_10": float(closes.rolling(10).mean().iloc[-1]),
        "sma_20": float(closes.rolling(20).mean().iloc[-1]),
        "sma_50": float(closes.rolling(50).mean().iloc[-1]),
        "sma_100": float(closes.rolling(100).mean().iloc[-1]) if len(closes) >= 100 else None
    }

def calculate_ema(df: pd.DataFrame) -> Dict[str, float]:
    """计算指数移动平均线"""
    closes = df['close']
    return {
        "ema_12": float(closes.ewm(span=12).mean().iloc[-1]),
        "ema_26": float(closes.ewm(span=26).mean().iloc[-1]),
        "ema_50": float(closes.ewm(span=50).mean().iloc[-1])
    }

def calculate_rsi(df: pd.DataFrame, period: int = 14) -> Dict[str, float]:
    """计算RSI指标"""
    closes = df['close']
    delta = closes.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    
    current_rsi = float(rsi.iloc[-1])
    
    # RSI信号判断
    if current_rsi > 70:
        signal = "overbought"
    elif current_rsi < 30:
        signal = "oversold"
    else:
        signal = "neutral"
    
    return {
        "rsi": current_rsi,
        "signal": signal,
        "overbought_level": 70,
        "oversold_level": 30
    }

def calculate_macd(df: pd.DataFrame) -> Dict[str, Any]:
    """计算MACD指标"""
    closes = df['close']
    ema_12 = closes.ewm(span=12).mean()
    ema_26 = closes.ewm(span=26).mean()
    macd_line = ema_12 - ema_26
    signal_line = macd_line.ewm(span=9).mean()
    histogram = macd_line - signal_line
    
    return {
        "macd": float(macd_line.iloc[-1]),
        "signal": float(signal_line.iloc[-1]),
        "histogram": float(histogram.iloc[-1]),
        "crossover": "bullish" if macd_line.iloc[-1] > signal_line.iloc[-1] else "bearish"
    }

def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std_dev: int = 2) -> Dict[str, float]:
    """计算布林带"""
    closes = df['close']
    sma = closes.rolling(period).mean()
    std = closes.rolling(period).std()
    
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    
    current_price = closes.iloc[-1]
    current_upper = upper_band.iloc[-1]
    current_lower = lower_band.iloc[-1]
    current_middle = sma.iloc[-1]
    
    # 判断价格位置
    if current_price > current_upper:
        position = "above_upper"
    elif current_price < current_lower:
        position = "below_lower"
    else:
        position = "within_bands"
    
    return {
        "upper_band": float(current_upper),
        "middle_band": float(current_middle),
        "lower_band": float(current_lower),
        "current_price": float(current_price),
        "position": position,
        "bandwidth": float((current_upper - current_lower) / current_middle * 100)
    }

def calculate_stochastic(df: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> Dict[str, Any]:
    """计算随机指标"""
    highs = df['high'].rolling(k_period).max()
    lows = df['low'].rolling(k_period).min()
    closes = df['close']
    
    k_percent = 100 * ((closes - lows) / (highs - lows))
    d_percent = k_percent.rolling(d_period).mean()
    
    current_k = float(k_percent.iloc[-1])
    current_d = float(d_percent.iloc[-1])
    
    # 信号判断
    if current_k > 80 and current_d > 80:
        signal = "overbought"
    elif current_k < 20 and current_d < 20:
        signal = "oversold"
    else:
        signal = "neutral"
    
    return {
        "k_percent": current_k,
        "d_percent": current_d,
        "signal": signal
    }

def calculate_atr(df: pd.DataFrame, period: int = 14) -> Dict[str, float]:
    """计算平均真实波幅"""
    high = df['high']
    low = df['low']
    close = df['close']
    
    tr1 = high - low
    tr2 = abs(high - close.shift())
    tr3 = abs(low - close.shift())
    
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = true_range.rolling(period).mean()
    
    return {
        "atr": float(atr.iloc[-1]),
        "atr_percentage": float(atr.iloc[-1] / close.iloc[-1] * 100)
    }

def generate_trading_signals(df: pd.DataFrame, technical_data: Dict) -> Dict[str, Any]:
    """生成交易信号"""
    signals = {
        "overall_signal": "neutral",
        "strength": 0,
        "individual_signals": {}
    }
    
    signal_count = 0
    bullish_signals = 0
    
    # RSI信号
    if "rsi" in technical_data:
        rsi_data = technical_data["rsi"]
        signals["individual_signals"]["rsi"] = rsi_data["signal"]
        signal_count += 1
        if rsi_data["signal"] == "oversold":
            bullish_signals += 1
    
    # MACD信号
    if "macd" in technical_data:
        macd_data = technical_data["macd"]
        signals["individual_signals"]["macd"] = macd_data["crossover"]
        signal_count += 1
        if macd_data["crossover"] == "bullish":
            bullish_signals += 1
    
    # 布林带信号
    if "bollinger" in technical_data:
        bb_data = technical_data["bollinger"]
        if bb_data["position"] == "below_lower":
            bb_signal = "bullish"
            bullish_signals += 1
        elif bb_data["position"] == "above_upper":
            bb_signal = "bearish"
        else:
            bb_signal = "neutral"
        signals["individual_signals"]["bollinger"] = bb_signal
        signal_count += 1
    
    # 计算整体信号
    if signal_count > 0:
        bullish_ratio = bullish_signals / signal_count
        if bullish_ratio > 0.6:
            signals["overall_signal"] = "bullish"
            signals["strength"] = bullish_ratio
        elif bullish_ratio < 0.4:
            signals["overall_signal"] = "bearish"
            signals["strength"] = 1 - bullish_ratio
        else:
            signals["overall_signal"] = "neutral"
            signals["strength"] = 0.5
    
    return signals

def determine_trend(df: pd.DataFrame) -> str:
    """判断趋势方向"""
    closes = df['close']
    if len(closes) < 20:
        return "unknown"
    
    sma_20 = closes.rolling(20).mean()
    sma_50 = closes.rolling(50).mean() if len(closes) >= 50 else sma_20
    
    current_price = closes.iloc[-1]
    current_sma_20 = sma_20.iloc[-1]
    current_sma_50 = sma_50.iloc[-1]
    
    if current_price > current_sma_20 > current_sma_50:
        return "uptrend"
    elif current_price < current_sma_20 < current_sma_50:
        return "downtrend"
    else:
        return "sideways"

def calculate_volatility(df: pd.DataFrame, period: int = 20) -> float:
    """计算波动率"""
    closes = df['close']
    returns = closes.pct_change()
    volatility = returns.rolling(period).std() * np.sqrt(24)  # 假设24小时数据
    return float(volatility.iloc[-1] * 100)  # 转换为百分比

def find_support_resistance(df: pd.DataFrame) -> Dict[str, List[float]]:
    """寻找支撑阻力位"""
    highs = df['high']
    lows = df['low']
    
    # 简单的支撑阻力位计算
    recent_highs = highs.tail(50).nlargest(3).tolist()
    recent_lows = lows.tail(50).nsmallest(3).tolist()
    
    return {
        "resistance_levels": [float(h) for h in recent_highs],
        "support_levels": [float(l) for l in recent_lows]
    }

@router.get("/correlation")
async def get_correlation_analysis(
    symbols: str = Query(..., description="交易对列表，逗号分隔"),
    period: int = Query(30, ge=7, le=90, description="分析周期（天）"),
    current_user: User = Depends(require_premium_access)
):
    """
    获取交易对相关性分析
    
    Args:
        symbols: 要分析的交易对列表
        period: 分析周期
    
    Returns:
        相关性矩阵和分析结果
    """
    try:
        symbol_list = [s.strip().upper() for s in symbols.split(",")]
        
        # 验证交易对
        invalid_symbols = [s for s in symbol_list if s not in settings.SUPPORTED_SYMBOLS]
        if invalid_symbols:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的交易对: {', '.join(invalid_symbols)}"
            )
        
        if len(symbol_list) < 2:
            raise HTTPException(
                status_code=400,
                detail="至少需要2个交易对进行相关性分析"
            )
        
        # 获取历史数据并计算相关性
        correlation_matrix = {}
        price_data = {}
        
        # 获取每个交易对的价格数据
        for symbol in symbol_list:
            symbol_data = await market_service.get_symbol_data(symbol, "1h", period * 24)
            prices = [kline["close"] for kline in symbol_data["klines"]]
            price_data[symbol] = prices
        
        # 计算相关性矩阵
        for i, symbol1 in enumerate(symbol_list):
            correlation_matrix[symbol1] = {}
            for j, symbol2 in enumerate(symbol_list):
                if i == j:
                    correlation_matrix[symbol1][symbol2] = 1.0
                else:
                    # 计算皮尔逊相关系数
                    corr = np.corrcoef(price_data[symbol1], price_data[symbol2])[0, 1]
                    correlation_matrix[symbol1][symbol2] = float(corr)
        
        return {
            "success": True,
            "data": {
                "symbols": symbol_list,
                "period_days": period,
                "correlation_matrix": correlation_matrix,
                "analysis": {
                    "strongest_positive": find_strongest_correlation(correlation_matrix, positive=True),
                    "strongest_negative": find_strongest_correlation(correlation_matrix, positive=False),
                    "average_correlation": calculate_average_correlation(correlation_matrix)
                },
                "timestamp": datetime.now().isoformat()
            },
            "message": "相关性分析完成"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"相关性分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"相关性分析失败: {str(e)}")

def find_strongest_correlation(correlation_matrix: Dict, positive: bool = True) -> Dict:
    """找到最强的相关性"""
    strongest_corr = -2 if positive else 2
    strongest_pair = None
    
    for symbol1, correlations in correlation_matrix.items():
        for symbol2, corr in correlations.items():
            if symbol1 != symbol2:
                if positive and corr > strongest_corr:
                    strongest_corr = corr
                    strongest_pair = (symbol1, symbol2)
                elif not positive and corr < strongest_corr:
                    strongest_corr = corr
                    strongest_pair = (symbol1, symbol2)
    
    return {
        "pair": strongest_pair,
        "correlation": strongest_corr
    } if strongest_pair else None

def calculate_average_correlation(correlation_matrix: Dict) -> float:
    """计算平均相关性"""
    total_corr = 0
    count = 0
    
    for symbol1, correlations in correlation_matrix.items():
        for symbol2, corr in correlations.items():
            if symbol1 != symbol2:
                total_corr += abs(corr)
                count += 1
    
    return total_corr / count if count > 0 else 0
