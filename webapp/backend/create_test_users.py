#!/usr/bin/env python3
"""
创建测试用户脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import get_db, init_db
from models.user import User, UserRole
from auth.security import get_password_hash
from sqlalchemy.orm import Session

def create_test_users():
    """创建测试用户"""
    
    # 初始化数据库
    init_db()
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 测试用户数据
        users_data = [
            {
                'username': 'demo_free',
                'email': '<EMAIL>',
                'password': 'demo123456',
                'full_name': '免费用户',
                'role': UserRole.FREE
            },
            {
                'username': 'demo_premium',
                'email': '<EMAIL>',
                'password': 'demo123456',
                'full_name': '付费用户',
                'role': UserRole.MONTHLY
            },
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123456',
                'full_name': '管理员',
                'role': UserRole.ADMIN
            }
        ]
        
        for user_data in users_data:
            # 检查用户是否已存在
            existing_user = db.query(User).filter(User.username == user_data['username']).first()
            if not existing_user:
                user = User(
                    username=user_data['username'],
                    email=user_data['email'],
                    hashed_password=get_password_hash(user_data['password']),
                    full_name=user_data['full_name'],
                    role=user_data['role'],
                    is_active=True,
                    is_verified=True
                )
                db.add(user)
                print(f'✅ 创建用户: {user_data["username"]} ({user_data["role"].value})')
            else:
                print(f'⚠️ 用户已存在: {user_data["username"]}')
        
        db.commit()
        print('🎉 用户创建完成!')
        
        # 验证用户创建
        users = db.query(User).all()
        print(f'\n📊 数据库中共有 {len(users)} 个用户:')
        for user in users:
            print(f'  - {user.username} ({user.email}) - {user.role.value}')
            
    except Exception as e:
        print(f'❌ 创建用户时出错: {e}')
        db.rollback()
    finally:
        db.close()

if __name__ == '__main__':
    create_test_users()
