import { lazy } from 'react'
import LazyWrapper, { PageFallback } from '@components/common/LazyWrapper'

// 懒加载页面组件
const HomePage = lazy(() => import('@pages/HomePage'))
const DashboardPage = lazy(() => import('@pages/DashboardPage'))
const PricesPage = lazy(() => import('@pages/PricesPage'))
const PredictionPage = lazy(() => import('@pages/PredictionPage'))
const AnalysisPage = lazy(() => import('@pages/AnalysisPage'))
const NewsPage = lazy(() => import('@pages/NewsPage'))
const SettingsPage = lazy(() => import('@pages/SettingsPage'))
const PaymentTestPage = lazy(() => import('@pages/PaymentTestPage'))

// 懒加载组件包装器
export const LazyHomePage = () => (
  <LazyWrapper fallback={<PageFallback />}>
    <HomePage />
  </LazyWrapper>
)

export const LazyDashboardPage = () => (
  <LazyWrapper fallback={<PageFallback />}>
    <DashboardPage />
  </LazyWrapper>
)

export const LazyPricesPage = () => (
  <LazyWrapper fallback={<PageFallback />}>
    <PricesPage />
  </LazyWrapper>
)

export const LazyPredictionPage = () => (
  <LazyWrapper fallback={<PageFallback />}>
    <PredictionPage />
  </LazyWrapper>
)

export const LazyAnalysisPage = () => (
  <LazyWrapper fallback={<PageFallback />}>
    <AnalysisPage />
  </LazyWrapper>
)

export const LazyNewsPage = () => (
  <LazyWrapper fallback={<PageFallback />}>
    <NewsPage />
  </LazyWrapper>
)

export const LazySettingsPage = () => (
  <LazyWrapper fallback={<PageFallback />}>
    <SettingsPage />
  </LazyWrapper>
)

export const LazyPaymentTestPage = () => (
  <LazyWrapper fallback={<PageFallback />}>
    <PaymentTestPage />
  </LazyWrapper>
)

// 路由配置
export const routes = [
  {
    path: '/',
    component: LazyHomePage,
    preload: true // 标记为需要预加载的路由
  },
  {
    path: '/dashboard',
    component: LazyDashboardPage,
    preload: true
  },
  {
    path: '/prices',
    component: LazyPricesPage,
    preload: false
  },
  {
    path: '/prediction',
    component: LazyPredictionPage,
    preload: false
  },
  {
    path: '/analysis',
    component: LazyAnalysisPage,
    preload: false
  },
  {
    path: '/news',
    component: LazyNewsPage,
    preload: false
  },
  {
    path: '/settings',
    component: LazySettingsPage,
    preload: false
  }
]

// 预加载关键路由
export const preloadRoutes = () => {
  routes.forEach(route => {
    if (route.preload) {
      // 预加载组件
      route.component()
    }
  })
}

// 路由预加载Hook
export const useRoutePreload = () => {
  const preloadRoute = (path: string) => {
    const route = routes.find(r => r.path === path)
    if (route) {
      route.component()
    }
  }

  return { preloadRoute }
}
