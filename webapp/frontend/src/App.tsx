import React, { Suspense, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'

// 布局组件
import Layout from '@components/layout/Layout'
import LoadingSpinner from '@components/common/LoadingSpinner'
import ErrorBoundary from '@components/common/ErrorBoundary'
import PerformanceMonitor from '@components/dev/PerformanceMonitor'
import { PageFallback } from '@components/common/LazyWrapper'

// 认证组件
import { ProtectedRoute } from '@components/auth/ProtectedRoute'
import { useAuthStore } from '@stores/authStore'

// 页面组件 (懒加载)
const HomePage = React.lazy(() => import('@pages/HomePage'))
const DashboardPage = React.lazy(() => import('@pages/DashboardPage'))
const PricesPage = React.lazy(() => import('@pages/PricesPage'))
const MarketPage = React.lazy(() => import('@pages/MarketPage'))
const PredictionPage = React.lazy(() => import('@pages/PredictionPage'))
const AnalysisPage = React.lazy(() => import('@pages/AnalysisPage'))
const NewsPage = React.lazy(() => import('@pages/NewsPage'))
const SettingsPage = React.lazy(() => import('@pages/SettingsPage'))
const AdminPage = React.lazy(() => import('@pages/AdminPage'))
const PaymentRecordsPage = React.lazy(() => import('@pages/admin/PaymentRecordsPage'))
const PaymentStatsPage = React.lazy(() => import('@pages/admin/PaymentStatsPage'))
const UserSubscriptionsPage = React.lazy(() => import('@pages/admin/UserSubscriptionsPage'))
const SystemManagePage = React.lazy(() => import('@pages/admin/SystemManagePage'))
const VisitorAnalyticsPage = React.lazy(() => import('@pages/admin/VisitorAnalyticsPage'))
const QRCodeAnalyticsPage = React.lazy(() => import('@pages/admin/QRCodeAnalyticsPage'))
const NotFoundPage = React.lazy(() => import('@pages/NotFoundPage'))
const TestCoinSwitching = React.lazy(() => import('@pages/TestCoinSwitching'))
const PaymentTestPage = React.lazy(() => import('@pages/PaymentTestPage'))

// 法律信息页面
const PrivacyPage = React.lazy(() => import('@pages/PrivacyPage'))
const TermsPage = React.lazy(() => import('@pages/TermsPage'))
const DisclaimerPage = React.lazy(() => import('@pages/DisclaimerPage'))
const CookiesPage = React.lazy(() => import('@pages/CookiesPage'))

// 资源页面
const ApiDocsPage = React.lazy(() => import('@pages/ApiDocsPage'))
const HelpCenterPage = React.lazy(() => import('@pages/HelpCenterPage'))
const UserGuidePage = React.lazy(() => import('@pages/UserGuidePage'))
const TechBlogPage = React.lazy(() => import('@pages/TechBlogPage'))
const BlogPostPage = React.lazy(() => import('@pages/BlogPostPage'))

// Hooks
import { useTheme } from '@hooks/useTheme'
import { useWebSocket } from '@hooks/useWebSocket'
import { usePreloadStrategy } from '@hooks/usePreloadStrategy'
import { autoPreload } from '@utils/imagePreloader'
import { MarketService } from '@services/marketService'

// 页面过渡动画配置
const pageVariants = {
  initial: {
    opacity: 0,
    x: -20
  },
  in: {
    opacity: 1,
    x: 0
  },
  out: {
    opacity: 0,
    x: 20
  }
}

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3
}

// 加载页面组件
const LoadingPage: React.FC = () => (
  <div className="min-h-screen bg-trading-bg flex items-center justify-center relative overflow-hidden">
    {/* Background Effects */}
    <div className="absolute inset-0 cyber-grid opacity-20" />
    <div className="absolute inset-0 bg-gradient-to-br from-neon-blue/5 via-transparent to-neon-purple/5" />

    {/* Content */}
    <div className="relative z-10 text-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        className="mb-8"
      >
        <div className="w-20 h-20 mx-auto mb-6 relative">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            className="absolute inset-0 border-4 border-neon-blue/30 border-t-neon-blue rounded-full"
          />
          <motion.div
            animate={{ rotate: -360 }}
            transition={{ duration: 3, repeat: Infinity, ease: 'linear' }}
            className="absolute inset-2 border-2 border-neon-purple/30 border-b-neon-purple rounded-full"
          />
        </div>
        <h2 className="text-2xl font-bold text-white mb-2">Coin-Analyze</h2>
        <motion.p
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 1.5, repeat: Infinity }}
          className="text-trading-text-secondary"
        >
          正在加载专业分析平台...
        </motion.p>
      </motion.div>

      {/* Progress Bar */}
      <div className="w-64 h-1 bg-trading-border rounded-full overflow-hidden mx-auto">
        <motion.div
          className="h-full bg-gradient-to-r from-neon-blue to-neon-purple rounded-full"
          initial={{ width: '0%' }}
          animate={{ width: '100%' }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
      </div>
    </div>
  </div>
)

// 错误边界组件
// 页面级错误边界已由ErrorBoundary组件替代

const App: React.FC = () => {
  const { theme, initializeTheme } = useTheme()
  const { isConnected, connectionStatus } = useWebSocket()
  const { preloadedRoutes, preloadedImages } = usePreloadStrategy()
  const { refreshUser, token } = useAuthStore()

  // 初始化主题
  useEffect(() => {
    initializeTheme()
  }, [initializeTheme])

  // 初始化用户认证状态
  useEffect(() => {
    if (token) {
      refreshUser()
    }
  }, [token, refreshUser])

  // 应用主题类
  useEffect(() => {
    const root = document.documentElement
    if (theme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }, [theme])

  // 初始化服务和预加载策略
  useEffect(() => {
    // 初始化市场数据服务
    MarketService.initialize().then(() => {
      console.log('📊 市场数据服务初始化完成')
    })

    // 页面加载完成后预加载关键资源
    autoPreload.onPageLoad()

    // 用户首次交互后预加载
    autoPreload.onUserInteraction()

    // 空闲时预加载
    autoPreload.onIdle()
  }, [])

  // 连接状态监控
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('WebSocket连接状态:', connectionStatus)
      console.log('预加载状态:', {
        routes: preloadedRoutes.length,
        images: preloadedImages.length
      })
    }
  }, [connectionStatus, preloadedRoutes.length, preloadedImages.length])

  return (
    <div className="min-h-screen bg-dark-900 text-dark-100">
      {/* 连接状态指示器 */}
      {!isConnected && (
        <div className="fixed top-0 left-0 right-0 bg-warning-600 text-white text-center py-2 text-sm z-50">
          <span className="inline-flex items-center">
            <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
            正在重新连接服务器...
          </span>
        </div>
      )}

      <ErrorBoundary>
        <Layout>
          <AnimatePresence mode="wait">
            <Suspense fallback={<LoadingPage />}>
              <Routes>
              {/* 首页 */}
              <Route
                path="/"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <HomePage />
                  </motion.div>
                }
              />

              {/* 仪表板页面 */}
              <Route
                path="/dashboard"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <DashboardPage />
                  </motion.div>
                }
              />

              {/* 价格页面 */}
              <Route
                path="/prices"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <PricesPage />
                  </motion.div>
                }
              />

              {/* 市场数据页面 */}
              <Route
                path="/market"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <MarketPage />
                  </motion.div>
                }
              />

              {/* 市场数据页面 */}
              <Route
                path="/market"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <MarketPage />
                  </motion.div>
                }
              />

              {/* AI预测页面 */}
              <Route
                path="/prediction"
                element={
                  <ProtectedRoute requirePremium>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <PredictionPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 技术分析页面 */}
              <Route
                path="/analysis"
                element={
                  <ProtectedRoute requirePremium>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <AnalysisPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 新闻页面 */}
              <Route
                path="/news"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <NewsPage />
                  </motion.div>
                }
              />

              {/* 设置页面 */}
              <Route
                path="/settings"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <SettingsPage />
                  </motion.div>
                }
              />

              {/* 管理员后台 */}
              <Route
                path="/admin"
                element={
                  <ProtectedRoute requireAdmin>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <AdminPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 管理员后台子页面 */}
              <Route
                path="/admin/payments/records"
                element={
                  <ProtectedRoute requireAdmin>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <PaymentRecordsPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/admin/payments/statistics"
                element={
                  <ProtectedRoute requireAdmin>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <PaymentStatsPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/admin/users/subscriptions"
                element={
                  <ProtectedRoute requireAdmin>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <UserSubscriptionsPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/admin/system"
                element={
                  <ProtectedRoute requireAdmin>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <SystemManagePage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/admin/analytics/visitors"
                element={
                  <ProtectedRoute requireAdmin>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <VisitorAnalyticsPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/admin/analytics/qr-codes"
                element={
                  <ProtectedRoute requireAdmin>
                    <motion.div
                      initial="initial"
                      animate="in"
                      exit="out"
                      variants={pageVariants}
                      transition={pageTransition}
                    >
                      <QRCodeAnalyticsPage />
                    </motion.div>
                  </ProtectedRoute>
                }
              />

              {/* 测试页面 */}
              <Route
                path="/test-coin-switching"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <TestCoinSwitching />
                  </motion.div>
                }
              />

              {/* 法律信息页面 */}
              <Route
                path="/privacy"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <PrivacyPage />
                  </motion.div>
                }
              />

              <Route
                path="/terms"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <TermsPage />
                  </motion.div>
                }
              />

              <Route
                path="/disclaimer"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <DisclaimerPage />
                  </motion.div>
                }
              />

              <Route
                path="/cookies"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <CookiesPage />
                  </motion.div>
                }
              />

              {/* 资源页面 */}
              <Route
                path="/api/docs"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <ApiDocsPage />
                  </motion.div>
                }
              />

              <Route
                path="/help"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <HelpCenterPage />
                  </motion.div>
                }
              />

              <Route
                path="/docs/guide"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <UserGuidePage />
                  </motion.div>
                }
              />

              <Route
                path="/blog"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <TechBlogPage />
                  </motion.div>
                }
              />

              <Route
                path="/blog/post/:id"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <BlogPostPage />
                  </motion.div>
                }
              />

              {/* 测试页面 */}
              <Route
                path="/payment-test"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <PaymentTestPage />
                  </motion.div>
                }
              />

              {/* 重定向旧路径 */}
              <Route path="/dashboard" element={<Navigate to="/" replace />} />
              <Route path="/charts" element={<Navigate to="/market" replace />} />
              <Route path="/tools" element={<Navigate to="/analysis" replace />} />

              {/* 404页面 */}
              <Route
                path="*"
                element={
                  <motion.div
                    initial="initial"
                    animate="in"
                    exit="out"
                    variants={pageVariants}
                    transition={pageTransition}
                  >
                    <NotFoundPage />
                  </motion.div>
                }
              />
              </Routes>
            </Suspense>
          </AnimatePresence>
        </Layout>
      </ErrorBoundary>

      {/* 开发模式调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 bg-trading-surface/95 backdrop-blur-sm text-trading-text-secondary text-xs p-3 rounded-xl border border-neon-blue/30 font-mono">
          <div>主题: {theme}</div>
          <div>WebSocket: {isConnected ? '已连接' : '未连接'}</div>
          <div>环境: {process.env.NODE_ENV}</div>
        </div>
      )}

      {/* 性能监控 */}
      <PerformanceMonitor />
    </div>
  )
}

export default App
