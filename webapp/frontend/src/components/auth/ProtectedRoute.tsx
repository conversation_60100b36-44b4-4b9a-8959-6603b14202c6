/**
 * 权限保护路由组件
 */

import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../../stores/authStore'
import { PremiumRequired } from './PremiumRequired'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean
  requirePremium?: boolean
  requireAdmin?: boolean
  fallback?: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = false,
  requirePremium = false,
  requireAdmin = false,
  fallback
}) => {
  const { user, isAuthenticated } = useAuthStore()
  const location = useLocation()

  // 如果需要认证但用户未登录
  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // 如果需要管理员权限但用户不是管理员
  if (requireAdmin && (!user || user.role !== 'admin')) {
    return fallback || <Navigate to="/" replace />
  }

  // 如果需要高级权限但用户没有
  if (requirePremium && (!user || !user.has_premium_access)) {
    return <PremiumRequired />
  }

  return <>{children}</>
}

/**
 * 权限检查Hook
 */
export const usePermissions = () => {
  const { user, isAuthenticated } = useAuthStore()

  return {
    isAuthenticated,
    isAdmin: user?.role === 'admin',
    isPremium: user?.has_premium_access || false,
    isFree: user?.role === 'free',
    isMonthly: user?.role === 'monthly',
    isQuarterly: user?.role === 'quarterly',
    isYearly: user?.role === 'yearly',
    user
  }
}

/**
 * 权限保护组件 - 用于包装需要权限的UI元素
 */
interface PermissionGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requirePremium?: boolean
  requireAdmin?: boolean
  fallback?: React.ReactNode
  showFallback?: boolean
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  requireAuth = false,
  requirePremium = false,
  requireAdmin = false,
  fallback = null,
  showFallback = true
}) => {
  const { user, isAuthenticated } = useAuthStore()

  // 检查认证
  if (requireAuth && !isAuthenticated) {
    return showFallback ? (fallback || <div>请先登录</div>) : null
  }

  // 检查管理员权限
  if (requireAdmin && (!user || user.role !== 'admin')) {
    return showFallback ? (fallback || <div>需要管理员权限</div>) : null
  }

  // 检查高级权限
  if (requirePremium && (!user || !user.has_premium_access)) {
    return showFallback ? (fallback || <PremiumRequired inline />) : null
  }

  return <>{children}</>
}
