/**
 * 高级权限提示组件
 */

import React, { useState } from 'react'
import { Crown, Lock, Star, Check, X, Key } from 'lucide-react'
import { useAuthStore } from '../../stores/authStore'
import { PersonalPaymentModal } from '../payment/PersonalPaymentModal'
import { ActivationCodeModal } from '../payment/ActivationCodeModal'
import { LoginModal } from './LoginModal'

interface PremiumRequiredProps {
  inline?: boolean
  title?: string
  description?: string
}

export const PremiumRequired: React.FC<PremiumRequiredProps> = ({
  inline = false,
  title = "此功能需要高级会员权限",
  description = "升级到高级会员以解锁AI预测分析和专业分析工具"
}) => {
  const [showUpgrade, setShowUpgrade] = useState(false)
  const [showPayment, setShowPayment] = useState(false)
  const [showActivationCode, setShowActivationCode] = useState(false)
  const [showLogin, setShowLogin] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<{
    type: 'monthly' | 'quarterly' | 'yearly'
    name: string
    price: string
  } | null>(null)
  const { user, subscribe, isLoading, refreshUser } = useAuthStore()

  const plans = [
    {
      id: 'monthly',
      name: '月卡会员',
      price: '¥29.99',
      period: '月',
      features: [
        'AI智能预测分析',
        '专业技术分析工具',
        '实时市场数据',
        '多币种支持',
        '基础客服支持'
      ],
      popular: false
    },
    {
      id: 'quarterly',
      name: '季卡会员',
      price: '¥79.99',
      period: '季度',
      originalPrice: '¥89.97',
      features: [
        '包含月卡所有功能',
        '高级分析报告',
        '策略回测功能',
        '优先客服支持',
        '专属投资建议'
      ],
      popular: true
    },
    {
      id: 'yearly',
      name: '年卡会员',
      price: '¥299.99',
      period: '年',
      originalPrice: '¥359.88',
      features: [
        '包含季卡所有功能',
        '个性化投资组合',
        '专业风险评估',
        '一对一投资顾问',
        '独家市场研报'
      ],
      popular: false
    }
  ]

  const handleSubscribe = (planId: 'monthly' | 'quarterly' | 'yearly') => {
    // 检查用户是否已登录
    if (!user) {
      // 保存选择的套餐信息
      const planMap = {
        monthly: { name: '月卡会员', price: '¥29.99' },
        quarterly: { name: '季卡会员', price: '¥79.99' },
        yearly: { name: '年卡会员', price: '¥299.99' }
      }

      setSelectedPlan({
        type: planId,
        name: planMap[planId].name,
        price: planMap[planId].price
      })

      // 显示登录模态框
      setShowLogin(true)
      return
    }

    // 用户已登录，直接显示支付界面
    const planMap = {
      monthly: { name: '月卡会员', price: '¥29.99' },
      quarterly: { name: '季卡会员', price: '¥79.99' },
      yearly: { name: '年卡会员', price: '¥299.99' }
    }

    setSelectedPlan({
      type: planId,
      name: planMap[planId].name,
      price: planMap[planId].price
    })
    setShowPayment(true)
  }

  const handlePaymentSuccess = () => {
    setShowPayment(false)
    setShowUpgrade(false)
    setSelectedPlan(null) // 重置选择的套餐
    refreshUser()
  }

  const handleActivationCodeSuccess = () => {
    setShowActivationCode(false)
    setShowUpgrade(false)
    setSelectedPlan(null) // 重置选择的套餐
  }

  const handleLoginSuccess = () => {
    setShowLogin(false)
    // 登录成功后，如果有选择的套餐，直接显示支付界面
    if (selectedPlan) {
      setShowPayment(true)
    }
  }

  const handleUpgradeClick = () => {
    if (!user) {
      setShowLogin(true)
    } else {
      setShowUpgrade(true)
    }
  }

  const handleActivationCodeClick = () => {
    if (!user) {
      setShowLogin(true)
    } else {
      setShowActivationCode(true)
    }
  }

  if (inline) {
    return (
      <div className="flex items-center justify-center p-8 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Crown className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {title}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {description}
          </p>
          <div className="flex gap-2">
            <button
              onClick={handleUpgradeClick}
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium px-4 py-2 rounded-lg transition-all"
            >
              立即升级
            </button>
            <button
              onClick={handleActivationCodeClick}
              className="bg-gray-500 hover:bg-gray-600 text-white font-medium px-4 py-2 rounded-lg transition-all flex items-center gap-1"
            >
              <Key className="w-4 h-4" />
              激活码
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <Crown className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {title}
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400">
            {description}
          </p>
        </div>

        {/* Pricing Plans */}
        <div className="grid md:grid-cols-3 gap-8 mb-8">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg border-2 transition-all hover:shadow-xl ${
                plan.popular
                  ? 'border-blue-500 scale-105'
                  : 'border-gray-200 dark:border-gray-700'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                    <Star className="w-4 h-4" />
                    推荐
                  </div>
                </div>
              )}

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  {plan.name}
                </h3>
                
                <div className="mb-4">
                  <div className="flex items-baseline gap-2">
                    <span className="text-3xl font-bold text-gray-900 dark:text-white">
                      {plan.price}
                    </span>
                    <span className="text-gray-500 dark:text-gray-400">
                      /{plan.period}
                    </span>
                  </div>
                  {plan.originalPrice && (
                    <div className="text-sm text-gray-500 line-through">
                      原价 {plan.originalPrice}
                    </div>
                  )}
                </div>

                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => handleSubscribe(plan.id)}
                  disabled={isLoading}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-all ${
                    plan.popular
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white'
                  } disabled:opacity-50`}
                >
                  {isLoading ? '处理中...' : '立即订阅'}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Features Highlight */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
            高级会员专享功能
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Crown className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                AI智能预测
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                基于深度学习的价格预测分析
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                专业分析工具
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                技术指标和相关性分析
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Lock className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                独家研报
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                专业市场分析报告
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Check className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                优先支持
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                专属客服和技术支持
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Upgrade Modal */}
      {showUpgrade && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                选择订阅计划
              </h3>
              <button
                onClick={() => setShowUpgrade(false)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>
            
            <div className="space-y-3">
              {plans.map((plan) => (
                <button
                  key={plan.id}
                  onClick={() => handleSubscribe(plan.id as 'monthly' | 'quarterly' | 'yearly')}
                  disabled={isLoading}
                  className="w-full p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 transition-colors text-left"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {plan.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {plan.price}/{plan.period}
                      </div>
                    </div>
                    {plan.popular && (
                      <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-600 px-2 py-1 rounded text-xs">
                        推荐
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      {selectedPlan && (
        <PersonalPaymentModal
          isOpen={showPayment}
          onClose={() => setShowPayment(false)}
          productType={selectedPlan.type}
          onSuccess={handlePaymentSuccess}
        />
      )}

      {/* Activation Code Modal */}
      <ActivationCodeModal
        isOpen={showActivationCode}
        onClose={() => setShowActivationCode(false)}
        onSuccess={handleActivationCodeSuccess}
      />

      {/* Login Modal */}
      <LoginModal
        isOpen={showLogin}
        onClose={() => setShowLogin(false)}
        onSuccess={handleLoginSuccess}
      />
    </div>
  )
}
