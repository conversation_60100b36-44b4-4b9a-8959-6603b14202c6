/**
 * 用户菜单组件
 */

import React, { useState, useRef, useEffect } from 'react'
import {
  User,
  Settings,
  LogOut,
  Crown,
  Shield,
  Calendar,
  ChevronDown,
  CreditCard,
  Key
} from 'lucide-react'
import { useAuthStore } from '../../stores/authStore'
import { ActivationCodeModal } from '../payment/ActivationCodeModal'

interface UserMenuProps {
  onOpenProfile?: () => void
  onOpenAdmin?: () => void
}

export const UserMenu: React.FC<UserMenuProps> = ({
  onOpenProfile,
  onOpenAdmin
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [showActivationCode, setShowActivationCode] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const { user, logout } = useAuthStore()

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  if (!user) return null

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="w-4 h-4 text-red-500" />
      case 'yearly':
        return <Crown className="w-4 h-4 text-yellow-500" />
      case 'quarterly':
        return <Crown className="w-4 h-4 text-blue-500" />
      case 'monthly':
        return <Crown className="w-4 h-4 text-green-500" />
      default:
        return <User className="w-4 h-4 text-gray-500" />
    }
  }

  const getRoleText = (role: string) => {
    switch (role) {
      case 'admin':
        return '管理员'
      case 'yearly':
        return '年卡会员'
      case 'quarterly':
        return '季卡会员'
      case 'monthly':
        return '月卡会员'
      default:
        return '免费用户'
    }
  }

  const handleLogout = () => {
    logout()
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={menuRef}>
      {/* User Avatar Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
          <span className="text-white text-sm font-medium">
            {user.username.charAt(0).toUpperCase()}
          </span>
        </div>
        <div className="hidden md:block text-left">
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            {user.full_name || user.username}
          </div>
          <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
            {getRoleIcon(user.role)}
            {getRoleText(user.role)}
          </div>
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50">
          {/* User Info */}
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-medium">
                  {user.username.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <div className="font-medium text-gray-900 dark:text-white">
                  {user.full_name || user.username}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {user.email}
                </div>
                <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {getRoleIcon(user.role)}
                  {getRoleText(user.role)}
                </div>
              </div>
            </div>
          </div>

          {/* Subscription Info */}
          {user.current_subscription && (
            <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4 text-blue-500" />
                <span className="text-gray-600 dark:text-gray-400">
                  剩余 {user.current_subscription.days_remaining} 天
                </span>
              </div>
            </div>
          )}

          {/* Menu Items */}
          <div className="py-1">
            <button
              onClick={() => {
                onOpenProfile?.()
                setIsOpen(false)
              }}
              className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <User className="w-4 h-4" />
              个人资料
            </button>

            {!user.has_premium_access && (
              <>
                <button
                  onClick={() => {
                    // TODO: 打开订阅页面
                    setIsOpen(false)
                  }}
                  className="w-full flex items-center gap-3 px-4 py-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                >
                  <CreditCard className="w-4 h-4" />
                  升级会员
                </button>

                <button
                  onClick={() => {
                    setShowActivationCode(true)
                    setIsOpen(false)
                  }}
                  className="w-full flex items-center gap-3 px-4 py-2 text-sm text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20"
                >
                  <Key className="w-4 h-4" />
                  使用激活码
                </button>
              </>
            )}

            {user.role === 'admin' && (
              <a
                href="/admin"
                onClick={() => setIsOpen(false)}
                className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <Shield className="w-4 h-4" />
                管理后台
              </a>
            )}

            <button
              onClick={() => {
                // TODO: 打开设置页面
                setIsOpen(false)
              }}
              className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <Settings className="w-4 h-4" />
              设置
            </button>
          </div>

          {/* Logout */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-1">
            <button
              onClick={handleLogout}
              className="w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
            >
              <LogOut className="w-4 h-4" />
              退出登录
            </button>
          </div>
        </div>
      )}

      {/* Activation Code Modal */}
      <ActivationCodeModal
        isOpen={showActivationCode}
        onClose={() => setShowActivationCode(false)}
        onSuccess={() => {
          // 激活成功后可以添加额外的处理逻辑
        }}
      />
    </div>
  )
}
