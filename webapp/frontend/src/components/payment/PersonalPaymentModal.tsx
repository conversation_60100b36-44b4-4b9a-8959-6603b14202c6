/**
 * 个人收款码支付模态框
 */

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>, Check<PERSON><PERSON>cle, Clock, AlertCircle } from 'lucide-react'
import { api } from '../../services/api'

interface PersonalPaymentModalProps {
  isOpen: boolean
  onClose: () => void
  productType: 'monthly' | 'quarterly' | 'yearly'
  onSuccess?: () => void
}

interface PaymentOrder {
  success: boolean
  order_no: string
  amount: number
  payment_method: string
  qr_code_url: string
  account_name: string
  account_info: string
  payment_reference: string
  expires_at: string
  instructions: string[]
  error?: string
}

interface QRCodeOption {
  qr_code_url: string
  account_name: string
  account_info: string
}

interface QRCodesResponse {
  success: boolean
  qr_codes: {
    wechat: QRCodeOption[]
    alipay: QRCodeOption[]
  }
}

export const PersonalPaymentModal: React.FC<PersonalPaymentModalProps> = ({
  isOpen,
  onClose,
  productType,
  onSuccess
}) => {
  const [selectedMethod, setSelectedMethod] = useState<'wechat' | 'alipay'>('wechat')
  const [paymentOrder, setPaymentOrder] = useState<PaymentOrder | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [userNote, setUserNote] = useState('')
  const [paymentSubmitted, setPaymentSubmitted] = useState(false)
  const [qrCodes, setQrCodes] = useState<QRCodesResponse | null>(null)
  const [selectedQRIndex, setSelectedQRIndex] = useState<number>(0)

  const productNames = {
    monthly: '月卡会员',
    quarterly: '季卡会员',
    yearly: '年卡会员'
  }

  // 获取收款码选项
  const fetchQRCodes = async () => {
    try {
      const response = await api.get('/api/v1/personal-payment/qr-codes')
      if (response.data.success) {
        setQrCodes(response.data)
      }
    } catch (error: any) {
      console.error('获取收款码失败:', error)
    }
  }

  // 当模态框打开时获取收款码选项
  React.useEffect(() => {
    if (isOpen && !qrCodes) {
      fetchQRCodes()
    }
  }, [isOpen])

  // 当支付方式变化时重置收款码选择
  React.useEffect(() => {
    setSelectedQRIndex(0)
  }, [selectedMethod])

  // 当模态框关闭时重置所有状态
  React.useEffect(() => {
    if (!isOpen) {
      setSelectedMethod('wechat')
      setPaymentOrder(null)
      setIsLoading(false)
      setIsSubmitting(false)
      setUserNote('')
      setPaymentSubmitted(false)
      setSelectedQRIndex(0)
      // 不重置qrCodes，避免重复获取
    }
  }, [isOpen])

  const handleCreatePayment = async () => {
    setIsLoading(true)
    try {
      const response = await api.post('/api/v1/personal-payment/create', {
        product_type: productType,
        payment_method: selectedMethod,
        qr_code_index: selectedQRIndex
      })

      if (response.data.success) {
        setPaymentOrder(response.data)
      } else {
        alert(response.data.error || '创建支付订单失败')
      }
    } catch (error: any) {
      console.error('创建支付订单失败:', error)
      alert(error.response?.data?.detail || '创建支付订单失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmitPayment = async () => {
    if (!paymentOrder) return

    setIsSubmitting(true)
    try {
      const response = await api.post('/api/v1/personal-payment/submit', {
        order_no: paymentOrder.order_no,
        user_note: userNote
      })

      if (response.data.success) {
        setPaymentSubmitted(true)
        alert('付款提交成功！请等待管理员确认，确认后将自动发放激活码。')
      } else {
        alert('付款提交失败')
      }
    } catch (error: any) {
      console.error('提交付款失败:', error)
      alert(error.response?.data?.detail || '提交付款失败')
    } finally {
      setIsSubmitting(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // 可以添加复制成功的提示
  }

  const formatExpiresAt = (expiresAt: string) => {
    const date = new Date(expiresAt)
    return date.toLocaleString('zh-CN')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            购买{productNames[productType]}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          {!paymentOrder ? (
            // 选择支付方式
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  选择支付方式
                </h3>
                <div className="space-y-2">
                  <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="wechat"
                      checked={selectedMethod === 'wechat'}
                      onChange={(e) => setSelectedMethod(e.target.value as 'wechat')}
                      className="mr-3"
                    />
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-green-500 rounded flex items-center justify-center mr-3">
                        <span className="text-white text-xs font-bold">微信</span>
                      </div>
                      <span className="text-gray-900 dark:text-white">微信支付</span>
                    </div>
                  </label>
                  
                  <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="alipay"
                      checked={selectedMethod === 'alipay'}
                      onChange={(e) => setSelectedMethod(e.target.value as 'alipay')}
                      className="mr-3"
                    />
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center mr-3">
                        <span className="text-white text-xs font-bold">支付宝</span>
                      </div>
                      <span className="text-gray-900 dark:text-white">支付宝</span>
                    </div>
                  </label>
                </div>
              </div>

              {/* 收款码选择 */}
              {qrCodes && qrCodes.qr_codes[selectedMethod] && qrCodes.qr_codes[selectedMethod].length > 1 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                    选择收款码
                  </h3>
                  <div className="space-y-2">
                    {qrCodes.qr_codes[selectedMethod].map((qrCode, index) => (
                      <label key={index} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                        <input
                          type="radio"
                          name="qrCodeSelection"
                          value={index}
                          checked={selectedQRIndex === index}
                          onChange={() => setSelectedQRIndex(index)}
                          className="mr-3"
                        />
                        <div className="flex items-center">
                          <img
                            src={qrCode.qr_code_url}
                            alt={`${qrCode.account_name}的收款码`}
                            className="w-12 h-12 rounded border mr-3"
                            onError={(e) => {
                              e.currentTarget.src = 'https://via.placeholder.com/48x48?text=QR'
                            }}
                          />
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {qrCode.account_name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {qrCode.account_info}
                            </div>
                          </div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              <button
                onClick={handleCreatePayment}
                disabled={isLoading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '创建中...' : '创建支付订单'}
              </button>
            </div>
          ) : (
            // 显示支付信息
            <div className="space-y-6">
              {/* 订单信息 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">订单号</span>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-mono">{paymentOrder.order_no}</span>
                    <button
                      onClick={() => copyToClipboard(paymentOrder.order_no)}
                      className="text-blue-500 hover:text-blue-600"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">支付金额</span>
                  <span className="text-lg font-bold text-red-500">¥{paymentOrder.amount}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">过期时间</span>
                  <span className="text-sm text-gray-900 dark:text-white">
                    {formatExpiresAt(paymentOrder.expires_at)}
                  </span>
                </div>
              </div>

              {/* 收款码 */}
              <div className="text-center">
                <div className="bg-white p-4 rounded-lg border-2 border-dashed border-gray-300 inline-block">
                  <img
                    src={paymentOrder.qr_code_url}
                    alt="收款码"
                    className="w-48 h-48 mx-auto"
                    onError={(e) => {
                      e.currentTarget.src = 'https://via.placeholder.com/300x300?text=收款码'
                    }}
                  />
                </div>
                <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  <p>收款人：{paymentOrder.account_name}</p>
                  <p>账号：{paymentOrder.account_info}</p>
                </div>
              </div>

              {/* 付款备注 */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="w-5 h-5 text-yellow-500" />
                  <span className="font-medium text-yellow-800 dark:text-yellow-200">
                    重要：付款备注
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-lg font-mono font-bold text-yellow-800 dark:text-yellow-200">
                    {paymentOrder.payment_reference}
                  </span>
                  <button
                    onClick={() => copyToClipboard(paymentOrder.payment_reference)}
                    className="text-yellow-600 hover:text-yellow-700 dark:text-yellow-400"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                  请在付款时务必填写此备注，以便系统识别您的付款
                </p>
              </div>

              {/* 支付说明 */}
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900 dark:text-white">支付步骤：</h4>
                <ol className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  {paymentOrder.instructions.map((instruction, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="flex-shrink-0 w-5 h-5 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs">
                        {index + 1}
                      </span>
                      <span>{instruction}</span>
                    </li>
                  ))}
                </ol>
              </div>

              {!paymentSubmitted ? (
                // 付款确认区域
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      付款备注（可选）
                    </label>
                    <textarea
                      value={userNote}
                      onChange={(e) => setUserNote(e.target.value)}
                      placeholder="如有特殊说明，请在此填写..."
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      rows={3}
                    />
                  </div>

                  <button
                    onClick={handleSubmitPayment}
                    disabled={isSubmitting}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors disabled:opacity-50"
                  >
                    {isSubmitting ? '提交中...' : '我已付款'}
                  </button>
                </div>
              ) : (
                // 付款已提交状态
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <Clock className="w-5 h-5 text-green-500" />
                    <span className="font-medium text-green-800 dark:text-green-200">
                      付款已提交
                    </span>
                  </div>
                  <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                    您的付款已提交，请等待管理员确认。确认后将自动发放激活码到您的账户。
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
