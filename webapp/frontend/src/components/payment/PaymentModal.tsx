/**
 * 支付模态框组件
 */

import React, { useState } from 'react'
import { X, CreditCard, Smartphone, QrCode, CheckCircle, AlertCircle } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { api } from '../../services/api'

interface PaymentModalProps {
  isOpen: boolean
  onClose: () => void
  planType: 'monthly' | 'quarterly' | 'yearly'
  planName: string
  planPrice: string
}

interface PaymentOrder {
  success: boolean
  order_no: string
  qr_code_url?: string
  payment_url?: string
  amount: string
  expires_at: string
  demo_mode?: boolean
  message?: string
}

export const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  planType,
  planName,
  planPrice
}) => {
  const [step, setStep] = useState<'select' | 'paying' | 'success' | 'error'>('select')
  const [selectedMethod, setSelectedMethod] = useState<'wechat' | 'alipay' | null>(null)
  const [paymentOrder, setPaymentOrder] = useState<PaymentOrder | null>(null)
  const [activationCode, setActivationCode] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const handlePaymentMethodSelect = async (method: 'wechat' | 'alipay') => {
    setSelectedMethod(method)
    setIsLoading(true)
    setError(null)

    try {
      const response = await api.post('/api/v1/payment/create', {
        product_type: planType,
        payment_method: method
      })

      setPaymentOrder(response.data)
      setStep('paying')
    } catch (error: any) {
      setError(error.response?.data?.detail || '创建支付订单失败')
      setStep('error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDemoPayment = async () => {
    if (!paymentOrder) return

    setIsLoading(true)
    try {
      const response = await api.post('/api/v1/payment/demo/pay', {
        order_no: paymentOrder.order_no
      })

      if (response.data.success) {
        setActivationCode(response.data.activation_code)
        setStep('success')
      } else {
        setError('支付处理失败')
        setStep('error')
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || '支付失败')
      setStep('error')
    } finally {
      setIsLoading(false)
    }
  }

  const resetModal = () => {
    setStep('select')
    setSelectedMethod(null)
    setPaymentOrder(null)
    setActivationCode(null)
    setError(null)
    setIsLoading(false)
  }

  const handleClose = () => {
    resetModal()
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            {step === 'success' ? '支付成功' : step === 'error' ? '支付失败' : `购买${planName}`}
          </h2>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="p-6">
          <AnimatePresence mode="wait">
            {/* 选择支付方式 */}
            {step === 'select' && (
              <motion.div
                key="select"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-4"
              >
                <div className="text-center mb-6">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    {planPrice}
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">
                    {planName}
                  </div>
                </div>

                <div className="space-y-3">
                  <button
                    onClick={() => handlePaymentMethodSelect('wechat')}
                    disabled={isLoading}
                    className="w-full flex items-center gap-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors disabled:opacity-50"
                  >
                    <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                      <Smartphone className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-medium text-gray-900 dark:text-white">微信支付</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">使用微信扫码支付</div>
                    </div>
                  </button>

                  <button
                    onClick={() => handlePaymentMethodSelect('alipay')}
                    disabled={isLoading}
                    className="w-full flex items-center gap-3 p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors disabled:opacity-50"
                  >
                    <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                      <CreditCard className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-medium text-gray-900 dark:text-white">支付宝</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">使用支付宝扫码支付</div>
                    </div>
                  </button>
                </div>

                {isLoading && (
                  <div className="flex items-center justify-center py-4">
                    <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">创建订单中...</span>
                  </div>
                )}
              </motion.div>
            )}

            {/* 支付中 */}
            {step === 'paying' && paymentOrder && (
              <motion.div
                key="paying"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="text-center space-y-6"
              >
                <div className="space-y-2">
                  <QrCode className="w-16 h-16 text-gray-400 mx-auto" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    扫码支付
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    请使用{selectedMethod === 'wechat' ? '微信' : '支付宝'}扫描二维码完成支付
                  </p>
                </div>

                {paymentOrder.qr_code_url && (
                  <div className="flex justify-center">
                    <img
                      src={paymentOrder.qr_code_url}
                      alt="支付二维码"
                      className="w-48 h-48 border border-gray-200 dark:border-gray-600 rounded-lg"
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    订单号: {paymentOrder.order_no}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    金额: ¥{paymentOrder.amount}
                  </div>
                </div>

                {paymentOrder.demo_mode ? (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div className="text-sm text-yellow-800 dark:text-yellow-200 mb-3">
                      {paymentOrder.message}
                    </div>
                    <button
                      onClick={handleDemoPayment}
                      disabled={isLoading}
                      className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
                    >
                      {isLoading ? '处理中...' : '模拟支付完成'}
                    </button>
                  </div>
                ) : (
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div className="text-sm text-green-800 dark:text-green-200">
                      ✅ 真实支付已启用 - 请使用{selectedMethod === 'wechat' ? '微信' : '支付宝'}扫描上方二维码完成支付
                    </div>
                  </div>
                )}
              </motion.div>
            )}

            {/* 支付成功 */}
            {step === 'success' && (
              <motion.div
                key="success"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className="text-center space-y-6"
              >
                <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    支付成功！
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    您已成功购买{planName}
                  </p>
                </div>

                {activationCode && (
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div className="text-sm text-green-800 dark:text-green-200 mb-2">
                      您的激活码：
                    </div>
                    <div className="font-mono text-lg font-bold text-green-900 dark:text-green-100 bg-white dark:bg-gray-800 p-3 rounded border">
                      {activationCode}
                    </div>
                    <div className="text-xs text-green-700 dark:text-green-300 mt-2">
                      请保存好您的激活码，也可以在个人中心查看
                    </div>
                  </div>
                )}

                <button
                  onClick={handleClose}
                  className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  完成
                </button>
              </motion.div>
            )}

            {/* 支付失败 */}
            {step === 'error' && (
              <motion.div
                key="error"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className="text-center space-y-6"
              >
                <AlertCircle className="w-16 h-16 text-red-500 mx-auto" />
                <div className="space-y-2">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    支付失败
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {error || '支付过程中出现错误'}
                  </p>
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={resetModal}
                    className="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                  >
                    重试
                  </button>
                  <button
                    onClick={handleClose}
                    className="flex-1 bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                  >
                    关闭
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </div>
  )
}
