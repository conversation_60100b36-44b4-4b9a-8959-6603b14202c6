/**
 * 支付配置状态组件
 */

import React, { useState, useEffect } from 'react'
import { AlertCircle, CheckCircle, Settings, ExternalLink, Copy } from 'lucide-react'
import { api } from '../../services/api'

interface PaymentConfigStatus {
  is_configured: boolean
  payment_mode: string
  wechat_configured: boolean
  alipay_configured: boolean
  config_example?: string
}

export const PaymentConfigStatus: React.FC = () => {
  const [config, setConfig] = useState<PaymentConfigStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [showExample, setShowExample] = useState(false)

  useEffect(() => {
    fetchConfigStatus()
  }, [])

  const fetchConfigStatus = async () => {
    try {
      const response = await api.get('/api/v1/payment/config/status')
      setConfig(response.data)
    } catch (error) {
      console.error('获取支付配置状态失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // 可以添加复制成功的提示
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">检查支付配置...</span>
      </div>
    )
  }

  if (!config) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center gap-2">
          <AlertCircle className="w-5 h-5 text-red-500" />
          <span className="text-red-700 dark:text-red-400">无法获取支付配置状态</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 配置状态概览 */}
      <div className={`border rounded-lg p-4 ${
        config.is_configured 
          ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
          : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
      }`}>
        <div className="flex items-center gap-2 mb-2">
          {config.is_configured ? (
            <CheckCircle className="w-5 h-5 text-green-500" />
          ) : (
            <AlertCircle className="w-5 h-5 text-yellow-500" />
          )}
          <h3 className="font-medium text-gray-900 dark:text-white">
            支付配置状态
          </h3>
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-gray-600 dark:text-gray-400">配置状态:</span>
            <span className={`font-medium ${
              config.is_configured 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-yellow-600 dark:text-yellow-400'
            }`}>
              {config.is_configured ? '已配置' : '未配置（演示模式）'}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-600 dark:text-gray-400">运行模式:</span>
            <span className="font-medium text-gray-900 dark:text-white">
              {config.payment_mode === 'sandbox' ? '沙箱模式' : '生产模式'}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-600 dark:text-gray-400">微信支付:</span>
            <span className={`font-medium ${
              config.wechat_configured 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-gray-500 dark:text-gray-400'
            }`}>
              {config.wechat_configured ? '已配置' : '未配置'}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-600 dark:text-gray-400">支付宝:</span>
            <span className={`font-medium ${
              config.alipay_configured 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-gray-500 dark:text-gray-400'
            }`}>
              {config.alipay_configured ? '已配置' : '未配置'}
            </span>
          </div>
        </div>
      </div>

      {/* 配置说明 */}
      {!config.is_configured && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <Settings className="w-5 h-5 text-blue-500 mt-0.5" />
            <div className="flex-1">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                配置真实支付
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                当前系统运行在演示模式。要启用真实支付功能，请按照以下步骤配置：
              </p>
              
              <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <div className="flex items-center gap-2">
                  <span className="w-5 h-5 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs">1</span>
                  <span>安装支付SDK: <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">pip install python-alipay-sdk wechatpay-python</code></span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-5 h-5 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs">2</span>
                  <span>配置环境变量（见下方配置示例）</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-5 h-5 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs">3</span>
                  <span>重启后端服务</span>
                </div>
              </div>

              <div className="mt-3 flex gap-2">
                <button
                  onClick={() => setShowExample(!showExample)}
                  className="text-sm bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded transition-colors"
                >
                  {showExample ? '隐藏' : '显示'}配置示例
                </button>
                <a
                  href="/docs/PAYMENT_SETUP.md"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm bg-blue-100 dark:bg-blue-800 hover:bg-blue-200 dark:hover:bg-blue-700 text-blue-700 dark:text-blue-300 px-3 py-1 rounded transition-colors flex items-center gap-1"
                >
                  <ExternalLink className="w-3 h-3" />
                  详细文档
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 配置示例 */}
      {!config.is_configured && showExample && config.config_example && (
        <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-900 dark:text-white">
              环境变量配置示例
            </h4>
            <button
              onClick={() => copyToClipboard(config.config_example!)}
              className="text-sm bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded transition-colors flex items-center gap-1"
            >
              <Copy className="w-3 h-3" />
              复制
            </button>
          </div>
          <pre className="text-xs text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-900 p-3 rounded border overflow-x-auto">
            {config.config_example}
          </pre>
        </div>
      )}

      {/* 已配置状态的说明 */}
      {config.is_configured && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-900 dark:text-green-100 mb-1">
                真实支付已启用
              </h4>
              <p className="text-sm text-green-700 dark:text-green-300">
                系统已检测到支付配置，真实支付功能已启用。用户可以通过微信支付和支付宝完成真实交易。
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
