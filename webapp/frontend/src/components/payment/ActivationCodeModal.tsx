/**
 * 激活码使用模态框组件
 */

import React, { useState } from 'react'
import { X, Key, CheckCircle, AlertCircle } from 'lucide-react'
import { motion } from 'framer-motion'
import { api } from '../../services/api'
import { useAuthStore } from '../../stores/authStore'

interface ActivationCodeModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export const ActivationCodeModal: React.FC<ActivationCodeModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [code, setCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState('')
  const { refreshUser } = useAuthStore()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!code.trim()) {
      setError('请输入激活码')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await api.post('/api/v1/payment/activation-code/use', {
        code: code.trim()
      })

      if (response.data.success) {
        setSuccess(true)
        setSuccessMessage(response.data.message)
        
        // 刷新用户信息
        await refreshUser()
        
        // 调用成功回调
        onSuccess?.()
      }
    } catch (error: any) {
      setError(error.response?.data?.detail || '激活码使用失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setCode('')
    setError(null)
    setSuccess(false)
    setSuccessMessage('')
    onClose()
  }

  const formatCode = (value: string) => {
    // 移除所有非字母数字字符
    const cleaned = value.replace(/[^A-Z0-9]/g, '').toUpperCase()
    
    // 格式化为 XXXX-XXXX-XXXX-XXXX 格式
    const formatted = cleaned.replace(/(.{4})/g, '$1-').replace(/-$/, '')
    
    return formatted
  }

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCode(e.target.value)
    if (formatted.length <= 19) { // 16个字符 + 3个连字符
      setCode(formatted)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            {success ? '激活成功' : '使用激活码'}
          </h2>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="p-6">
          {success ? (
            /* 成功状态 */
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center space-y-6"
            >
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
              <div className="space-y-2">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  激活成功！
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {successMessage}
                </p>
              </div>
              <button
                onClick={handleClose}
                className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                完成
              </button>
            </motion.div>
          ) : (
            /* 输入表单 */
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="text-center space-y-2">
                <Key className="w-12 h-12 text-blue-500 mx-auto" />
                <p className="text-gray-600 dark:text-gray-400">
                  请输入您的激活码来升级会员等级
                </p>
              </div>

              {/* 错误提示 */}
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
                  <span className="text-sm text-red-600 dark:text-red-400">{error}</span>
                </div>
              )}

              {/* 激活码输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  激活码
                </label>
                <input
                  type="text"
                  value={code}
                  onChange={handleCodeChange}
                  placeholder="XXXX-XXXX-XXXX-XXXX"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-center text-lg tracking-wider"
                  disabled={isLoading}
                />
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  激活码格式：16位字母数字组合
                </div>
              </div>

              {/* 提交按钮 */}
              <button
                type="submit"
                disabled={isLoading || !code.trim()}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    激活中...
                  </>
                ) : (
                  '使用激活码'
                )}
              </button>

              {/* 说明文字 */}
              <div className="text-center space-y-2">
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  激活码可以通过购买会员服务获得
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  每个激活码只能使用一次
                </div>
              </div>
            </form>
          )}
        </div>
      </motion.div>
    </div>
  )
}
