import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  Menu,
  Search,
  Bell,
  Settings,
  Moon,
  Sun,
  TrendingUp,
  Wifi,
  WifiOff,
  LogIn
} from 'lucide-react'

import { useTheme } from '@hooks/useTheme'
import { useWebSocket } from '@hooks/useWebSocket'
import { useAuthStore } from '@stores/authStore'
import SearchModal from '@components/common/SearchModal'
import NotificationPanel from '@components/common/NotificationPanel'
import { LoginModal } from '@components/auth/LoginModal'
import { UserMenu } from '@components/auth/UserMenu'
import Logo from '@components/Logo'

interface HeaderProps {
  onToggleSidebar: () => void
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar }) => {
  const location = useLocation()
  const { theme, toggleTheme } = useTheme()
  const { isConnected } = useWebSocket()
  const { isAuthenticated } = useAuthStore()

  const [searchOpen, setSearchOpen] = useState(false)
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const [loginOpen, setLoginOpen] = useState(false)

  // 获取当前页面标题
  const getPageTitle = () => {
    const path = location.pathname
    switch (path) {
      case '/':
        return '市场概览'
      case '/market':
        return '价格与图表'
      case '/prediction':
        return 'AI预测分析'
      case '/analysis':
        return '分析工具'
      case '/news':
        return '新闻与研究'
      case '/settings':
        return '设置'
      default:
        return 'Coin-Analyze'
    }
  }

  return (
    <>
      <header className="bg-dark-800 border-b border-dark-700 sticky top-0 z-30">
        <div className="px-4 lg:px-6">
          <div className="flex items-center justify-between h-16">
            {/* Left Section */}
            <div className="flex items-center space-x-4">
              {/* Mobile Menu Button */}
              <button
                onClick={onToggleSidebar}
                className="lg:hidden p-2 rounded-lg text-dark-400 hover:text-dark-200 hover:bg-dark-700 transition-colors"
                aria-label="打开菜单"
              >
                <Menu size={20} />
              </button>

              {/* Logo & Title */}
              <Link to="/" className="flex items-center space-x-3">
                <Logo
                  variant="default"
                  size="sm"
                  showText={false}
                  style={{
                    filter: 'drop-shadow(0 0 8px rgba(0, 212, 255, 0.3))'
                  }}
                />
                <div className="hidden sm:block">
                  <h1 className="text-lg font-semibold text-white">
                    Coin-Analyze
                  </h1>
                  <p className="text-xs text-gray-400 -mt-1">
                    {getPageTitle()}
                  </p>
                </div>
              </Link>
            </div>

            {/* Center Section - Search (Desktop) */}
            <div className="hidden md:flex flex-1 max-w-md mx-8">
              <div className="relative w-full">
                <Search
                  size={18}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-400"
                />
                <input
                  type="text"
                  placeholder="搜索交易对..."
                  className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-dark-200 placeholder-dark-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                  onClick={() => setSearchOpen(true)}
                  readOnly
                />
              </div>
            </div>

            {/* Right Section */}
            <div className="flex items-center space-x-2">
              {/* Connection Status */}
              <div className="hidden sm:flex items-center space-x-2">
                {isConnected ? (
                  <div className="flex items-center space-x-1 text-success-400">
                    <Wifi size={16} />
                    <span className="text-xs">已连接</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-1 text-danger-400">
                    <WifiOff size={16} />
                    <span className="text-xs">未连接</span>
                  </div>
                )}
              </div>

              {/* Search Button (Mobile) */}
              <button
                onClick={() => setSearchOpen(true)}
                className="md:hidden p-2 rounded-lg text-dark-400 hover:text-dark-200 hover:bg-dark-700 transition-colors"
                aria-label="搜索"
              >
                <Search size={18} />
              </button>

              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg text-dark-400 hover:text-dark-200 hover:bg-dark-700 transition-colors"
                aria-label="切换主题"
              >
                {theme === 'dark' ? <Sun size={18} /> : <Moon size={18} />}
              </button>

              {/* Notifications */}
              <div className="relative">
                <button
                  onClick={() => setNotificationsOpen(!notificationsOpen)}
                  className="p-2 rounded-lg text-dark-400 hover:text-dark-200 hover:bg-dark-700 transition-colors relative"
                  aria-label="通知"
                >
                  <Bell size={18} />
                  {/* Notification Badge */}
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-danger-500 rounded-full text-xs flex items-center justify-center">
                    <span className="w-1.5 h-1.5 bg-white rounded-full"></span>
                  </span>
                </button>

                {/* Notification Panel */}
                {notificationsOpen && (
                  <NotificationPanel
                    onClose={() => setNotificationsOpen(false)}
                  />
                )}
              </div>

              {/* Settings */}
              <Link
                to="/settings"
                className="p-2 rounded-lg text-dark-400 hover:text-dark-200 hover:bg-dark-700 transition-colors"
                aria-label="设置"
              >
                <Settings size={18} />
              </Link>

              {/* User Authentication */}
              {isAuthenticated ? (
                <UserMenu />
              ) : (
                <button
                  onClick={() => setLoginOpen(true)}
                  className="flex items-center gap-2 px-3 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
                >
                  <LogIn size={16} />
                  <span className="hidden sm:inline">登录</span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Page Title */}
        <div className="sm:hidden px-4 pb-3">
          <h2 className="text-lg font-medium text-white">
            {getPageTitle()}
          </h2>
        </div>
      </header>

      {/* Search Modal */}
      <SearchModal
        isOpen={searchOpen}
        onClose={() => setSearchOpen(false)}
      />

      {/* Login Modal */}
      <LoginModal
        isOpen={loginOpen}
        onClose={() => setLoginOpen(false)}
      />
    </>
  )
}

export default Header
