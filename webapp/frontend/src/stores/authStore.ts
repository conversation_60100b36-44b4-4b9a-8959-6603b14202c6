/**
 * 用户认证状态管理
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { api } from '../services/api'

export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  role: 'admin' | 'free' | 'monthly' | 'quarterly' | 'yearly'
  is_active: boolean
  is_verified: boolean
  created_at: string
  last_login?: string
  timezone: string
  language: string
  avatar_url?: string
  has_premium_access: boolean
  current_subscription?: {
    id: number
    subscription_type: string
    status: string
    starts_at: string
    expires_at: string
    days_remaining: number
    is_active: boolean
    amount?: string
    currency: string
  }
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface AuthActions {
  login: (username: string, password: string) => Promise<void>
  register: (userData: {
    username: string
    email: string
    password: string
    full_name?: string
  }) => Promise<void>
  logout: () => void
  refreshUser: () => Promise<void>
  updateProfile: (userData: Partial<User>) => Promise<void>
  subscribe: (subscriptionType: string, paymentMethod?: string) => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (username: string, password: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await api.post('/api/v1/auth/login', {
            username,
            password
          })
          
          const { access_token, user } = response.data
          
          // 存储token到localStorage
          localStorage.setItem('auth_token', access_token)

          // 设置认证头
          api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`

          set({
            user,
            token: access_token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
          
          console.log('✅ 登录成功:', user.username)
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || '登录失败'
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          })
          throw new Error(errorMessage)
        }
      },

      register: async (userData) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await api.post('/api/v1/auth/register', userData)
          
          console.log('✅ 注册成功:', response.data.username)
          
          // 注册成功后自动登录
          await get().login(userData.username, userData.password)
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || '注册失败'
          set({
            isLoading: false,
            error: errorMessage
          })
          throw new Error(errorMessage)
        }
      },

      logout: () => {
        // 清除localStorage中的token
        localStorage.removeItem('auth_token')

        // 清除认证头
        delete api.defaults.headers.common['Authorization']
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null
        })
        
        console.log('👋 用户已登出')
      },

      refreshUser: async () => {
        const { token } = get()
        if (!token) return
        
        try {
          const response = await api.get('/api/v1/auth/me')
          set({ user: response.data })
        } catch (error) {
          console.error('刷新用户信息失败:', error)
          get().logout()
        }
      },

      updateProfile: async (userData) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await api.put('/api/v1/auth/me', userData)
          set({
            user: response.data,
            isLoading: false
          })
          console.log('✅ 个人资料更新成功')
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || '更新失败'
          set({
            isLoading: false,
            error: errorMessage
          })
          throw new Error(errorMessage)
        }
      },

      subscribe: async (subscriptionType: string, paymentMethod?: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await api.post('/api/v1/auth/subscribe', {
            subscription_type: subscriptionType,
            payment_method: paymentMethod
          })
          
          // 刷新用户信息以获取最新订阅状态
          await get().refreshUser()
          
          set({ isLoading: false })
          console.log('✅ 订阅成功:', subscriptionType)
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || '订阅失败'
          set({
            isLoading: false,
            error: errorMessage
          })
          throw new Error(errorMessage)
        }
      },

      clearError: () => set({ error: null }),
      
      setLoading: (loading: boolean) => set({ isLoading: loading })
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      }),
      onRehydrateStorage: () => (state) => {
        // 恢复认证头和localStorage
        if (state?.token) {
          localStorage.setItem('auth_token', state.token)
          api.defaults.headers.common['Authorization'] = `Bearer ${state.token}`
        }
      }
    }
  )
)
