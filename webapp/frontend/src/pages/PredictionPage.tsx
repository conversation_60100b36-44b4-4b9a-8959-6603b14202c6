import React, { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { createPortal } from 'react-dom'
import { Brain, Zap, Target, ChevronDown, AlertCircle } from 'lucide-react'
import ImperialEdict from '../components/ui/ImperialEdict'
import { useAuthStore } from '../stores/authStore'
import { LoginModal } from '../components/auth/LoginModal'
import axios from 'axios'

// 模型选项配置
const MODEL_OPTIONS = [
  {
    value: 'cpu',
    label: 'CPU专业模型',
    description: 'professional_prediction_system.py',
    accuracy: '72.3%',
    status: '运行中'
  },
  {
    value: 'gpu',
    label: 'GPU深度学习模型',
    description: 'professional_prediction_system_gpu.py',
    accuracy: '78.5%',
    status: '运行中'
  },
  {
    value: 'binary',
    label: '增强二分类模型',
    description: 'interactive_enhanced_binary_system.py',
    accuracy: '80.96%',
    status: '运行中'
  }
]

// 支持的币种配置
const SYMBOL_OPTIONS = [
  { value: 'BTCUSDT', label: 'BTC/USDT', name: '比特币' },
  { value: 'ETHUSDT', label: 'ETH/USDT', name: '以太坊' },
  { value: 'BNBUSDT', label: 'BNB/USDT', name: '币安币' },
  { value: 'ADAUSDT', label: 'ADA/USDT', name: '艾达币' },
  { value: 'XRPUSDT', label: 'XRP/USDT', name: '瑞波币' },
  { value: 'SOLUSDT', label: 'SOL/USDT', name: 'Solana' },
  { value: 'DOTUSDT', label: 'DOT/USDT', name: '波卡' },
  { value: 'DOGEUSDT', label: 'DOGE/USDT', name: '狗狗币' },
  { value: 'AVAXUSDT', label: 'AVAX/USDT', name: '雪崩' },
  { value: 'LINKUSDT', label: 'LINK/USDT', name: '链环' }
]

const PredictionPage: React.FC = () => {
  const [selectedModel, setSelectedModel] = useState(MODEL_OPTIONS[0].value)
  const [selectedSymbol, setSelectedSymbol] = useState(SYMBOL_OPTIONS[0].value)
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false)
  const [isSymbolDropdownOpen, setIsSymbolDropdownOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showEdict, setShowEdict] = useState(false)
  const [predictionResult, setPredictionResult] = useState<any>(null)
  const [historyRecords, setHistoryRecords] = useState<any[]>([])
  const [modelDropdownPosition, setModelDropdownPosition] = useState<{top: number, left: number, width: number} | null>(null)
  const [symbolDropdownPosition, setSymbolDropdownPosition] = useState<{top: number, left: number, width: number} | null>(null)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const modelDropdownRef = useRef<HTMLDivElement>(null)
  const symbolDropdownRef = useRef<HTMLDivElement>(null)
  const modelPortalRef = useRef<HTMLDivElement>(null)
  const symbolPortalRef = useRef<HTMLDivElement>(null)

  // 获取认证状态
  const { user, isAuthenticated } = useAuthStore()

  // API基础URL - 使用环境变量配置
  const API_BASE = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

  // 获取历史预测记录
  const fetchHistoryRecords = async () => {
    // 只有登录且有权限的用户才获取历史记录
    if (!isAuthenticated || !user?.has_premium_access) {
      setHistoryRecords([])
      return
    }

    try {
      const response = await axios.get(`${API_BASE}/api/v1/prediction/history`, {
        params: {
          days: 7,
          limit: 20
        }
      })
      if (response.data.success) {
        setHistoryRecords(response.data.data)
      }
    } catch (error) {
      console.error('获取历史记录失败:', error)
      setHistoryRecords([])
    }
  }

  // 计算下拉菜单位置
  const calculateDropdownPosition = (ref: React.RefObject<HTMLDivElement>) => {
    if (!ref.current) return null
    const rect = ref.current.getBoundingClientRect()
    return {
      top: rect.bottom + window.scrollY + 8,
      left: rect.left + window.scrollX,
      width: rect.width
    }
  }

  // 处理模型下拉菜单开关
  const toggleModelDropdown = () => {
    if (!isModelDropdownOpen) {
      const position = calculateDropdownPosition(modelDropdownRef)
      setModelDropdownPosition(position)
    }
    setIsModelDropdownOpen(!isModelDropdownOpen)
  }

  // 处理币种下拉菜单开关
  const toggleSymbolDropdown = () => {
    if (!isSymbolDropdownOpen) {
      const position = calculateDropdownPosition(symbolDropdownRef)
      setSymbolDropdownPosition(position)
    }
    setIsSymbolDropdownOpen(!isSymbolDropdownOpen)
  }

  // 处理点击外部区域关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node

      // 检查模型下拉菜单
      const isClickInsideModelDropdown =
        (modelDropdownRef.current && modelDropdownRef.current.contains(target)) ||
        (modelPortalRef.current && modelPortalRef.current.contains(target))

      if (!isClickInsideModelDropdown) {
        setIsModelDropdownOpen(false)
        setModelDropdownPosition(null)
      }

      // 检查币种下拉菜单
      const isClickInsideSymbolDropdown =
        (symbolDropdownRef.current && symbolDropdownRef.current.contains(target)) ||
        (symbolPortalRef.current && symbolPortalRef.current.contains(target))

      if (!isClickInsideSymbolDropdown) {
        setIsSymbolDropdownOpen(false)
        setSymbolDropdownPosition(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // 组件加载时获取历史记录，当用户状态变化时也重新获取
  useEffect(() => {
    fetchHistoryRecords()
  }, [isAuthenticated, user?.has_premium_access])

  // 开始预测
  const handleStartPrediction = async () => {
    // 检查用户是否登录和有权限
    if (!isAuthenticated) {
      setShowLoginModal(true)
      return
    }

    if (!user?.has_premium_access) {
      alert('此功能需要高级会员权限。请升级您的账户以访问AI预测分析功能。\n\n您可以使用演示账户：\n- demo_monthly / demo123456\n- demo_yearly / demo123456')
      return
    }

    setIsLoading(true)
    try {
      // 根据选择的模型类型映射到API参数
      let modelType = selectedModel // 直接使用选择的模型类型

      const response = await axios.get(`${API_BASE}/api/v1/prediction/predict/${selectedSymbol}`, {
        params: {
          model_type: modelType,
          use_cache: false
        }
      })

      if (response.data.success) {
        const result = response.data.data
        // 从prediction_result中获取预测数据
        const predictionData = result.prediction_result || result
        const professionalAnalysis = result.professional_analysis || {}

        setPredictionResult({
          symbol: predictionData.symbol || selectedSymbol,
          direction: predictionData.prediction_label || predictionData.direction || '未知',
          confidence: predictionData.confidence || 0,
          predicted_price: predictionData.predicted_price,
          current_price: predictionData.current_price,
          down_prob: predictionData.down_prob,
          sideways_prob: predictionData.sideways_prob,
          up_prob: predictionData.up_prob,
          model_type: selectedModel,
          // 添加更多详细信息
          professional_analysis: professionalAnalysis,
          trading_advice: professionalAnalysis.trading_advice,
          risk_assessment: professionalAnalysis.risk_assessment,
          technical_analysis: professionalAnalysis.technical_analysis,
          enhanced_interpretation: result.enhanced_interpretation,
          current_time: predictionData.current_time,
          prediction_time: predictionData.prediction_time
        })
        setShowEdict(true)

        // 刷新历史记录
        fetchHistoryRecords()
      } else {
        alert('预测失败: ' + (response.data.message || '未知错误'))
      }
    } catch (error: any) {
      console.error('预测失败:', error)
      alert('预测失败: ' + (error.response?.data?.detail || error.message || '网络错误'))
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <div className="space-y-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex items-center space-x-3 mb-6">
          <Brain size={32} className="text-primary-400" />
          <div>
            <h1 className="text-3xl font-bold text-white">AI预测分析</h1>
            <p className="text-dark-400">基于深度学习的智能预测和专业分析</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 预测结果 */}
          <div className="card p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Target size={20} className="text-success-400" />
              <h2 className="text-xl font-semibold text-white">预测结果</h2>
            </div>
            
            {predictionResult ? (
              <div>
                <div className="text-center py-8">
                  <div className={`w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-4 ${
                    predictionResult.direction === '上涨' || predictionResult.direction === 'up' ? 'bg-success-600/20' :
                    predictionResult.direction === '下跌' || predictionResult.direction === 'down' ? 'bg-danger-600/20' :
                    'bg-warning-600/20'
                  }`}>
                    <Brain size={32} className={
                      predictionResult.direction === '上涨' || predictionResult.direction === 'up' ? 'text-success-400' :
                      predictionResult.direction === '下跌' || predictionResult.direction === 'down' ? 'text-danger-400' :
                      'text-warning-400'
                    } />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-2">
                    {predictionResult.direction === 'up' ? '上涨趋势' :
                     predictionResult.direction === 'down' ? '下跌趋势' :
                     predictionResult.direction === 'sideways' ? '横盘整理' :
                     predictionResult.direction}
                  </h3>
                  <p className={`text-lg mb-4 ${
                    predictionResult.direction === '上涨' || predictionResult.direction === 'up' ? 'text-success-400' :
                    predictionResult.direction === '下跌' || predictionResult.direction === 'down' ? 'text-danger-400' :
                    'text-warning-400'
                  }`}>
                    置信度: {(predictionResult.confidence * 100).toFixed(1)}%
                  </p>
                  <p className="text-dark-400">
                    基于当前市场数据和技术指标，AI模型预测{predictionResult.symbol}在未来1小时内的走势
                  </p>
                </div>

                <div className="grid grid-cols-3 gap-4 mt-6">
                  <div className="text-center">
                    <p className="text-danger-400 text-lg font-semibold">
                      {predictionResult.down_prob ? (predictionResult.down_prob * 100).toFixed(1) : '0.0'}%
                    </p>
                    <p className="text-dark-400 text-sm">下跌概率</p>
                  </div>
                  <div className="text-center">
                    <p className="text-warning-400 text-lg font-semibold">
                      {predictionResult.sideways_prob ? (predictionResult.sideways_prob * 100).toFixed(1) : '0.0'}%
                    </p>
                    <p className="text-dark-400 text-sm">横盘概率</p>
                  </div>
                  <div className="text-center">
                    <p className="text-success-400 text-lg font-semibold">
                      {predictionResult.up_prob ? (predictionResult.up_prob * 100).toFixed(1) : '0.0'}%
                    </p>
                    <p className="text-dark-400 text-sm">上涨概率</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-24 h-24 bg-gray-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain size={32} className="text-gray-400" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">等待预测</h3>
                <p className="text-gray-400 text-lg mb-4">请选择币种和模型后开始预测</p>
                <p className="text-dark-400">
                  选择您要分析的交易对和AI模型，然后点击"开始新预测"按钮
                </p>
              </div>
            )}
          </div>

          {/* 模型状态 */}
          <div className="card p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Zap size={20} className="text-primary-400" />
              <h2 className="text-xl font-semibold text-white">模型状态</h2>
            </div>

            {/* 币种选择器 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-dark-400 mb-2">
                选择交易对
              </label>
              <div className="relative" ref={symbolDropdownRef}>
                <button
                  onClick={toggleSymbolDropdown}
                  className="w-full bg-trading-surface border border-trading-border rounded-xl px-4 py-3 text-left text-white hover:border-neon-blue/50 transition-all duration-300 flex items-center justify-between"
                >
                  <div>
                    <div className="font-medium">
                      {SYMBOL_OPTIONS.find(opt => opt.value === selectedSymbol)?.label}
                    </div>
                    <div className="text-sm text-dark-400">
                      {SYMBOL_OPTIONS.find(opt => opt.value === selectedSymbol)?.name}
                    </div>
                  </div>
                  <ChevronDown
                    size={20}
                    className={`text-dark-400 transition-transform duration-200 ${
                      isSymbolDropdownOpen ? 'rotate-180' : ''
                    }`}
                  />
                </button>


              </div>
            </div>

            {/* 模型选择器 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-dark-400 mb-2">
                选择预测模型
              </label>
              <div className="relative" ref={modelDropdownRef}>
                <button
                  onClick={toggleModelDropdown}
                  className="w-full bg-trading-surface border border-trading-border rounded-xl px-4 py-3 text-left text-white hover:border-neon-blue/50 transition-all duration-300 flex items-center justify-between"
                >
                  <div>
                    <div className="font-medium">
                      {MODEL_OPTIONS.find(opt => opt.value === selectedModel)?.label}
                    </div>
                  </div>
                  <ChevronDown
                    size={20}
                    className={`text-dark-400 transition-transform duration-200 ${
                      isModelDropdownOpen ? 'rotate-180' : ''
                    }`}
                  />
                </button>


              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-dark-400">当前模型</span>
                <span className="badge-success">
                  {MODEL_OPTIONS.find(opt => opt.value === selectedModel)?.status}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-dark-400">数据更新</span>
                <span className="text-success-400">实时</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-dark-400">预测准确率</span>
                <span className="text-white">
                  {MODEL_OPTIONS.find(opt => opt.value === selectedModel)?.accuracy}
                </span>
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={handleStartPrediction}
                disabled={isLoading}
                className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    预测中...
                  </div>
                ) : (
                  '开始新预测'
                )}
              </button>

              {/* 权限提示 */}
              {!isAuthenticated && (
                <div className="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                  <div className="flex items-center text-yellow-400 text-sm">
                    <AlertCircle size={16} className="mr-2" />
                    需要登录才能使用AI预测功能
                  </div>
                </div>
              )}

              {isAuthenticated && !user?.has_premium_access && (
                <div className="mt-3 p-3 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                  <div className="flex items-center text-orange-400 text-sm">
                    <AlertCircle size={16} className="mr-2" />
                    此功能需要高级会员权限。可使用演示账户：demo_monthly / demo123456
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 历史预测 */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-white mb-4">历史预测记录</h2>
          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th>时间</th>
                  <th>交易对</th>
                  <th>预测方向</th>
                  <th>置信度</th>
                  <th>实际结果</th>
                  <th>准确性</th>
                </tr>
              </thead>
              <tbody>
                {historyRecords.length > 0 ? historyRecords.map((record, index) => (
                  <tr key={index}>
                    <td>{new Date(record.created_at).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}</td>
                    <td>{record.symbol}</td>
                    <td>
                      <span className={`badge ${
                        record.direction === '上涨' || record.direction === 'up' ? 'badge-success' :
                        record.direction === '下跌' || record.direction === 'down' ? 'badge-danger' : 'badge-warning'
                      }`}>
                        {record.direction === 'up' ? '上涨' :
                         record.direction === 'down' ? '下跌' :
                         record.direction === 'sideways' ? '横盘' :
                         record.direction}
                      </span>
                    </td>
                    <td>{(record.confidence * 100).toFixed(1)}%</td>
                    <td>{record.actual_result || '待验证'}</td>
                    <td>
                      <span className={record.is_correct === true ? 'text-success-400' :
                                     record.is_correct === false ? 'text-danger-400' : 'text-gray-400'}>
                        {record.is_correct === true ? '✓' :
                         record.is_correct === false ? '✗' : '-'}
                      </span>
                    </td>
                  </tr>
                )) : (
                  <tr>
                    <td colSpan={6} className="text-center text-gray-400 py-8">
                      {!isAuthenticated ? (
                        <div>
                          <div className="mb-2">需要登录才能查看历史记录</div>
                          <button
                            onClick={() => setShowLoginModal(true)}
                            className="text-blue-400 hover:text-blue-300 underline"
                          >
                            点击登录
                          </button>
                        </div>
                      ) : !user?.has_premium_access ? (
                        <div>
                          <div className="mb-2">需要高级会员权限才能查看历史记录</div>
                          <div className="text-sm text-gray-500">
                            可使用演示账户：demo_monthly / demo123456
                          </div>
                        </div>
                      ) : (
                        '暂无历史预测记录'
                      )}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </motion.div>

      {/* 圣旨弹窗 */}
      <ImperialEdict
        isOpen={showEdict}
        onClose={() => setShowEdict(false)}
        predictionResult={predictionResult}
      />

      {/* Portal下拉菜单 */}
      {isSymbolDropdownOpen && symbolDropdownPosition && createPortal(
        <motion.div
          ref={symbolPortalRef}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="fixed bg-trading-surface border border-trading-border rounded-xl shadow-xl z-[9999] overflow-hidden max-h-60 overflow-y-auto"
          style={{
            top: symbolDropdownPosition.top,
            left: symbolDropdownPosition.left,
            width: symbolDropdownPosition.width
          }}
        >
          {SYMBOL_OPTIONS.map((option) => (
            <button
              key={option.value}
              onClick={() => {
                setSelectedSymbol(option.value)
                setIsSymbolDropdownOpen(false)
                setSymbolDropdownPosition(null)
              }}
              className={`w-full px-4 py-3 text-left hover:bg-trading-border transition-colors duration-200 ${
                selectedSymbol === option.value ? 'bg-neon-blue/10 border-l-4 border-neon-blue' : ''
              }`}
            >
              <div className="font-medium text-white">{option.label}</div>
              <div className="text-sm text-dark-400">{option.name}</div>
            </button>
          ))}
        </motion.div>,
        document.body
      )}

      {isModelDropdownOpen && modelDropdownPosition && createPortal(
        <motion.div
          ref={modelPortalRef}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="fixed bg-trading-surface border border-trading-border rounded-xl shadow-xl z-[9999] overflow-hidden max-h-96 overflow-y-auto"
          style={{
            top: modelDropdownPosition.top,
            left: modelDropdownPosition.left,
            width: modelDropdownPosition.width
          }}
        >
          <div className="pb-2">
            {MODEL_OPTIONS.map((option) => (
              <button
                key={option.value}
                onClick={() => {
                  setSelectedModel(option.value)
                  setIsModelDropdownOpen(false)
                  setModelDropdownPosition(null)
                }}
                className={`w-full px-4 py-3 text-left hover:bg-trading-border transition-colors duration-200 ${
                  selectedModel === option.value ? 'bg-neon-blue/10 border-l-4 border-neon-blue' : ''
                }`}
              >
                <div className="font-medium text-white">{option.label}</div>
                <div className="text-xs text-success-400 mt-1">
                  准确率: {option.accuracy} • {option.status}
                </div>
              </button>
            ))}
          </div>
        </motion.div>,
        document.body
      )}

      {/* 登录模态框 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
      />
    </div>
  )
}

export default PredictionPage
