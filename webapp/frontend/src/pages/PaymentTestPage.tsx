/**
 * 支付功能测试页面
 */

import React, { useState } from 'react'
import { CreditCard, Smartphone, QrCode } from 'lucide-react'
import api from '../services/api'

interface PaymentOrder {
  success: boolean
  order_no: string
  amount: number
  payment_method: string
  qr_code_url: string
  account_name: string
  account_info: string
  payment_reference: string
  expires_at: string
  instructions: string[]
  error?: string
}

export const PaymentTestPage: React.FC = () => {
  const [paymentOrder, setPaymentOrder] = useState<PaymentOrder | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createPayment = async (productType: string, paymentMethod: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      console.log('🚀 创建支付订单:', { productType, paymentMethod })
      
      const response = await api.post('/api/v1/personal-payment/create', {
        product_type: productType,
        payment_method: paymentMethod
      })
      
      console.log('✅ 支付订单创建成功:', response.data)
      setPaymentOrder(response.data)
      
    } catch (err: any) {
      console.error('❌ 创建支付订单失败:', err)
      setError(err.response?.data?.detail || err.message || '创建支付订单失败')
    } finally {
      setIsLoading(false)
    }
  }

  const testImageLoad = (url: string) => {
    console.log('🖼️ 测试图片加载:', url)
    
    const img = new Image()
    img.onload = () => {
      console.log('✅ 图片加载成功:', url)
    }
    img.onerror = (err) => {
      console.error('❌ 图片加载失败:', url, err)
    }
    img.src = url
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            个人支付功能测试
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            测试个人收款码支付系统的各项功能
          </p>
        </div>

        {/* 测试按钮 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            创建支付订单测试
          </h2>
          
          <div className="grid md:grid-cols-2 gap-4 mb-6">
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-3">微信支付</h3>
              <div className="space-y-2">
                <button
                  onClick={() => createPayment('monthly', 'wechat')}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
                >
                  <Smartphone className="w-4 h-4" />
                  微信月卡 ¥29.99
                </button>
                <button
                  onClick={() => createPayment('quarterly', 'wechat')}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
                >
                  <Smartphone className="w-4 h-4" />
                  微信季卡 ¥79.99
                </button>
              </div>
            </div>

            <div>
              <h3 className="font-medium text-gray-900 dark:text-white mb-3">支付宝</h3>
              <div className="space-y-2">
                <button
                  onClick={() => createPayment('monthly', 'alipay')}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
                >
                  <CreditCard className="w-4 h-4" />
                  支付宝月卡 ¥29.99
                </button>
                <button
                  onClick={() => createPayment('quarterly', 'alipay')}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors disabled:opacity-50"
                >
                  <CreditCard className="w-4 h-4" />
                  支付宝季卡 ¥79.99
                </button>
              </div>
            </div>
          </div>

          {isLoading && (
            <div className="text-center py-4">
              <div className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                创建支付订单中...
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
              <p className="text-red-800 dark:text-red-200">❌ {error}</p>
            </div>
          )}
        </div>

        {/* 支付订单结果 */}
        {paymentOrder && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <QrCode className="w-5 h-5" />
              支付订单详情
            </h2>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white mb-3">订单信息</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">订单号:</span>
                    <span className="font-mono text-gray-900 dark:text-white">{paymentOrder.order_no}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">金额:</span>
                    <span className="font-semibold text-green-600 dark:text-green-400">¥{paymentOrder.amount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">支付方式:</span>
                    <span className="text-gray-900 dark:text-white">{paymentOrder.payment_method === 'wechat' ? '微信' : '支付宝'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">收款人:</span>
                    <span className="text-gray-900 dark:text-white">{paymentOrder.account_name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">付款备注:</span>
                    <span className="font-mono font-semibold text-red-600 dark:text-red-400">{paymentOrder.payment_reference}</span>
                  </div>
                </div>

                <div className="mt-4">
                  <button
                    onClick={() => testImageLoad(paymentOrder.qr_code_url)}
                    className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
                  >
                    测试图片加载
                  </button>
                </div>
              </div>

              <div>
                <h3 className="font-medium text-gray-900 dark:text-white mb-3">收款码</h3>
                <div className="text-center">
                  <div className="inline-block p-4 bg-white rounded-lg shadow-sm border">
                    <img
                      src={paymentOrder.qr_code_url}
                      alt="收款码"
                      className="w-48 h-48 object-contain"
                      onLoad={() => console.log('✅ 收款码图片加载成功')}
                      onError={(e) => {
                        console.error('❌ 收款码图片加载失败:', paymentOrder.qr_code_url)
                        e.currentTarget.src = 'https://via.placeholder.com/192x192?text=加载失败'
                      }}
                    />
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                    {paymentOrder.account_info}
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="font-medium text-gray-900 dark:text-white mb-3">支付说明</h3>
              <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                {paymentOrder.instructions.map((instruction, index) => (
                  <li key={index}>{instruction}</li>
                ))}
              </ol>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
