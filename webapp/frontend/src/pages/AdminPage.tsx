/**
 * 管理员后台页面 - 重新设计版本
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Users,
  Shield,
  BarChart3,
  Settings,
  Crown,
  AlertCircle,
  CheckCircle,
  XCircle,
  Calendar,
  DollarSign,
  TrendingUp,
  UserCheck,
  UserX,
  CreditCard,
  QrCode,
  FileText,
  Activity,
  RefreshCw,
  Eye,
  Filter,
  Download,
  Bell,
  Zap
} from 'lucide-react'
import { useAuthStore } from '../stores/authStore'
import { api } from '../services/api'

// 接口定义
interface UserStats {
  total_users: number
  active_users: number
  role_distribution: Record<string, number>
  active_subscriptions: number
}

interface User {
  id: number
  username: string
  email: string
  full_name?: string
  role: string
  is_active: boolean
  is_verified: boolean
  created_at: string
  last_login?: string
  has_premium_access: boolean
  current_subscription?: {
    subscription_type: string
    status: string
    expires_at: string
    days_remaining: number
  }
}

interface PaymentOverviewStats {
  today: {
    date: string
    total_payments: number
    total_amount: number
    unique_users: number
    payment_methods: Record<string, number>
    product_types: Record<string, number>
  }
  this_month: {
    month: string
    total_payments: number
    total_amount: number
    unique_users: number
  }
  payment_trends: Array<{
    date: string
    amount: number
    count: number
  }>
  qr_code_usage: Array<{
    payment_method: string
    qr_code_info: string
    usage_count: number
    total_amount: number
    last_used?: string
  }>
  top_products: Record<string, number>
}

interface DashboardData {
  userStats: UserStats
  paymentStats: PaymentOverviewStats
  recentActivity: Array<{
    id: string
    type: 'payment' | 'user' | 'system'
    message: string
    timestamp: string
    status: 'success' | 'warning' | 'error'
  }>
}

export const AdminPage: React.FC = () => {
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('dashboard')
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  // 检查管理员权限
  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Shield className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            访问被拒绝
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            您需要管理员权限才能访问此页面
          </p>
        </div>
      </div>
    )
  }

  // 获取仪表板数据
  const fetchDashboardData = async () => {
    try {
      const [userStatsRes, paymentStatsRes] = await Promise.all([
        api.get('/api/v1/auth/admin/stats'),
        api.get('/api/v1/admin/stats/overview')
      ])

      // 从API获取最近活动数据
      const recentActivity: Array<{
        id: string
        type: 'payment' | 'user' | 'system'
        message: string
        timestamp: string
        status: 'success' | 'warning' | 'error'
      }> = []

      setDashboardData({
        userStats: userStatsRes.data,
        paymentStats: paymentStatsRes.data,
        recentActivity
      })
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
      setError('获取仪表板数据失败')
    }
  }

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const response = await api.get('/api/v1/auth/admin/users')
      setUsers(response.data)
    } catch (error) {
      console.error('获取用户列表失败:', error)
      setError('获取用户列表失败')
    }
  }

  // 更新用户状态
  const updateUserStatus = async (userId: number, isActive: boolean) => {
    try {
      await api.put(`/api/v1/auth/admin/users/${userId}/status`, null, {
        params: { is_active: isActive }
      })
      await fetchUsers()
    } catch (error) {
      console.error('更新用户状态失败:', error)
    }
  }

  // 更新用户角色
  const updateUserRole = async (userId: number, newRole: string) => {
    try {
      await api.put(`/api/v1/auth/admin/users/${userId}/role`, null, {
        params: { new_role: newRole }
      })
      await fetchUsers()
    } catch (error) {
      console.error('更新用户角色失败:', error)
    }
  }

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      if (activeTab === 'dashboard') {
        await fetchDashboardData()
      } else if (activeTab === 'users') {
        await fetchUsers()
      }
    } finally {
      setRefreshing(false)
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      try {
        if (activeTab === 'dashboard') {
          await fetchDashboardData()
        } else if (activeTab === 'users') {
          await fetchUsers()
        }
      } finally {
        setLoading(false)
      }
    }
    loadData()
  }, [activeTab])

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="w-4 h-4 text-red-500" />
      case 'yearly':
        return <Crown className="w-4 h-4 text-yellow-500" />
      case 'quarterly':
        return <Crown className="w-4 h-4 text-blue-500" />
      case 'monthly':
        return <Crown className="w-4 h-4 text-green-500" />
      default:
        return <Users className="w-4 h-4 text-gray-500" />
    }
  }

  const getRoleText = (role: string) => {
    const roleMap: Record<string, string> = {
      admin: '管理员',
      yearly: '年卡会员',
      quarterly: '季卡会员',
      monthly: '月卡会员',
      free: '免费用户'
    }
    return roleMap[role] || role
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                管理员后台
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                全面的用户管理和付费统计系统
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                刷新
              </button>
              <div className="flex items-center gap-2 px-3 py-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded-lg">
                <Activity className="w-4 h-4" />
                <span className="text-sm font-medium">系统正常</span>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <span className="text-red-600 dark:text-red-400">{error}</span>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {[
                { id: 'dashboard', name: '仪表板', icon: BarChart3, description: '总览和关键指标' },
                { id: 'payments', name: '付费管理', icon: CreditCard, description: '付费统计和记录' },
                { id: 'users', name: '用户管理', icon: Users, description: '用户列表和权限' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-3 px-4 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                  title={tab.description}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'dashboard' && dashboardData && (
            <div className="space-y-8">
              {/* 关键指标卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* 今日付费 */}
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm font-medium">今日付费</p>
                      <p className="text-3xl font-bold">¥{dashboardData.paymentStats.today.total_amount.toFixed(2)}</p>
                      <p className="text-blue-100 text-sm">{dashboardData.paymentStats.today.total_payments} 笔订单</p>
                    </div>
                    <div className="p-3 bg-blue-400/30 rounded-lg">
                      <DollarSign className="w-8 h-8" />
                    </div>
                  </div>
                </div>

                {/* 本月付费 */}
                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm font-medium">本月付费</p>
                      <p className="text-3xl font-bold">¥{dashboardData.paymentStats.this_month.total_amount.toFixed(2)}</p>
                      <p className="text-green-100 text-sm">{dashboardData.paymentStats.this_month.total_payments} 笔订单</p>
                    </div>
                    <div className="p-3 bg-green-400/30 rounded-lg">
                      <TrendingUp className="w-8 h-8" />
                    </div>
                  </div>
                </div>

                {/* 总用户数 */}
                <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm font-medium">总用户数</p>
                      <p className="text-3xl font-bold">{dashboardData.userStats.total_users}</p>
                      <p className="text-purple-100 text-sm">{dashboardData.userStats.active_users} 活跃用户</p>
                    </div>
                    <div className="p-3 bg-purple-400/30 rounded-lg">
                      <Users className="w-8 h-8" />
                    </div>
                  </div>
                </div>

                {/* 付费用户 */}
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-100 text-sm font-medium">付费用户</p>
                      <p className="text-3xl font-bold">
                        {(dashboardData.userStats.role_distribution.monthly || 0) +
                         (dashboardData.userStats.role_distribution.quarterly || 0) +
                         (dashboardData.userStats.role_distribution.yearly || 0)}
                      </p>
                      <p className="text-orange-100 text-sm">{dashboardData.userStats.active_subscriptions} 活跃订阅</p>
                    </div>
                    <div className="p-3 bg-orange-400/30 rounded-lg">
                      <Crown className="w-8 h-8" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 详细统计面板 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 付费方式统计 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      今日付费方式分布
                    </h3>
                    <CreditCard className="w-5 h-5 text-gray-400" />
                  </div>
                  <div className="space-y-4">
                    {Object.entries(dashboardData.paymentStats.today.payment_methods).map(([method, count]) => (
                      <div key={method} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${method === 'wechat' ? 'bg-green-500' : 'bg-blue-500'}`}></div>
                          <span className="text-gray-700 dark:text-gray-300">
                            {method === 'wechat' ? '微信支付' : '支付宝'}
                          </span>
                        </div>
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {count} 笔
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 产品类型统计 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      热门产品
                    </h3>
                    <BarChart3 className="w-5 h-5 text-gray-400" />
                  </div>
                  <div className="space-y-4">
                    {Object.entries(dashboardData.paymentStats.top_products)
                      .sort(([,a], [,b]) => b - a)
                      .slice(0, 3)
                      .map(([product, count]) => (
                      <div key={product} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Crown className={`w-4 h-4 ${
                            product === 'yearly' ? 'text-yellow-500' :
                            product === 'quarterly' ? 'text-blue-500' : 'text-green-500'
                          }`} />
                          <span className="text-gray-700 dark:text-gray-300">
                            {product === 'yearly' ? '年卡会员' :
                             product === 'quarterly' ? '季卡会员' : '月卡会员'}
                          </span>
                        </div>
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {count}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 收款码使用统计 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    收款码使用统计（最近30天）
                  </h3>
                  <QrCode className="w-5 h-5 text-gray-400" />
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">支付方式</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">收款码信息</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">使用次数</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">总金额</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">最后使用</th>
                      </tr>
                    </thead>
                    <tbody>
                      {dashboardData.paymentStats.qr_code_usage.slice(0, 5).map((qr, index) => (
                        <tr key={index} className="border-b border-gray-100 dark:border-gray-700">
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              qr.payment_method === 'wechat'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                                : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200'
                            }`}>
                              {qr.payment_method === 'wechat' ? '微信' : '支付宝'}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-gray-900 dark:text-white">
                            {qr.qr_code_info.length > 30 ? `${qr.qr_code_info.substring(0, 30)}...` : qr.qr_code_info}
                          </td>
                          <td className="py-3 px-4 font-semibold text-gray-900 dark:text-white">
                            {qr.usage_count}
                          </td>
                          <td className="py-3 px-4 font-semibold text-green-600 dark:text-green-400">
                            ¥{qr.total_amount.toFixed(2)}
                          </td>
                          <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                            {qr.last_used ? new Date(qr.last_used).toLocaleDateString() : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* 最近活动 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    最近活动
                  </h3>
                  <Bell className="w-5 h-5 text-gray-400" />
                </div>
                <div className="space-y-4">
                  {dashboardData.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.status === 'success' ? 'bg-green-500' :
                        activity.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                      <div className="flex-1">
                        <p className="text-gray-900 dark:text-white text-sm">
                          {activity.message}
                        </p>
                        <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="space-y-8">
              {/* 付费概览卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm font-medium">今日收入</p>
                      <p className="text-3xl font-bold">¥{dashboardData.paymentStats.today.total_amount.toFixed(2)}</p>
                      <p className="text-green-100 text-sm">{dashboardData.paymentStats.today.total_payments} 笔订单</p>
                    </div>
                    <div className="p-3 bg-green-400/30 rounded-lg">
                      <DollarSign className="w-8 h-8" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm font-medium">本月收入</p>
                      <p className="text-3xl font-bold">¥{dashboardData.paymentStats.this_month.total_amount.toFixed(2)}</p>
                      <p className="text-blue-100 text-sm">{dashboardData.paymentStats.this_month.total_payments} 笔订单</p>
                    </div>
                    <div className="p-3 bg-blue-400/30 rounded-lg">
                      <TrendingUp className="w-8 h-8" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm font-medium">付费用户</p>
                      <p className="text-3xl font-bold">{dashboardData.paymentStats.this_month.unique_users}</p>
                      <p className="text-purple-100 text-sm">本月新增</p>
                    </div>
                    <div className="p-3 bg-purple-400/30 rounded-lg">
                      <Users className="w-8 h-8" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 快速操作 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  付费管理操作
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <button
                    onClick={() => window.open('/admin/payments/records', '_blank')}
                    className="flex items-center gap-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <CreditCard className="w-6 h-6 text-blue-600" />
                    <div className="text-left">
                      <div className="font-medium text-gray-900 dark:text-white">付费记录</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">查看所有付费记录</div>
                    </div>
                  </button>

                  <button
                    onClick={() => window.open('/admin/payments/statistics', '_blank')}
                    className="flex items-center gap-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <BarChart3 className="w-6 h-6 text-green-600" />
                    <div className="text-left">
                      <div className="font-medium text-gray-900 dark:text-white">付费统计</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">详细数据分析</div>
                    </div>
                  </button>

                  <button
                    onClick={() => window.open('/admin/analytics/visitors', '_blank')}
                    className="flex items-center gap-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Activity className="w-6 h-6 text-purple-600" />
                    <div className="text-left">
                      <div className="font-medium text-gray-900 dark:text-white">访问统计</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">用户访问分析</div>
                    </div>
                  </button>

                  <button
                    onClick={() => window.open('/admin/analytics/qr-codes', '_blank')}
                    className="flex items-center gap-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <QrCode className="w-6 h-6 text-orange-600" />
                    <div className="text-left">
                      <div className="font-medium text-gray-900 dark:text-white">收款码统计</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">收款码收入分析</div>
                    </div>
                  </button>
                </div>
              </div>

              {/* 最近付费记录 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    最近付费记录
                  </h3>
                  <button
                    onClick={() => window.open('/admin/payments/records', '_blank')}
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium"
                  >
                    查看全部
                  </button>
                </div>
                <div className="space-y-4">
                  {/* 最近付费记录 - 从API获取 */}
                  {dashboardData.paymentStats.recent_payments?.length > 0 ? (
                    dashboardData.paymentStats.recent_payments.map((record, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                            <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">{record.username}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">{record.product_type} - {record.payment_method}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold text-green-600 dark:text-green-400">¥{record.amount}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {new Date(record.created_at).toLocaleString()}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">暂无最近付费记录</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}



          {activeTab === 'users' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    用户列表
                  </h3>
                  <div className="flex items-center gap-3">
                    <button className="flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
                      <Filter className="w-4 h-4" />
                      筛选
                    </button>
                    <button className="flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
                      <Download className="w-4 h-4" />
                      导出
                    </button>
                  </div>
                </div>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        用户
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        角色
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        注册时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {users.map((user) => (
                      <tr key={user.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {user.full_name || user.username}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {user.email}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-2">
                            {getRoleIcon(user.role)}
                            <span className="text-sm text-gray-900 dark:text-white">
                              {getRoleText(user.role)}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-2">
                            {user.is_active ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-500" />
                            )}
                            <span className={`text-sm ${
                              user.is_active 
                                ? 'text-green-600 dark:text-green-400' 
                                : 'text-red-600 dark:text-red-400'
                            }`}>
                              {user.is_active ? '活跃' : '禁用'}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {new Date(user.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => updateUserStatus(user.id, !user.is_active)}
                              className={`px-3 py-1 rounded text-xs ${
                                user.is_active
                                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                  : 'bg-green-100 text-green-700 hover:bg-green-200'
                              }`}
                            >
                              {user.is_active ? '禁用' : '启用'}
                            </button>
                            <select
                              value={user.role}
                              onChange={(e) => updateUserRole(user.id, e.target.value)}
                              className="text-xs border border-gray-300 rounded px-2 py-1 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                              <option value="free">免费用户</option>
                              <option value="monthly">月卡会员</option>
                              <option value="quarterly">季卡会员</option>
                              <option value="yearly">年卡会员</option>
                              <option value="admin">管理员</option>
                            </select>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default AdminPage
