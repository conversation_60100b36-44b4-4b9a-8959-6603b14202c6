/**
 * 付费记录管理页面
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  RefreshCw,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react'
import { api } from '../../services/api'

// 接口定义
interface PaymentRecord {
  id: number
  order_no: string
  user_id: number
  username: string
  user_email: string
  product_type: string
  amount: number
  currency: string
  payment_method: string
  payment_status: string
  user_note?: string
  admin_note?: string
  created_at: string
  submitted_at?: string
  paid_at?: string
  expires_at?: string
  confirmed_by_username?: string
  activation_codes: string[]
}

interface PaymentSummary {
  total_records: number
  total_amount: number
  status_breakdown: Record<string, number>
  method_breakdown: Record<string, number>
  product_breakdown: Record<string, number>
}

interface PaymentRecordsResponse {
  records: PaymentRecord[]
  summary: PaymentSummary
  total_pages: number
  current_page: number
}

const PaymentRecordsPage: React.FC = () => {
  const [data, setData] = useState<PaymentRecordsResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 筛选和分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [methodFilter, setMethodFilter] = useState('')
  const [productFilter, setProductFilter] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  // 编辑备注状态
  const [editingNote, setEditingNote] = useState<string | null>(null)
  const [noteText, setNoteText] = useState('')

  // 获取付费记录
  const fetchRecords = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        page_size: pageSize.toString()
      })

      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter) params.append('payment_status', statusFilter)
      if (methodFilter) params.append('payment_method', methodFilter)
      if (productFilter) params.append('product_type', productFilter)
      if (startDate) params.append('start_date', startDate)
      if (endDate) params.append('end_date', endDate)

      const response = await api.get(`/api/v1/admin/payments/records?${params}`)
      setData(response.data)
      setError(null)
    } catch (error) {
      console.error('获取付费记录失败:', error)
      setError('获取付费记录失败')
      // 数据库查询失败时设置空数据
      setData({
        records: [],
        summary: {
          total_records: 0,
          total_amount: 0,
          status_breakdown: {},
          method_breakdown: {},
          product_breakdown: {}
        },
        total_pages: 0,
        current_page: 1
      })
    }
  }

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchRecords()
    setRefreshing(false)
  }

  // 更新备注
  const handleUpdateNote = async (orderNo: string, note: string) => {
    try {
      await api.put('/api/v1/admin/payments/notes', {
        order_no: orderNo,
        admin_note: note
      })
      setEditingNote(null)
      setNoteText('')
      await fetchRecords()
    } catch (error) {
      console.error('更新备注失败:', error)
      alert('更新备注失败')
    }
  }

  // 重置筛选
  const handleResetFilters = () => {
    setSearchTerm('')
    setStatusFilter('')
    setMethodFilter('')
    setProductFilter('')
    setStartDate('')
    setEndDate('')
    setCurrentPage(1)
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await fetchRecords()
      setLoading(false)
    }
    loadData()
  }, [currentPage, searchTerm, statusFilter, methodFilter, productFilter, startDate, endDate])

  // 状态显示组件
  const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    const getStatusConfig = (status: string) => {
      switch (status) {
        case 'paid':
          return { color: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200', icon: CheckCircle, text: '已支付' }
        case 'pending':
          return { color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200', icon: Clock, text: '待支付' }
        case 'submitted':
          return { color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200', icon: AlertCircle, text: '已提交' }
        case 'cancelled':
          return { color: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200', icon: XCircle, text: '已取消' }
        default:
          return { color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-200', icon: Clock, text: status }
      }
    }

    const config = getStatusConfig(status)
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {config.text}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载付费记录中...</p>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <CreditCard className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            数据加载失败
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error || '无法获取付费记录'}
          </p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                付费记录管理
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                查看和管理所有付费记录，包括备注信息和收款码使用情况
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                刷新
              </button>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <Download className="w-4 h-4" />
                导出
              </button>
            </div>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <CreditCard className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">总记录数</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {data.summary.total_records}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">总金额</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  ¥{data.summary.total_amount.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已支付</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {data.summary.status_breakdown.paid || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <AlertCircle className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">待处理</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {(data.summary.status_breakdown.pending || 0) + (data.summary.status_breakdown.submitted || 0)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 筛选和搜索 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                搜索
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="用户名、邮箱或订单号"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                支付状态
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">全部状态</option>
                <option value="pending">待支付</option>
                <option value="submitted">已提交</option>
                <option value="paid">已支付</option>
                <option value="cancelled">已取消</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={handleResetFilters}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                重置筛选
              </button>
            </div>
          </div>
        </div>

        {/* 付费记录表格 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              付费记录 ({data.records.length})
            </h3>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    订单信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    用户
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    产品/金额
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    支付方式
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    备注
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {data.records.map((record) => (
                  <tr key={record.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {record.order_no}
                        </div>
                        {record.activation_codes.length > 0 && (
                          <div className="text-xs text-green-600 dark:text-green-400">
                            激活码: {record.activation_codes[0]}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {record.username}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {record.user_email}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {record.product_type === 'monthly' && '月卡会员'}
                          {record.product_type === 'quarterly' && '季卡会员'}
                          {record.product_type === 'yearly' && '年卡会员'}
                        </div>
                        <div className="text-sm font-semibold text-green-600 dark:text-green-400">
                          ¥{record.amount}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        record.payment_method === 'wechat'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200'
                      }`}>
                        {record.payment_method === 'wechat' ? '微信支付' : '支付宝'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge status={record.payment_status} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div>
                        <div>创建: {new Date(record.created_at).toLocaleDateString()}</div>
                        {record.paid_at && (
                          <div className="text-green-600 dark:text-green-400">
                            支付: {new Date(record.paid_at).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="max-w-xs">
                        {record.user_note && (
                          <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                            用户: {record.user_note.length > 30 ? `${record.user_note.substring(0, 30)}...` : record.user_note}
                          </div>
                        )}
                        {editingNote === record.order_no ? (
                          <div className="space-y-2">
                            <textarea
                              value={noteText}
                              onChange={(e) => setNoteText(e.target.value)}
                              placeholder="管理员备注..."
                              className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                              rows={2}
                            />
                            <div className="flex gap-1">
                              <button
                                onClick={() => handleUpdateNote(record.order_no, noteText)}
                                className="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                              >
                                保存
                              </button>
                              <button
                                onClick={() => {
                                  setEditingNote(null)
                                  setNoteText('')
                                }}
                                className="px-2 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700"
                              >
                                取消
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="text-xs text-gray-600 dark:text-gray-400">
                            {record.admin_note ? (
                              <span>管理员: {record.admin_note.length > 30 ? `${record.admin_note.substring(0, 30)}...` : record.admin_note}</span>
                            ) : (
                              <span className="text-gray-400">无管理员备注</span>
                            )}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => {
                            setEditingNote(record.order_no)
                            setNoteText(record.admin_note || '')
                          }}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          title="编辑备注"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                          title="查看详情"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {data.records.length === 0 && (
            <div className="text-center py-12">
              <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                {searchTerm || statusFilter
                  ? '没有找到符合条件的付费记录'
                  : '暂无付费记录'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default PaymentRecordsPage