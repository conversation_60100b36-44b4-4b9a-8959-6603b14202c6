/**
 * 付费统计页面
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Calendar,
  Users,
  CreditCard,
  RefreshCw,
  Download,
  Filter,
  Eye,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import { api } from '../../services/api'

// 接口定义
interface PaymentTrendData {
  date: string
  amount: number
  count: number
}

interface DailyPaymentStats {
  date: string
  total_payments: number
  total_amount: number
  unique_users: number
  payment_methods: Record<string, number>
  product_types: Record<string, number>
}

interface MonthlyPaymentStats {
  month: string
  total_payments: number
  total_amount: number
  unique_users: number
  daily_breakdown: DailyPaymentStats[]
}

interface QRCodeUsageStats {
  payment_method: string
  qr_code_info: string
  usage_count: number
  total_amount: number
  last_used?: string
}

interface PaymentOverviewStats {
  today: DailyPaymentStats
  this_month: MonthlyPaymentStats
  payment_trends: PaymentTrendData[]
  qr_code_usage: QRCodeUsageStats[]
  top_products: Record<string, number>
}

const PaymentStatsPage: React.FC = () => {
  const [stats, setStats] = useState<PaymentOverviewStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState('30') // 默认30天
  const [viewMode, setViewMode] = useState<'overview' | 'trends' | 'methods' | 'products'>('overview')

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const response = await api.get('/api/v1/admin/stats/overview')
      setStats(response.data)
      setError(null)
    } catch (error) {
      console.error('获取付费统计失败:', error)
      setError('获取付费统计失败')
    }
  }

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchStats()
    setRefreshing(false)
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await fetchStats()
      setLoading(false)
    }
    loadData()
  }, [])

  // 计算增长率
  const calculateGrowthRate = (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载统计数据中...</p>
        </div>
      </div>
    )
  }

  if (error || !stats) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <BarChart3 className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            数据加载失败
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error || '无法获取统计数据'}
          </p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                付费统计分析
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                详细的付费数据分析和趋势报告
              </p>
            </div>
            <div className="flex items-center gap-3">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="7">最近7天</option>
                <option value="30">最近30天</option>
                <option value="90">最近90天</option>
              </select>
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                刷新
              </button>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <Download className="w-4 h-4" />
                导出
              </button>
            </div>
          </div>
        </div>

        {/* 视图切换 */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', name: '概览', icon: BarChart3 },
                { id: 'trends', name: '趋势分析', icon: TrendingUp },
                { id: 'methods', name: '支付方式', icon: CreditCard },
                { id: 'products', name: '产品分析', icon: Users }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setViewMode(tab.id as any)}
                  className={`flex items-center gap-2 py-3 px-4 border-b-2 font-medium text-sm transition-colors ${
                    viewMode === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 内容区域 */}
        <motion.div
          key={viewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {viewMode === 'overview' && (
            <div className="space-y-8">
              {/* 关键指标卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* 今日收入 */}
                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm font-medium">今日收入</p>
                      <p className="text-3xl font-bold">¥{stats.today.total_amount.toFixed(2)}</p>
                      <div className="flex items-center gap-1 mt-1">
                        <ArrowUp className="w-4 h-4" />
                        <span className="text-green-100 text-sm">+12.5%</span>
                      </div>
                    </div>
                    <div className="p-3 bg-green-400/30 rounded-lg">
                      <DollarSign className="w-8 h-8" />
                    </div>
                  </div>
                </div>

                {/* 今日订单 */}
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm font-medium">今日订单</p>
                      <p className="text-3xl font-bold">{stats.today.total_payments}</p>
                      <div className="flex items-center gap-1 mt-1">
                        <ArrowUp className="w-4 h-4" />
                        <span className="text-blue-100 text-sm">+8.3%</span>
                      </div>
                    </div>
                    <div className="p-3 bg-blue-400/30 rounded-lg">
                      <BarChart3 className="w-8 h-8" />
                    </div>
                  </div>
                </div>

                {/* 本月收入 */}
                <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm font-medium">本月收入</p>
                      <p className="text-3xl font-bold">¥{stats.this_month.total_amount.toFixed(2)}</p>
                      <div className="flex items-center gap-1 mt-1">
                        <ArrowUp className="w-4 h-4" />
                        <span className="text-purple-100 text-sm">+15.7%</span>
                      </div>
                    </div>
                    <div className="p-3 bg-purple-400/30 rounded-lg">
                      <TrendingUp className="w-8 h-8" />
                    </div>
                  </div>
                </div>

                {/* 付费用户 */}
                <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-100 text-sm font-medium">本月付费用户</p>
                      <p className="text-3xl font-bold">{stats.this_month.unique_users}</p>
                      <div className="flex items-center gap-1 mt-1">
                        <ArrowUp className="w-4 h-4" />
                        <span className="text-orange-100 text-sm">+22.1%</span>
                      </div>
                    </div>
                    <div className="p-3 bg-orange-400/30 rounded-lg">
                      <Users className="w-8 h-8" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 收款码使用统计 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    收款码使用排行
                  </h3>
                  <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium">
                    查看全部
                  </button>
                </div>
                <div className="space-y-4">
                  {stats.qr_code_usage.slice(0, 5).map((qr, index) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">
                            {index + 1}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {qr.payment_method === 'wechat' ? '微信支付' : '支付宝'}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {qr.qr_code_info.length > 40 ? `${qr.qr_code_info.substring(0, 40)}...` : qr.qr_code_info}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900 dark:text-white">
                          {qr.usage_count} 次
                        </p>
                        <p className="text-sm text-green-600 dark:text-green-400">
                          ¥{qr.total_amount.toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 其他视图模式的占位符 */}
          {viewMode !== 'overview' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12">
              <div className="text-center">
                <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {viewMode === 'trends' && '趋势分析'}
                  {viewMode === 'methods' && '支付方式分析'}
                  {viewMode === 'products' && '产品分析'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  详细的{viewMode === 'trends' && '趋势分析'}
                  {viewMode === 'methods' && '支付方式分析'}
                  {viewMode === 'products' && '产品分析'}功能正在开发中...
                </p>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default PaymentStatsPage
