/**
 * 收款码详细统计页面
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  QrCode,
  DollarSign,
  TrendingUp,
  Calendar,
  Users,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Download,
  ArrowLeft,
  BarChart3
} from 'lucide-react'
import { api } from '../../services/api'
import { useParams, useNavigate } from 'react-router-dom'

// 接口定义
interface QRCodePaymentRecord {
  id: number
  order_no: string
  qr_code_id: string
  qr_code_name: string
  user_id: number
  username: string
  amount: number
  payment_status: string
  created_at: string
  paid_at?: string
  user_note?: string
}

interface QRCodeSummary {
  total_qr_codes: number
  total_payments: number
  total_amount: number
  average_success_rate: number
}

interface QRCodeStats {
  qr_code_id: string
  qr_code_name: string
  total_payments: number
  total_amount: number
  success_rate: number
  last_used?: string
  daily_stats: Array<{
    date: string
    payments: number
    amount: number
  }>
}

const QRCodeAnalyticsPage: React.FC = () => {
  const { qrCodeId } = useParams<{ qrCodeId?: string }>()
  const navigate = useNavigate()
  
  const [summary, setSummary] = useState<QRCodeSummary | null>(null)
  const [qrCodeStats, setQRCodeStats] = useState<QRCodeStats[]>([])
  const [selectedQRCode, setSelectedQRCode] = useState<string | null>(qrCodeId || null)
  const [paymentRecords, setPaymentRecords] = useState<QRCodePaymentRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [pageSize] = useState(20)

  // 获取收款码汇总统计
  const fetchQRCodeSummary = async () => {
    try {
      const response = await api.get('/api/v1/admin/analytics/qr-codes/summary')
      setSummary(response.data.summary)
      setQRCodeStats(response.data.qr_code_stats)
      setError(null)
    } catch (error) {
      console.error('获取收款码统计失败:', error)
      setError('获取收款码统计失败')
      // 数据库查询失败时设置空数据
      setSummary({
        total_qr_codes: 0,
        total_payments: 0,
        total_amount: 0,
        average_success_rate: 0
      })
      setQRCodeStats([])
    }
  }

  // 获取特定收款码的付费记录
  const fetchQRCodePayments = async (qrCodeId: string) => {
    try {
      const response = await api.get(`/api/v1/admin/analytics/qr-codes/${qrCodeId}/payments`, {
        params: {
          page: currentPage,
          page_size: pageSize
        }
      })
      setPaymentRecords(response.data.records)
      setTotalPages(response.data.total_pages)
      setError(null)
    } catch (error) {
      console.error('获取收款码付费记录失败:', error)
      setError('获取收款码付费记录失败')
      // 数据库查询失败时设置空数据
      setPaymentRecords([])
      setTotalPages(0)
    }
  }

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchQRCodeSummary()
    if (selectedQRCode) {
      await fetchQRCodePayments(selectedQRCode)
    }
    setRefreshing(false)
  }

  // 选择收款码
  const handleSelectQRCode = (qrCodeId: string) => {
    setSelectedQRCode(qrCodeId)
    setCurrentPage(1)
  }

  // 状态显示组件
  const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    const getStatusConfig = (status: string) => {
      switch (status) {
        case 'paid':
          return { color: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200', icon: CheckCircle, text: '已支付' }
        case 'pending':
          return { color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200', icon: Clock, text: '待支付' }
        case 'cancelled':
          return { color: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200', icon: XCircle, text: '已取消' }
        default:
          return { color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-200', icon: Clock, text: status }
      }
    }

    const config = getStatusConfig(status)
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {config.text}
      </span>
    )
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await fetchQRCodeSummary()
      setLoading(false)
    }
    loadData()
  }, [])

  useEffect(() => {
    if (selectedQRCode) {
      fetchQRCodePayments(selectedQRCode)
    }
  }, [selectedQRCode, currentPage])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载收款码统计中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admin')}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  收款码统计分析
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  查看各收款码的使用情况、收入统计和付费记录
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                刷新
              </button>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <Download className="w-4 h-4" />
                导出
              </button>
            </div>
          </div>
        </div>

        {/* 汇总统计卡片 */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm font-medium">收款码总数</p>
                  <p className="text-3xl font-bold">{summary.total_qr_codes}</p>
                </div>
                <QrCode className="w-8 h-8 text-blue-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm font-medium">总支付次数</p>
                  <p className="text-3xl font-bold">{summary.total_payments}</p>
                </div>
                <Users className="w-8 h-8 text-green-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm font-medium">总收入金额</p>
                  <p className="text-3xl font-bold">¥{summary.total_amount.toFixed(2)}</p>
                </div>
                <DollarSign className="w-8 h-8 text-purple-200" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm font-medium">平均成功率</p>
                  <p className="text-3xl font-bold">{summary.average_success_rate.toFixed(1)}%</p>
                </div>
                <TrendingUp className="w-8 h-8 text-orange-200" />
              </div>
            </div>
          </div>
        )}

        {/* 收款码列表 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden mb-8">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              收款码详细统计
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {qrCodeStats.map((qrStat) => (
              <div
                key={qrStat.qr_code_id}
                className={`border rounded-lg p-6 cursor-pointer transition-all ${
                  selectedQRCode === qrStat.qr_code_id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                onClick={() => handleSelectQRCode(qrStat.qr_code_id)}
              >
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {qrStat.qr_code_name}
                  </h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    qrStat.success_rate >= 90
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                      : qrStat.success_rate >= 80
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'
                  }`}>
                    {qrStat.success_rate.toFixed(1)}%
                  </span>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">支付次数</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {qrStat.total_payments} 次
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">收入金额</span>
                    <span className="text-sm font-medium text-green-600 dark:text-green-400">
                      ¥{qrStat.total_amount.toFixed(2)}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">最后使用</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {qrStat.last_used
                        ? new Date(qrStat.last_used).toLocaleDateString()
                        : '从未使用'}
                    </span>
                  </div>
                </div>

                {selectedQRCode === qrStat.qr_code_id && (
                  <div className="mt-4 p-2 bg-blue-100 dark:bg-blue-900/30 rounded text-center">
                    <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                      已选中 - 查看下方详细记录
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 选中收款码的付费记录 */}
        {selectedQRCode && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {qrCodeStats.find(q => q.qr_code_id === selectedQRCode)?.qr_code_name} - 付费记录
              </h3>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      订单信息
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      用户
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      金额
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      创建时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      支付时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      备注
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {paymentRecords.map((record) => (
                    <tr key={record.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {record.order_no}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {record.username}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-semibold text-green-600 dark:text-green-400">
                          ¥{record.amount.toFixed(2)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadge status={record.payment_status} />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {new Date(record.created_at).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {record.paid_at
                          ? new Date(record.paid_at).toLocaleString()
                          : '-'}
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-600 dark:text-gray-400 max-w-xs truncate">
                          {record.user_note || '-'}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {paymentRecords.length === 0 && (
              <div className="text-center py-12">
                <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  该收款码暂无付费记录
                </p>
              </div>
            )}

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    第 {currentPage} 页，共 {totalPages} 页
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      上一页
                    </button>
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      下一页
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default QRCodeAnalyticsPage
