/**
 * 管理员付款确认页面
 */

import React, { useState, useEffect } from 'react'
import { CheckCircle, XCircle, Clock, User, CreditCard, MessageSquare, RefreshCw } from 'lucide-react'
import { api } from '../../services/api'

interface PendingPayment {
  order_no: string
  user_id: number
  username: string
  product_type: string
  amount: number
  payment_method: string
  payment_reference: string
  submitted_at: string
  user_note?: string
  expires_at: string
}

export const PaymentConfirmPage: React.FC = () => {
  const [pendingPayments, setPendingPayments] = useState<PendingPayment[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<PendingPayment | null>(null)
  const [adminNote, setAdminNote] = useState('')
  const [rejectReason, setRejectReason] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  const productNames = {
    monthly: '月卡会员',
    quarterly: '季卡会员',
    yearly: '年卡会员'
  }

  const paymentMethodNames = {
    wechat: '微信支付',
    alipay: '支付宝'
  }

  useEffect(() => {
    fetchPendingPayments()
  }, [])

  const fetchPendingPayments = async () => {
    try {
      const response = await api.get('/api/v1/personal-payment/pending')
      setPendingPayments(response.data)
    } catch (error) {
      console.error('获取待确认付款失败:', error)
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  const handleRefresh = () => {
    setIsRefreshing(true)
    fetchPendingPayments()
  }

  const handleConfirmPayment = async (orderNo: string) => {
    setIsProcessing(true)
    try {
      const response = await api.post('/api/v1/personal-payment/confirm', {
        order_no: orderNo,
        admin_note: adminNote
      })

      if (response.data.success) {
        alert('付款确认成功！激活码已生成。')
        setSelectedPayment(null)
        setAdminNote('')
        fetchPendingPayments()
      } else {
        alert('付款确认失败')
      }
    } catch (error: any) {
      console.error('确认付款失败:', error)
      alert(error.response?.data?.detail || '确认付款失败')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRejectPayment = async (orderNo: string) => {
    if (!rejectReason.trim()) {
      alert('请填写拒绝原因')
      return
    }

    setIsProcessing(true)
    try {
      const response = await api.post('/api/v1/personal-payment/reject', {
        order_no: orderNo,
        reason: rejectReason
      })

      if (response.data.success) {
        alert('付款已拒绝')
        setSelectedPayment(null)
        setRejectReason('')
        fetchPendingPayments()
      } else {
        alert('拒绝付款失败')
      }
    } catch (error: any) {
      console.error('拒绝付款失败:', error)
      alert(error.response?.data?.detail || '拒绝付款失败')
    } finally {
      setIsProcessing(false)
    }
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <CreditCard className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                付款确认管理
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                确认用户提交的付款并发放激活码
              </p>
            </div>
          </div>
          
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            刷新
          </button>
        </div>

        {/* 统计信息 */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                <Clock className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">待确认付款</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {pendingPayments.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">总金额</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ¥{pendingPayments.reduce((sum, p) => sum + p.amount, 0).toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">涉及用户</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {new Set(pendingPayments.map(p => p.user_id)).size}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 待确认付款列表 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              待确认付款列表
            </h2>
          </div>

          {pendingPayments.length === 0 ? (
            <div className="p-8 text-center">
              <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">暂无待确认的付款</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {pendingPayments.map((payment) => (
                <div key={payment.order_no} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-3">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-gray-400" />
                          <span className="font-medium text-gray-900 dark:text-white">
                            {payment.username}
                          </span>
                        </div>
                        <div className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded-full">
                          {productNames[payment.product_type as keyof typeof productNames]}
                        </div>
                        <div className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full">
                          {paymentMethodNames[payment.payment_method as keyof typeof paymentMethodNames]}
                        </div>
                      </div>

                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600 dark:text-gray-400">订单号: {payment.order_no}</p>
                          <p className="text-gray-600 dark:text-gray-400">付款备注: {payment.payment_reference}</p>
                          <p className="text-gray-600 dark:text-gray-400">提交时间: {formatDateTime(payment.submitted_at)}</p>
                        </div>
                        <div>
                          <p className="text-lg font-bold text-red-500">¥{payment.amount}</p>
                          {payment.user_note && (
                            <p className="text-gray-600 dark:text-gray-400 mt-1">
                              用户备注: {payment.user_note}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2 ml-4">
                      <button
                        onClick={() => setSelectedPayment(payment)}
                        className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors"
                      >
                        确认
                      </button>
                      <button
                        onClick={() => {
                          setSelectedPayment(payment)
                          setRejectReason('')
                        }}
                        className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors"
                      >
                        拒绝
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 确认/拒绝模态框 */}
        {selectedPayment && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {rejectReason !== undefined ? '拒绝付款' : '确认付款'}
                </h3>

                <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <p className="text-sm text-gray-600 dark:text-gray-400">订单号: {selectedPayment.order_no}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">用户: {selectedPayment.username}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">金额: ¥{selectedPayment.amount}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">产品: {productNames[selectedPayment.product_type as keyof typeof productNames]}</p>
                </div>

                {rejectReason !== undefined ? (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      拒绝原因 *
                    </label>
                    <textarea
                      value={rejectReason}
                      onChange={(e) => setRejectReason(e.target.value)}
                      placeholder="请说明拒绝原因..."
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      rows={3}
                      required
                    />
                  </div>
                ) : (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      管理员备注（可选）
                    </label>
                    <textarea
                      value={adminNote}
                      onChange={(e) => setAdminNote(e.target.value)}
                      placeholder="如有需要，请填写备注..."
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      rows={3}
                    />
                  </div>
                )}

                <div className="flex gap-3">
                  <button
                    onClick={() => setSelectedPayment(null)}
                    className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={() => {
                      if (rejectReason !== undefined) {
                        handleRejectPayment(selectedPayment.order_no)
                      } else {
                        handleConfirmPayment(selectedPayment.order_no)
                      }
                    }}
                    disabled={isProcessing || (rejectReason !== undefined && !rejectReason.trim())}
                    className={`flex-1 px-4 py-2 text-white rounded-lg transition-colors disabled:opacity-50 ${
                      rejectReason !== undefined
                        ? 'bg-red-600 hover:bg-red-700'
                        : 'bg-green-600 hover:bg-green-700'
                    }`}
                  >
                    {isProcessing ? '处理中...' : (rejectReason !== undefined ? '确认拒绝' : '确认付款')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
