/**
 * 管理员收款码管理页面
 */

import React, { useState, useEffect } from 'react'
import { QrCode, Plus, Trash2, Edit, Eye, RefreshCw } from 'lucide-react'

interface QRCodeItem {
  id: string
  type: 'wechat' | 'alipay'
  qr_code_url: string
  account_name: string
  account_info: string
  is_active: boolean
}

export const PaymentQRCodeManagePage: React.FC = () => {
  const [qrCodes, setQrCodes] = useState<QRCodeItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // 模拟数据 - 实际应该从API获取
  useEffect(() => {
    // 模拟从配置中加载收款码
    const mockQRCodes: QRCodeItem[] = [
      {
        id: '1',
        type: 'wechat',
        qr_code_url: 'http://localhost:3000/images/jiazi-weixin.jpg',
        account_name: '甲子',
        account_info: '甲子微信',
        is_active: true
      },
      {
        id: '2',
        type: 'wechat',
        qr_code_url: 'http://localhost:3000/images/xueqian-weixin.jpg',
        account_name: '雪千',
        account_info: '雪千微信',
        is_active: true
      },
      {
        id: '3',
        type: 'alipay',
        qr_code_url: 'http://localhost:3000/images/jiazi-zhifubao.jpg',
        account_name: '甲子',
        account_info: '甲子支付宝',
        is_active: true
      },
      {
        id: '4',
        type: 'alipay',
        qr_code_url: 'http://localhost:3000/images/xueqian-zhifubao.jpg',
        account_name: '雪千',
        account_info: '雪千支付宝',
        is_active: true
      }
    ]
    
    setTimeout(() => {
      setQrCodes(mockQRCodes)
      setIsLoading(false)
    }, 1000)
  }, [])

  const getTypeIcon = (type: 'wechat' | 'alipay') => {
    if (type === 'wechat') {
      return (
        <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
          <span className="text-green-600 dark:text-green-400 font-bold text-xs">微信</span>
        </div>
      )
    } else {
      return (
        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
          <span className="text-blue-600 dark:text-blue-400 font-bold text-xs">支付宝</span>
        </div>
      )
    }
  }

  const getTypeName = (type: 'wechat' | 'alipay') => {
    return type === 'wechat' ? '微信支付' : '支付宝'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <QrCode className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                收款码管理
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                管理微信和支付宝收款码
              </p>
            </div>
          </div>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            <RefreshCw className="w-4 h-4" />
            刷新配置
          </button>
        </div>

        {/* 统计信息 */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <span className="text-green-600 dark:text-green-400 font-bold text-xs">微信</span>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">微信收款码</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {qrCodes.filter(qr => qr.type === 'wechat' && qr.is_active).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <span className="text-blue-600 dark:text-blue-400 font-bold text-xs">支付宝</span>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">支付宝收款码</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {qrCodes.filter(qr => qr.type === 'alipay' && qr.is_active).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                <QrCode className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">总收款码</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {qrCodes.filter(qr => qr.is_active).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                <Eye className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">启用状态</p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                  正常
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 收款码列表 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              收款码列表
            </h2>
          </div>

          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {qrCodes.map((qrCode) => (
              <div key={qrCode.id} className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {getTypeIcon(qrCode.type)}
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {qrCode.account_name}
                        </h3>
                        <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full">
                          {getTypeName(qrCode.type)}
                        </span>
                        {qrCode.is_active && (
                          <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-xs rounded-full">
                            启用
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {qrCode.account_info}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                        {qrCode.qr_code_url}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {/* 预览收款码 */}
                    <div className="w-16 h-16 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                      <img
                        src={qrCode.qr_code_url}
                        alt={`${qrCode.account_name}的收款码`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = 'https://via.placeholder.com/64x64?text=QR'
                        }}
                      />
                    </div>

                    <div className="flex gap-1 ml-4">
                      <button className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 配置说明 */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">
            配置说明
          </h3>
          <div className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
            <p>• 收款码配置在 <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">.env</code> 文件中</p>
            <p>• 支持多个收款码，系统会随机选择一个显示给用户</p>
            <p>• 图片文件应放在 <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">webapp/frontend/public/images/</code> 目录下</p>
            <p>• 修改配置后需要重启后端服务</p>
          </div>
        </div>
      </div>
    </div>
  )
}
