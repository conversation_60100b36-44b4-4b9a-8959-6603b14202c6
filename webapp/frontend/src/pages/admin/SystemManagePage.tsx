/**
 * 系统管理页面
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Settings,
  Key,
  QrCode,
  Database,
  Shield,
  Bell,
  Globe,
  Palette,
  Server,
  RefreshCw,
  Save,
  Plus,
  Trash2,
  Edit,
  Eye,
  Copy,
  Download,
  Upload,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react'
import { api } from '../../services/api'

// 接口定义
interface ActivationCode {
  id: number
  code: string
  code_type: string
  status: string
  created_at: string
  used_at?: string
  expires_at?: string
  used_by_username?: string
}

interface QRCodeConfig {
  id: string
  type: 'wechat' | 'alipay'
  qr_code_url: string
  account_name: string
  account_info: string
  is_active: boolean
}

interface SystemSettings {
  site_name: string
  site_description: string
  maintenance_mode: boolean
  registration_enabled: boolean
  email_verification_required: boolean
  max_users: number
  session_timeout: number
}

const SystemManagePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('activation-codes')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  // 激活码相关状态
  const [activationCodes, setActivationCodes] = useState<ActivationCode[]>([])
  const [newCodeType, setNewCodeType] = useState('monthly')
  const [newCodeCount, setNewCodeCount] = useState(1)
  
  // 收款码相关状态
  const [qrCodes, setQrCodes] = useState<QRCodeConfig[]>([])
  const [editingQR, setEditingQR] = useState<string | null>(null)
  
  // 系统设置状态
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    site_name: 'Coin-Analyze',
    site_description: '专业加密货币数据分析平台',
    maintenance_mode: false,
    registration_enabled: true,
    email_verification_required: false,
    max_users: 10000,
    session_timeout: 1440
  })

  // 获取激活码列表
  const fetchActivationCodes = async () => {
    try {
      // 这里需要实现激活码API
      // const response = await api.get('/api/v1/admin/activation-codes')
      // setActivationCodes(response.data)
      
      // 模拟数据
      setActivationCodes([
        {
          id: 1,
          code: 'MONTHLY-ABC123',
          code_type: 'monthly',
          status: 'unused',
          created_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          code: 'YEARLY-XYZ789',
          code_type: 'yearly',
          status: 'used',
          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          used_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          used_by_username: 'test_user'
        }
      ])
    } catch (error) {
      console.error('获取激活码失败:', error)
      setError('获取激活码失败')
    }
  }

  // 获取收款码配置
  const fetchQRCodes = async () => {
    try {
      // 这里需要实现收款码API
      // const response = await api.get('/api/v1/admin/qr-codes')
      // setQrCodes(response.data)
      
      // 模拟数据
      setQrCodes([
        {
          id: '1',
          type: 'wechat',
          qr_code_url: '/images/wechat-qr.png',
          account_name: '微信收款',
          account_info: 'wxid_example',
          is_active: true
        },
        {
          id: '2',
          type: 'alipay',
          qr_code_url: '/images/alipay-qr.png',
          account_name: '支付宝收款',
          account_info: '<EMAIL>',
          is_active: true
        }
      ])
    } catch (error) {
      console.error('获取收款码配置失败:', error)
      setError('获取收款码配置失败')
    }
  }

  // 生成激活码
  const handleGenerateActivationCodes = async () => {
    try {
      setLoading(true)
      // const response = await api.post('/api/v1/admin/activation-codes/generate', {
      //   code_type: newCodeType,
      //   count: newCodeCount
      // })
      
      // 模拟成功
      setSuccess(`成功生成 ${newCodeCount} 个 ${newCodeType} 激活码`)
      await fetchActivationCodes()
      setNewCodeCount(1)
    } catch (error) {
      console.error('生成激活码失败:', error)
      setError('生成激活码失败')
    } finally {
      setLoading(false)
    }
  }

  // 保存系统设置
  const handleSaveSettings = async () => {
    try {
      setLoading(true)
      // await api.put('/api/v1/admin/settings', systemSettings)
      setSuccess('系统设置保存成功')
    } catch (error) {
      console.error('保存系统设置失败:', error)
      setError('保存系统设置失败')
    } finally {
      setLoading(false)
    }
  }

  // 复制激活码
  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code)
    setSuccess('激活码已复制到剪贴板')
  }

  useEffect(() => {
    if (activeTab === 'activation-codes') {
      fetchActivationCodes()
    } else if (activeTab === 'qr-codes') {
      fetchQRCodes()
    }
  }, [activeTab])

  // 清除消息
  useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null)
        setError(null)
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [success, error])

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                系统管理
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                管理激活码、收款码配置和系统设置
              </p>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 px-3 py-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 rounded-lg">
                <Server className="w-4 h-4" />
                <span className="text-sm font-medium">系统正常</span>
              </div>
            </div>
          </div>
        </div>

        {/* 消息提示 */}
        {(success || error) && (
          <div className={`mb-6 p-4 rounded-lg border ${
            success 
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
          }`}>
            <div className="flex items-center gap-2">
              {success ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-500" />
              )}
              <span className={success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                {success || error}
              </span>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'activation-codes', name: '激活码管理', icon: Key },
                { id: 'qr-codes', name: '收款码配置', icon: QrCode },
                { id: 'settings', name: '系统设置', icon: Settings },
                { id: 'database', name: '数据库管理', icon: Database }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-3 px-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'activation-codes' && (
            <div className="space-y-8">
              {/* 生成激活码 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  生成激活码
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      激活码类型
                    </label>
                    <select
                      value={newCodeType}
                      onChange={(e) => setNewCodeType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    >
                      <option value="monthly">月卡会员</option>
                      <option value="quarterly">季卡会员</option>
                      <option value="yearly">年卡会员</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      生成数量
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="100"
                      value={newCodeCount}
                      onChange={(e) => setNewCodeCount(parseInt(e.target.value) || 1)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div className="flex items-end">
                    <button
                      onClick={handleGenerateActivationCodes}
                      disabled={loading}
                      className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
                    >
                      <Plus className="w-4 h-4" />
                      生成激活码
                    </button>
                  </div>
                </div>
              </div>

              {/* 激活码列表 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    激活码列表
                  </h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          激活码
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          类型
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          状态
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          创建时间
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          使用信息
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {activationCodes.map((code) => (
                        <tr key={code.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              <code className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                {code.code}
                              </code>
                              <button
                                onClick={() => handleCopyCode(code.code)}
                                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                title="复制激活码"
                              >
                                <Copy className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              code.code_type === 'yearly' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200' :
                              code.code_type === 'quarterly' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200' :
                              'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                            }`}>
                              {code.code_type === 'yearly' ? '年卡' : code.code_type === 'quarterly' ? '季卡' : '月卡'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                              code.status === 'used' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                              'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                            }`}>
                              {code.status === 'used' ? (
                                <>
                                  <CheckCircle className="w-3 h-3" />
                                  已使用
                                </>
                              ) : (
                                <>
                                  <Key className="w-3 h-3" />
                                  未使用
                                </>
                              )}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {new Date(code.created_at).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {code.status === 'used' ? (
                              <div>
                                <div>用户: {code.used_by_username}</div>
                                <div>时间: {code.used_at ? new Date(code.used_at).toLocaleDateString() : '-'}</div>
                              </div>
                            ) : (
                              <div>
                                过期: {code.expires_at ? new Date(code.expires_at).toLocaleDateString() : '永不过期'}
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center gap-2">
                              <button
                                className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                                title="查看详情"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                              {code.status === 'unused' && (
                                <button
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  title="删除激活码"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'qr-codes' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="text-center py-12">
                <QrCode className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  收款码配置
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  收款码配置功能正在开发中...
                </p>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  配置收款码
                </button>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-8">
              {/* 基本设置 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  基本设置
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      网站名称
                    </label>
                    <input
                      type="text"
                      value={systemSettings.site_name}
                      onChange={(e) => setSystemSettings({...systemSettings, site_name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      最大用户数
                    </label>
                    <input
                      type="number"
                      value={systemSettings.max_users}
                      onChange={(e) => setSystemSettings({...systemSettings, max_users: parseInt(e.target.value) || 0})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      网站描述
                    </label>
                    <textarea
                      value={systemSettings.site_description}
                      onChange={(e) => setSystemSettings({...systemSettings, site_description: e.target.value})}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              {/* 功能开关 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  功能开关
                </h3>
                <div className="space-y-4">
                  {[
                    { key: 'maintenance_mode', label: '维护模式', description: '开启后网站将显示维护页面' },
                    { key: 'registration_enabled', label: '允许注册', description: '是否允许新用户注册' },
                    { key: 'email_verification_required', label: '邮箱验证', description: '注册时是否需要邮箱验证' }
                  ].map((setting) => (
                    <div key={setting.key} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {setting.label}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {setting.description}
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={systemSettings[setting.key as keyof SystemSettings] as boolean}
                          onChange={(e) => setSystemSettings({
                            ...systemSettings,
                            [setting.key]: e.target.checked
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* 保存按钮 */}
              <div className="flex justify-end">
                <button
                  onClick={handleSaveSettings}
                  disabled={loading}
                  className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  <Save className="w-4 h-4" />
                  保存设置
                </button>
              </div>
            </div>
          )}

          {activeTab === 'database' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="text-center py-12">
                <Database className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  数据库管理
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  数据库备份、恢复和维护功能正在开发中...
                </p>
                <div className="flex justify-center gap-3">
                  <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    备份数据库
                  </button>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    数据维护
                  </button>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default SystemManagePage
