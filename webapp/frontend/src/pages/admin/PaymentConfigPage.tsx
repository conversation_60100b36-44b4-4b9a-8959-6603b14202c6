/**
 * 管理员支付配置页面
 */

import React from 'react'
import { CreditCard, Settings, AlertTriangle, CheckCircle } from 'lucide-react'
import { PaymentConfigStatus } from '../../components/payment/PaymentConfigStatus'

export const PaymentConfigPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <CreditCard className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              支付配置管理
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            配置和管理微信支付、支付宝等支付方式
          </p>
        </div>

        {/* 支付配置状态 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="flex items-center gap-2 mb-4">
            <Settings className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              当前配置状态
            </h2>
          </div>
          
          <PaymentConfigStatus />
        </div>

        {/* 支付方式说明 */}
        <div className="grid md:grid-cols-2 gap-6 mb-6">
          {/* 微信支付 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <span className="text-green-600 dark:text-green-400 font-bold text-sm">微信</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">微信支付</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Native扫码支付</p>
              </div>
            </div>
            
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-gray-700 dark:text-gray-300">支持扫码支付</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-gray-700 dark:text-gray-300">实时到账</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-gray-700 dark:text-gray-300">支持退款</span>
              </div>
            </div>

            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-xs text-gray-600 dark:text-gray-400">
                需要配置：APP ID、商户号、API密钥、证书文件
              </p>
            </div>
          </div>

          {/* 支付宝 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-400 font-bold text-sm">支付宝</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">支付宝</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">当面付扫码支付</p>
              </div>
            </div>
            
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-gray-700 dark:text-gray-300">支持扫码支付</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-gray-700 dark:text-gray-300">实时到账</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-gray-700 dark:text-gray-300">支持退款</span>
              </div>
            </div>

            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-xs text-gray-600 dark:text-gray-400">
                需要配置：APP ID、应用私钥、支付宝公钥
              </p>
            </div>
          </div>
        </div>

        {/* 安全提醒 */}
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-amber-500 mt-0.5" />
            <div>
              <h3 className="font-semibold text-amber-900 dark:text-amber-100 mb-2">
                安全提醒
              </h3>
              <div className="space-y-2 text-sm text-amber-800 dark:text-amber-200">
                <p>• 请妥善保管支付密钥和证书文件，不要泄露给他人</p>
                <p>• 生产环境必须使用HTTPS协议</p>
                <p>• 定期检查和更新支付配置</p>
                <p>• 建议先在沙箱环境测试支付流程</p>
                <p>• 确保服务器防火墙允许支付平台回调</p>
              </div>
            </div>
          </div>
        </div>

        {/* 快速链接 */}
        <div className="mt-6 grid md:grid-cols-3 gap-4">
          <a
            href="https://pay.weixin.qq.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="block p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
          >
            <div className="text-center">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                <span className="text-green-600 dark:text-green-400 font-bold text-xs">微信</span>
              </div>
              <h4 className="font-medium text-gray-900 dark:text-white text-sm">微信商户平台</h4>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">配置微信支付</p>
            </div>
          </a>

          <a
            href="https://open.alipay.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="block p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
          >
            <div className="text-center">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-2">
                <span className="text-blue-600 dark:text-blue-400 font-bold text-xs">支付宝</span>
              </div>
              <h4 className="font-medium text-gray-900 dark:text-white text-sm">支付宝开放平台</h4>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">配置支付宝支付</p>
            </div>
          </a>

          <a
            href="/docs/PAYMENT_SETUP.md"
            target="_blank"
            rel="noopener noreferrer"
            className="block p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
          >
            <div className="text-center">
              <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Settings className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              </div>
              <h4 className="font-medium text-gray-900 dark:text-white text-sm">配置文档</h4>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">详细配置指南</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  )
}
