/**
 * 访问统计分析页面
 */

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Users,
  Eye,
  UserCheck,
  Crown,
  Calendar,
  Clock,
  Globe,
  Smartphone,
  RefreshCw,
  Download,
  Filter,
  Search,
  TrendingUp,
  Activity
} from 'lucide-react'
import { api } from '../../services/api'

// 接口定义
interface VisitorStats {
  total_visitors: number
  unique_visitors: number
  registered_users: number
  premium_users: number
  today_visitors: number
  this_week_visitors: number
  this_month_visitors: number
}

interface VisitorRecord {
  id: number
  user_id?: number
  username?: string
  email?: string
  ip_address: string
  user_agent: string
  page_path: string
  visit_time: string
  session_duration?: number
  is_registered: boolean
  is_premium: boolean
}

interface QRCodeStats {
  qr_code_id: string
  qr_code_name: string
  total_payments: number
  total_amount: number
  success_rate: number
  last_used?: string
}

interface AnalyticsData {
  visitor_stats: VisitorStats
  recent_visitors: VisitorRecord[]
  qr_code_stats: QRCodeStats[]
  total_pages: number
  current_page: number
}

const VisitorAnalyticsPage: React.FC = () => {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // 筛选状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [searchTerm, setSearchTerm] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  // 获取访问统计数据
  const fetchAnalyticsData = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        page_size: pageSize.toString()
      })
      
      if (searchTerm) params.append('search', searchTerm)
      if (startDate) params.append('start_date', startDate)
      if (endDate) params.append('end_date', endDate)

      const response = await api.get(`/api/v1/admin/analytics/visitors?${params}`)
      setData(response.data)
      setError(null)
    } catch (error) {
      console.error('获取访问统计失败:', error)
      setError('获取访问统计失败')
      // 数据库查询失败时设置空数据
      setData({
        visitor_stats: {
          total_visitors: 0,
          unique_visitors: 0,
          registered_users: 0,
          premium_users: 0,
          today_visitors: 0,
          this_week_visitors: 0,
          this_month_visitors: 0
        },
        recent_visitors: [],
        qr_code_stats: [],
        total_pages: 0,
        current_page: 1
      })
    }
  }

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchAnalyticsData()
    setRefreshing(false)
  }

  // 重置筛选
  const handleResetFilters = () => {
    setSearchTerm('')
    setStartDate('')
    setEndDate('')
    setCurrentPage(1)
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await fetchAnalyticsData()
      setLoading(false)
    }
    loadData()
  }, [currentPage, searchTerm, startDate, endDate])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载访问统计中...</p>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <Activity className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            数据加载失败
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error || '无法获取访问统计数据'}
          </p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                访问统计分析
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                查看网站访问数据、用户行为分析和收款码使用统计
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                刷新
              </button>
              <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <Download className="w-4 h-4" />
                导出
              </button>
            </div>
          </div>
        </div>

        {/* 访问统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm font-medium">总访问量</p>
                <p className="text-3xl font-bold">{data.visitor_stats.total_visitors.toLocaleString()}</p>
                <p className="text-blue-100 text-sm">累计访问</p>
              </div>
              <div className="p-3 bg-blue-400/30 rounded-lg">
                <Eye className="w-8 h-8" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm font-medium">注册用户</p>
                <p className="text-3xl font-bold">{data.visitor_stats.registered_users.toLocaleString()}</p>
                <p className="text-green-100 text-sm">已注册</p>
              </div>
              <div className="p-3 bg-green-400/30 rounded-lg">
                <UserCheck className="w-8 h-8" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm font-medium">会员用户</p>
                <p className="text-3xl font-bold">{data.visitor_stats.premium_users.toLocaleString()}</p>
                <p className="text-purple-100 text-sm">付费会员</p>
              </div>
              <div className="p-3 bg-purple-400/30 rounded-lg">
                <Crown className="w-8 h-8" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm font-medium">今日访问</p>
                <p className="text-3xl font-bold">{data.visitor_stats.today_visitors.toLocaleString()}</p>
                <p className="text-orange-100 text-sm">新增访问</p>
              </div>
              <div className="p-3 bg-orange-400/30 rounded-lg">
                <TrendingUp className="w-8 h-8" />
              </div>
            </div>
          </div>
        </div>

        {/* 时间段统计 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center gap-3 mb-4">
              <Calendar className="w-6 h-6 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">今日访问</h3>
            </div>
            <p className="text-3xl font-bold text-blue-600 mb-2">{data.visitor_stats.today_visitors}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">比昨日 +12%</p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center gap-3 mb-4">
              <Clock className="w-6 h-6 text-green-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">本周访问</h3>
            </div>
            <p className="text-3xl font-bold text-green-600 mb-2">{data.visitor_stats.this_week_visitors}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">比上周 +8%</p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center gap-3 mb-4">
              <Globe className="w-6 h-6 text-purple-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">本月访问</h3>
            </div>
            <p className="text-3xl font-bold text-purple-600 mb-2">{data.visitor_stats.this_month_visitors}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">比上月 +15%</p>
          </div>
        </div>

        {/* 筛选和搜索 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                搜索用户
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="用户名或邮箱"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                开始日期
              </label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                结束日期
              </label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div className="flex items-end">
              <button
                onClick={handleResetFilters}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                重置筛选
              </button>
            </div>
          </div>
        </div>

        {/* 最近访问记录 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden mb-8">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              最近访问记录 ({data.recent_visitors.length})
            </h3>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    用户信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    访问页面
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    IP地址
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    设备信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    访问时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    会话时长
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    状态
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {data.recent_visitors.map((visitor) => (
                  <tr key={visitor.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                          <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {visitor.username || '匿名用户'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {visitor.email || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{visitor.page_path}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">{visitor.ip_address}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                        {visitor.user_agent.includes('Mobile') ? (
                          <div className="flex items-center gap-1">
                            <Smartphone className="w-4 h-4" />
                            移动设备
                          </div>
                        ) : (
                          <div className="flex items-center gap-1">
                            <Globe className="w-4 h-4" />
                            桌面设备
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(visitor.visit_time).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {visitor.session_duration ? `${Math.floor(visitor.session_duration / 60)}分钟` : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex gap-1">
                        {visitor.is_registered && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200">
                            已注册
                          </span>
                        )}
                        {visitor.is_premium && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-200">
                            会员
                          </span>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {data.recent_visitors.length === 0 && (
            <div className="text-center py-12">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                {searchTerm || startDate || endDate
                  ? '没有找到符合条件的访问记录'
                  : '暂无访问记录'}
              </p>
            </div>
          )}
        </div>

        {/* 收款码统计 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              收款码使用统计
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {data.qr_code_stats.map((qrStat) => (
              <div key={qrStat.qr_code_id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {qrStat.qr_code_name}
                  </h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    qrStat.success_rate >= 90
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'
                      : qrStat.success_rate >= 80
                      ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'
                  }`}>
                    {qrStat.success_rate.toFixed(1)}% 成功率
                  </span>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">总支付次数</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {qrStat.total_payments} 次
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">总收入金额</span>
                    <span className="text-sm font-medium text-green-600 dark:text-green-400">
                      ¥{qrStat.total_amount.toFixed(2)}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">最后使用</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {qrStat.last_used
                        ? new Date(qrStat.last_used).toLocaleDateString()
                        : '从未使用'}
                    </span>
                  </div>
                </div>

                <button className="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                  查看详细记录
                </button>
              </div>
            ))}
          </div>

          {data.qr_code_stats.length === 0 && (
            <div className="text-center py-12">
              <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">暂无收款码使用记录</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default VisitorAnalyticsPage
