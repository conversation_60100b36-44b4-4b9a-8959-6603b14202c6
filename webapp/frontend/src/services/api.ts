import axios, { AxiosInstance, AxiosResponse } from 'axios'
import toast from 'react-hot-toast'

// API配置
const isDev = import.meta.env.DEV
// 在开发环境中直接使用后端地址，避免代理问题
// 在生产环境中使用完整的API URL
const API_BASE_URL = isDev ? 'http://localhost:8000' : (import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000')
const API_TIMEOUT = 10000

console.log('🔧 API配置:', {
  isDev,
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  env: import.meta.env.VITE_API_BASE_URL,
  currentOrigin: typeof window !== 'undefined' ? window.location.origin : 'server'
})

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token等
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('❌ 请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`✅ API响应: ${response.config.url} - ${response.status}`)
    return response
  },
  (error) => {
    console.error('❌ API错误:', error)
    
    // 处理不同类型的错误
    if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
      console.warn('🔌 后端服务器未启动，使用模拟数据')
      // 不显示错误提示，静默处理
      return Promise.reject(error)
    }
    
    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          toast.error('认证失败，请重新登录')
          // 清除token并重定向到登录页
          localStorage.removeItem('auth_token')
          break
        case 403:
          toast.error('权限不足')
          break
        case 404:
          console.warn('API接口不存在:', error.config?.url)
          break
        case 500:
          toast.error('服务器内部错误')
          break
        default:
          toast.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      // 请求发出但没有收到响应
      toast.error('网络连接失败，请检查网络')
    } else {
      // 其他错误
      toast.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
  timestamp?: string
}

// 通用API方法
export const apiClient = {
  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> =>
    api.get(url, { params }).then(res => res.data),
    
  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> =>
    api.post(url, data).then(res => res.data),
    
  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> =>
    api.put(url, data).then(res => res.data),
    
  delete: <T = any>(url: string): Promise<ApiResponse<T>> =>
    api.delete(url).then(res => res.data),
}

// 健康检查
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    console.log('🏥 开始API健康检查，URL:', `${API_BASE_URL}/health`)
    const response = await api.get('/health', { timeout: 3000 })
    console.log('🏥 健康检查成功，响应:', response.data)
    return true
  } catch (error) {
    console.error('🏥 API健康检查失败:', error)
    console.error('错误详情:', {
      message: error.message,
      code: error.code,
      response: error.response?.data,
      status: error.response?.status,
      url: error.config?.url
    })
    return false
  }
}

export { api }
export default api
