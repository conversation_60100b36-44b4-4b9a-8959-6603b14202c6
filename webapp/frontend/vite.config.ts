import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// 自定义图片优化插件
const imageOptimizationPlugin = () => {
  return {
    name: 'image-optimization',
    generateBundle(options: any, bundle: any) {
      // 在构建时提示图片优化
      console.log('💡 图片优化提示:')
      console.log('   运行 node scripts/optimize-images.js 来优化图片')
      console.log('   或访问 https://tinypng.com/ 手动压缩')
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
  plugins: [
    react(),
    imageOptimizationPlugin()
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@services': path.resolve(__dirname, './src/services'),
      '@stores': path.resolve(__dirname, './src/stores'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@types': path.resolve(__dirname, './src/types'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@contexts': path.resolve(__dirname, './src/contexts')
    }
  },
  server: {
    port: parseInt(env.VITE_DEV_PORT) || 5173,
    host: true,
    strictPort: false, // 如果端口被占用则自动选择其他端口
    // 开发环境HTTPS配置 (可选)
    // https: env.VITE_ENABLE_HTTPS === 'true' ? {
    //   key: fs.readFileSync('../nginx/ssl/dev/localhost.key'),
    //   cert: fs.readFileSync('../nginx/ssl/dev/localhost.crt'),
    // } : undefined,
    proxy: {
      // 代理所有API请求到后端
      '/api': {
        target: env.VITE_API_BASE_URL || 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/health': {
        target: env.VITE_API_BASE_URL || 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/predict': {
        target: env.VITE_API_BASE_URL || 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/backtest': {
        target: env.VITE_API_BASE_URL || 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/market': {
        target: env.VITE_API_BASE_URL || 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/ws': {
        target: env.VITE_WS_BASE_URL || 'ws://localhost:8000',
        ws: true,
        changeOrigin: true
      }
    }
  },
  preview: {
    port: parseInt(env.VITE_PREVIEW_PORT) || 3001,
    host: true,
    strictPort: true
  },
  build: {
    outDir: 'dist',
    sourcemap: false, // 生产环境关闭sourcemap
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // 移除console.log
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          // 核心框架
          'react-vendor': ['react', 'react-dom'],
          'router': ['react-router-dom'],

          // 状态管理和数据获取
          'state': ['zustand', 'react-query'],

          // 图表库（按需加载）
          'charts-core': ['chart.js'],
          'charts-react': ['react-chartjs-2'],
          'charts-advanced': ['recharts'],

          // UI和动画
          'ui-core': ['framer-motion', 'lucide-react'],
          'ui-utils': ['clsx', 'react-hot-toast'],

          // 工具库
          'utils': ['axios', 'date-fns', 'numeral']
        },
        // 文件名优化
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          const ext = info[info.length - 1]
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            return `media/[name]-[hash].${ext}`
          }
          if (/\.(png|jpe?g|gif|svg|webp|avif)(\?.*)?$/i.test(assetInfo.name)) {
            return `img/[name]-[hash].${ext}`
          }
          if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            return `fonts/[name]-[hash].${ext}`
          }
          return `assets/[name]-[hash].${ext}`
        }
      }
    },
    // 压缩配置
    cssCodeSplit: true,
    assetsInlineLimit: 4096, // 小于4kb的资源内联
    chunkSizeWarningLimit: 1000
  },
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __DEV__: mode === 'development'
  }
  }
})
